const express = require('express');
const { query } = require('../config/database');
const { authenticateAdmin } = require('./auth');

const router = express.Router();

// Mock 解析数据
const explanations = [
  {
    explanation_id: 1,
    question_id: 1,
    explanation_content: "Paris is the capital and most populous city of France.",
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    explanation_id: 2,
    question_id: 2,
    explanation_content: "Paris is the capital and most populous city of France.",
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    explanation_id: 3,
    question_id: 3,
    explanation_content: "Paris, Lyon, and Marseille are all cities in France. Berlin is in Germany.",
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  }
];

// 获取题目解析
router.get('/question/:question_id', async (req, res) => {
  try {
    const questionId = parseInt(req.params.question_id);
    const explanation = explanations.find(exp => exp.question_id === questionId);

    if (!explanation) {
      return res.status(404).json({
        success: false,
        message: '解析不存在'
      });
    }

    res.json({
      success: true,
      data: explanation,
      message: '获取解析成功'
    });

  } catch (error) {
    console.error('获取解析错误:', error);
    res.status(500).json({
      success: false,
      message: '获取解析失败'
    });
  }
});

// 创建解析（管理员）
router.post('/', authenticateAdmin, async (req, res) => {
  try {
    const { question_id, explanation_content } = req.body;

    if (!question_id || !explanation_content) {
      return res.status(400).json({
        success: false,
        message: '题目ID和解析内容不能为空'
      });
    }

    const newExplanation = {
      explanation_id: explanations.length + 1,
      question_id: parseInt(question_id),
      explanation_content,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    explanations.push(newExplanation);

    res.json({
      success: true,
      data: newExplanation,
      message: '创建解析成功'
    });

  } catch (error) {
    console.error('创建解析错误:', error);
    res.status(500).json({
      success: false,
      message: '创建解析失败'
    });
  }
});

module.exports = router;
