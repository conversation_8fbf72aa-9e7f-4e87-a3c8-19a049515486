{"_from": "is-extendable@^0.1.0", "_id": "is-extendable@0.1.1", "_inBundle": false, "_integrity": "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==", "_location": "/is-extendable", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-extendable@^0.1.0", "name": "is-extendable", "escapedName": "is-extendable", "rawSpec": "^0.1.0", "saveSpec": null, "fetchSpec": "^0.1.0"}, "_requiredBy": ["/braces/extend-shallow", "/condense-newlines/extend-shallow", "/expand-brackets/extend-shallow", "/extglob/extend-shallow", "/fill-range/extend-shallow", "/object.omit", "/pretty/extend-shallow", "/set-value", "/set-value/extend-shallow", "/snapdragon/extend-shallow", "/svg-baker/extend-shallow", "/union-value"], "_resolved": "https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz", "_shasum": "62b110e289a471418e3ec36a617d472e301dfc89", "_spec": "is-extendable@^0.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\braces\\node_modules\\extend-shallow", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extendable/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Returns true if a value is any of the object types: array, regexp, plain object, function or date. This is useful for determining if a value can be extended, e.g. \"can the value have keys?\"", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-extendable", "keywords": ["array", "assign", "check", "date", "extend", "extensible", "function", "is", "object", "regex", "test"], "license": "MIT", "main": "index.js", "name": "is-extendable", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-extendable.git"}, "scripts": {"test": "mocha"}, "verbiage": {"related": {"list": ["isobject", "is-plain-object", "kind-of", "is-extendable", "is-equal-shallow", "extend-shallow", "assign-deep"]}}, "version": "0.1.1"}