{"_from": "ip-regex@^2.1.0", "_id": "ip-regex@2.1.0", "_inBundle": false, "_integrity": "sha512-58yWmlHpp7VYfcdTwMTvwMmqx/Elfxjd9RXTDyMsbL7lLWmhMylLEqiYVLKuLzOZqVgiWXD9MfR62Vv89VRxkw==", "_location": "/ip-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ip-regex@^2.1.0", "name": "ip-regex", "escapedName": "ip-regex", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/internal-ip/default-gateway", "/jest-environment-jsdom-fifteen/tough-cookie"], "_resolved": "https://registry.npmmirror.com/ip-regex/-/ip-regex-2.1.0.tgz", "_shasum": "fa78bf5d2e6913c911ce9f819ee5146bb6d844e9", "_spec": "ip-regex@^2.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\jest-environment-jsdom-fifteen\\node_modules\\tough-cookie", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/ip-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Regular expression for matching IP addresses (IPv4 & IPv6)", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/ip-regex#readme", "keywords": ["ip", "ipv6", "ipv4", "regex", "regexp", "re", "match", "test", "find", "text", "pattern", "internet", "protocol", "address", "validate"], "license": "MIT", "name": "ip-regex", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ip-regex.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0", "xo": {"esnext": true}}