# is-dotfile [![NPM version](https://img.shields.io/npm/v/is-dotfile.svg?style=flat)](https://www.npmjs.com/package/is-dotfile) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-dotfile.svg?style=flat)](https://npmjs.org/package/is-dotfile) [![NPM total downloads](https://img.shields.io/npm/dt/is-dotfile.svg?style=flat)](https://npmjs.org/package/is-dotfile) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-dotfile.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-dotfile)

> Return true if a file path is (or has) a dotfile. Returns false if the path is a dot directory.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save is-dotfile
```

## Usage

To be considered a dotfile, it must be the last filename in the path, like `.gitignore`. Otherwise it's a [dot directory](https://github.com/jonschlinkert/is-dotdir), like `.git/` and `.github/`.

```js
var isDotfile = require('is-dotfile');
```

**false**

All of the following return `false`:

```js
isDotfile('a/b/c.js');
isDotfile('/.git/foo');
isDotfile('a/b/c/.git/foo');
//=> false
```

**true**

All of the following return `true`:

```js
isDotfile('a/b/.gitignore');
isDotfile('.gitignore');
isDotfile('/.gitignore');
//=> true
```

## About

### Related projects

* [dotdir-regex](https://www.npmjs.com/package/dotdir-regex): Regex for matching dot-directories, like `.git/` | [homepage](https://github.com/regexps/dotdir-regex "Regex for matching dot-directories, like `.git/`")
* [dotfile-regex](https://www.npmjs.com/package/dotfile-regex): Regular expresson for matching dotfiles. | [homepage](https://github.com/regexps/dotfile-regex "Regular expresson for matching dotfiles.")
* [is-dotdir](https://www.npmjs.com/package/is-dotdir): Returns true if a path is a dot-directory. | [homepage](https://github.com/jonschlinkert/is-dotdir "Returns true if a path is a dot-directory.")
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 13 | [jonschlinkert](https://github.com/jonschlinkert) |
| 1 | [Lykathia](https://github.com/Lykathia) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on May 30, 2017._