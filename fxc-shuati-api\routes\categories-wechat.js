const express = require('express');
const router = express.Router();
const { query } = require('../config/database');

// 获取分类列表
router.get('/', async (req, res) => {
  try {
    const categoriesSql = `
      SELECT c.category_id, c.category_name, c.category_code, c.category_desc, 
             c.parent_id, c.sort_order,
             COUNT(q.question_id) as question_count
      FROM categories c
      LEFT JOIN questions q ON c.category_id = q.category_id AND q.status = 'active'
      WHERE c.status = 'active'
      GROUP BY c.category_id
      ORDER BY c.sort_order ASC, c.created_at ASC
    `;
    
    const categories = await query(categoriesSql);
    
    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取分类列表失败:', error);
    
    // 返回模拟数据
    const mockCategories = [
      {
        category_id: 1,
        category_name: '马克思主义基本原理',
        category_code: 'marxism',
        category_desc: '马克思主义基本原理概论',
        parent_id: 0,
        sort_order: 1,
        question_count: 245
      },
      {
        category_id: 11,
        category_name: '马克思主义哲学',
        category_code: 'marxism_philosophy',
        category_desc: '马克思主义哲学基本原理',
        parent_id: 1,
        sort_order: 1,
        question_count: 147
      },
      {
        category_id: 111,
        category_name: '唯物论',
        category_code: 'materialism',
        category_desc: '马克思主义唯物论',
        parent_id: 11,
        sort_order: 1,
        question_count: 35
      },
      {
        category_id: 1111,
        category_name: '物质概念',
        category_code: 'matter_concept',
        category_desc: '物质的哲学概念',
        parent_id: 111,
        sort_order: 1,
        question_count: 12
      },
      {
        category_id: 1112,
        category_name: '意识本质',
        category_code: 'consciousness_essence',
        category_desc: '意识的本质和特点',
        parent_id: 111,
        sort_order: 2,
        question_count: 15
      },
      {
        category_id: 112,
        category_name: '辩证法',
        category_code: 'dialectics',
        category_desc: '马克思主义辩证法',
        parent_id: 11,
        sort_order: 2,
        question_count: 42
      },
      {
        category_id: 1121,
        category_name: '联系观',
        category_code: 'connection_view',
        category_desc: '马克思主义联系观',
        parent_id: 112,
        sort_order: 1,
        question_count: 14
      },
      {
        category_id: 1123,
        category_name: '矛盾规律',
        category_code: 'contradiction_law',
        category_desc: '对立统一规律',
        parent_id: 112,
        sort_order: 3,
        question_count: 12
      },
      {
        category_id: 2,
        category_name: '毛泽东思想和中国特色社会主义理论体系概论',
        category_code: 'maoism',
        category_desc: '毛泽东思想和中国特色社会主义理论体系概论',
        parent_id: 0,
        sort_order: 2,
        question_count: 312
      },
      {
        category_id: 21,
        category_name: '毛泽东思想',
        category_code: 'mao_thought',
        category_desc: '毛泽东思想的形成和发展',
        parent_id: 2,
        sort_order: 1,
        question_count: 89
      }
    ];
    
    res.json({
      success: true,
      data: mockCategories
    });
  }
});

// 获取分类树形结构
router.get('/tree', async (req, res) => {
  try {
    const categoriesSql = `
      SELECT c.category_id, c.category_name, c.category_code, c.category_desc, 
             c.parent_id, c.sort_order,
             COUNT(q.question_id) as question_count
      FROM categories c
      LEFT JOIN questions q ON c.category_id = q.category_id AND q.status = 'active'
      WHERE c.status = 'active'
      GROUP BY c.category_id
      ORDER BY c.sort_order ASC, c.created_at ASC
    `;
    
    const categories = await query(categoriesSql);
    
    // 构建树形结构
    const buildTree = (items, parentId = 0) => {
      return items
        .filter(item => item.parent_id === parentId)
        .map(item => ({
          ...item,
          children: buildTree(items, item.category_id)
        }));
    };
    
    const tree = buildTree(categories);
    
    res.json({
      success: true,
      data: tree
    });
  } catch (error) {
    console.error('获取分类树失败:', error);
    
    // 返回模拟树形数据
    const mockTree = [
      {
        category_id: 1,
        category_name: '马克思主义基本原理',
        category_code: 'marxism',
        category_desc: '马克思主义基本原理概论',
        parent_id: 0,
        sort_order: 1,
        question_count: 245,
        children: [
          {
            category_id: 11,
            category_name: '马克思主义哲学',
            category_code: 'marxism_philosophy',
            category_desc: '马克思主义哲学基本原理',
            parent_id: 1,
            sort_order: 1,
            question_count: 147,
            children: [
              {
                category_id: 111,
                category_name: '唯物论',
                category_code: 'materialism',
                category_desc: '马克思主义唯物论',
                parent_id: 11,
                sort_order: 1,
                question_count: 35,
                children: [
                  {
                    category_id: 1111,
                    category_name: '物质概念',
                    category_code: 'matter_concept',
                    category_desc: '物质的哲学概念',
                    parent_id: 111,
                    sort_order: 1,
                    question_count: 12,
                    children: []
                  },
                  {
                    category_id: 1112,
                    category_name: '意识本质',
                    category_code: 'consciousness_essence',
                    category_desc: '意识的本质和特点',
                    parent_id: 111,
                    sort_order: 2,
                    question_count: 15,
                    children: []
                  }
                ]
              },
              {
                category_id: 112,
                category_name: '辩证法',
                category_code: 'dialectics',
                category_desc: '马克思主义辩证法',
                parent_id: 11,
                sort_order: 2,
                question_count: 42,
                children: [
                  {
                    category_id: 1121,
                    category_name: '联系观',
                    category_code: 'connection_view',
                    category_desc: '马克思主义联系观',
                    parent_id: 112,
                    sort_order: 1,
                    question_count: 14,
                    children: []
                  },
                  {
                    category_id: 1123,
                    category_name: '矛盾规律',
                    category_code: 'contradiction_law',
                    category_desc: '对立统一规律',
                    parent_id: 112,
                    sort_order: 3,
                    question_count: 12,
                    children: []
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        category_id: 2,
        category_name: '毛泽东思想和中国特色社会主义理论体系概论',
        category_code: 'maoism',
        category_desc: '毛泽东思想和中国特色社会主义理论体系概论',
        parent_id: 0,
        sort_order: 2,
        question_count: 312,
        children: [
          {
            category_id: 21,
            category_name: '毛泽东思想',
            category_code: 'mao_thought',
            category_desc: '毛泽东思想的形成和发展',
            parent_id: 2,
            sort_order: 1,
            question_count: 89,
            children: []
          }
        ]
      }
    ];
    
    res.json({
      success: true,
      data: mockTree
    });
  }
});

module.exports = router;
