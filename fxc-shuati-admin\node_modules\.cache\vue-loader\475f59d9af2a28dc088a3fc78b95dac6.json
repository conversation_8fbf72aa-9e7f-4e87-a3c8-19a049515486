{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\add.vue?vue&type=style&index=0&id=4f9629b5&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\add.vue", "mtime": 1752630759856}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYXBwLWNvbnRhaW5lciB7CiAgLmZvcm0tY2FyZCB7CiAgICAuY2FyZC1oZWFkZXIgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICB9CgogICAgLnF1ZXN0aW9uLWZvcm0gewogICAgICBwYWRkaW5nOiAyMHB4IDA7CgogICAgICAub3B0aW9ucy1jb250YWluZXIgewogICAgICAgIC5vcHRpb24taXRlbSB7CiAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CgogICAgICAgICAgLm9wdGlvbi1pbnB1dCB7CiAgICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkYA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"form-card\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>添加题目</span>\n        <div>\n          <el-button @click=\"handleCancel\">取消</el-button>\n          <el-button type=\"primary\" @click=\"handleSave\" :loading=\"saving\">保存</el-button>\n        </div>\n      </div>\n\n      <el-form\n        ref=\"questionForm\"\n        :model=\"questionForm\"\n        :rules=\"rules\"\n        label-width=\"120px\"\n        class=\"question-form\"\n      >\n        <!-- 基本信息 -->\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"题目类型\" prop=\"question_type\">\n              <el-select v-model=\"questionForm.question_type\" placeholder=\"请选择题目类型\" style=\"width: 100%\">\n                <el-option label=\"单选题\" value=\"single_choice\" />\n                <el-option label=\"多选题\" value=\"multiple_choice\" />\n                <el-option label=\"判断题\" value=\"true_false\" />\n                <el-option label=\"填空题\" value=\"fill_blank\" />\n                <el-option label=\"简答题\" value=\"short_answer\" />\n                <el-option label=\"论述题\" value=\"essay\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"难度等级\" prop=\"difficulty\">\n              <el-select v-model=\"questionForm.difficulty\" placeholder=\"请选择难度等级\" style=\"width: 100%\">\n                <el-option label=\"简单\" value=\"easy\" />\n                <el-option label=\"中等\" value=\"medium\" />\n                <el-option label=\"困难\" value=\"hard\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"题目分类\" prop=\"category_id\">\n              <el-cascader\n                v-model=\"questionForm.category_id\"\n                :options=\"categoryOptions\"\n                :props=\"categoryProps\"\n                placeholder=\"请选择题目分类\"\n                style=\"width: 100%\"\n                clearable\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"题目标签\" prop=\"tags\">\n              <el-select\n                v-model=\"questionForm.tags\"\n                multiple\n                placeholder=\"请选择题目标签\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"tag in tagOptions\"\n                  :key=\"tag.tag_id\"\n                  :label=\"tag.tag_name\"\n                  :value=\"tag.tag_id\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <!-- 题目内容 -->\n        <el-form-item label=\"题目内容\" prop=\"question_content\">\n          <el-input\n            v-model=\"questionForm.question_content\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入题目内容\"\n          />\n        </el-form-item>\n\n        <!-- 选择题选项 -->\n        <div v-if=\"questionForm.question_type === 'single_choice' || questionForm.question_type === 'multiple_choice'\">\n          <el-form-item label=\"选项设置\">\n            <div class=\"options-container\">\n              <div\n                v-for=\"(option, index) in questionForm.options\"\n                :key=\"index\"\n                class=\"option-item\"\n              >\n                <el-input\n                  v-model=\"option.content\"\n                  :placeholder=\"`选项 ${String.fromCharCode(65 + index)}`\"\n                  class=\"option-input\"\n                />\n                <el-button\n                  v-if=\"questionForm.options.length > 2\"\n                  type=\"danger\"\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  @click=\"removeOption(index)\"\n                />\n              </div>\n              <el-button\n                v-if=\"questionForm.options.length < 6\"\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                size=\"mini\"\n                @click=\"addOption\"\n              >\n                添加选项\n              </el-button>\n            </div>\n          </el-form-item>\n\n          <el-form-item label=\"正确答案\" prop=\"correct_answer\">\n            <el-checkbox-group v-if=\"questionForm.question_type === 'multiple_choice'\" v-model=\"questionForm.correct_answer\">\n              <el-checkbox\n                v-for=\"(option, index) in questionForm.options\"\n                :key=\"index\"\n                :label=\"String.fromCharCode(65 + index)\"\n              >\n                {{ String.fromCharCode(65 + index) }}\n              </el-checkbox>\n            </el-checkbox-group>\n            <el-radio-group v-else v-model=\"questionForm.correct_answer\">\n              <el-radio\n                v-for=\"(option, index) in questionForm.options\"\n                :key=\"index\"\n                :label=\"String.fromCharCode(65 + index)\"\n              >\n                {{ String.fromCharCode(65 + index) }}\n              </el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </div>\n\n        <!-- 判断题答案 -->\n        <el-form-item v-if=\"questionForm.question_type === 'true_false'\" label=\"正确答案\" prop=\"correct_answer\">\n          <el-radio-group v-model=\"questionForm.correct_answer\">\n            <el-radio label=\"true\">正确</el-radio>\n            <el-radio label=\"false\">错误</el-radio>\n          </el-radio-group>\n        </el-form-item>\n\n        <!-- 填空题答案 -->\n        <el-form-item v-if=\"questionForm.question_type === 'fill_blank'\" label=\"参考答案\" prop=\"correct_answer\">\n          <el-input\n            v-model=\"questionForm.correct_answer\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入参考答案，多个答案用分号分隔\"\n          />\n        </el-form-item>\n\n        <!-- 简答题/论述题答案 -->\n        <el-form-item v-if=\"questionForm.question_type === 'short_answer' || questionForm.question_type === 'essay'\" label=\"参考答案\" prop=\"correct_answer\">\n          <el-input\n            v-model=\"questionForm.correct_answer\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入参考答案\"\n          />\n        </el-form-item>\n\n        <!-- 解析 -->\n        <el-form-item label=\"题目解析\">\n          <el-input\n            v-model=\"questionForm.explanation\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入题目解析（可选）\"\n          />\n        </el-form-item>\n\n        <!-- 其他设置 -->\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"分值\">\n              <el-input-number v-model=\"questionForm.score\" :min=\"1\" :max=\"100\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"排序\">\n              <el-input-number v-model=\"questionForm.sort_order\" :min=\"0\" :max=\"9999\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"questionForm.status\" style=\"width: 100%\">\n                <el-option label=\"启用\" value=\"active\" />\n                <el-option label=\"禁用\" value=\"disabled\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'QuestionAdd',\n  data() {\n    return {\n      saving: false,\n      questionForm: {\n        question_type: '',\n        difficulty: 'medium',\n        category_id: [],\n        tags: [],\n        question_content: '',\n        options: [\n          { content: '' },\n          { content: '' }\n        ],\n        correct_answer: '',\n        explanation: '',\n        score: 5,\n        sort_order: 0,\n        status: 'active'\n      },\n      categoryOptions: [],\n      tagOptions: [],\n      categoryProps: {\n        value: 'category_id',\n        label: 'category_name',\n        children: 'children',\n        emitPath: false\n      },\n      rules: {\n        question_type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],\n        difficulty: [{ required: true, message: '请选择难度等级', trigger: 'change' }],\n        category_id: [{ required: true, message: '请选择题目分类', trigger: 'change' }],\n        question_content: [{ required: true, message: '请输入题目内容', trigger: 'blur' }],\n        correct_answer: [{ required: true, message: '请设置正确答案', trigger: 'change' }]\n      }\n    }\n  },\n  created() {\n    this.loadCategoryOptions()\n    this.loadTagOptions()\n  },\n  methods: {\n    loadCategoryOptions() {\n      // 模拟分类数据\n      this.categoryOptions = [\n        {\n          category_id: 1,\n          category_name: '马克思主义基本原理',\n          children: [\n            {\n              category_id: 11,\n              category_name: '马克思主义哲学',\n              children: [\n                {\n                  category_id: 111,\n                  category_name: '唯物论',\n                  children: [\n                    { category_id: 1111, category_name: '物质概念' },\n                    { category_id: 1112, category_name: '意识本质' },\n                    { category_id: 1113, category_name: '物质与意识关系' }\n                  ]\n                },\n                {\n                  category_id: 112,\n                  category_name: '辩证法',\n                  children: [\n                    { category_id: 1121, category_name: '联系观' },\n                    { category_id: 1122, category_name: '发展观' },\n                    { category_id: 1123, category_name: '矛盾规律' }\n                  ]\n                },\n                { category_id: 113, category_name: '认识论' },\n                { category_id: 114, category_name: '历史观' }\n              ]\n            },\n            { category_id: 12, category_name: '马克思主义政治经济学' },\n            { category_id: 13, category_name: '科学社会主义' }\n          ]\n        },\n        {\n          category_id: 2,\n          category_name: '毛泽东思想和中国特色社会主义理论体系概论',\n          children: [\n            { category_id: 21, category_name: '毛泽东思想' },\n            { category_id: 22, category_name: '邓小平理论' },\n            { category_id: 23, category_name: '三个代表重要思想' }\n          ]\n        },\n        {\n          category_id: 3,\n          category_name: '中国近现代史纲要',\n          children: [\n            { category_id: 31, category_name: '旧民主主义革命时期' },\n            { category_id: 32, category_name: '新民主主义革命时期' },\n            { category_id: 33, category_name: '社会主义革命和建设时期' }\n          ]\n        },\n        {\n          category_id: 4,\n          category_name: '思想道德与法治'\n        },\n        {\n          category_id: 5,\n          category_name: '形势与政策'\n        }\n      ]\n    },\n    loadTagOptions() {\n      // 模拟标签数据\n      this.tagOptions = [\n        { tag_id: 1, tag_name: '马克思主义基本原理' },\n        { tag_id: 2, tag_name: '毛泽东思想' },\n        { tag_id: 3, tag_name: '中国近现代史纲要' },\n        { tag_id: 4, tag_name: '思想道德与法治' },\n        { tag_id: 5, tag_name: '形势与政策' },\n        { tag_id: 6, tag_name: '重点难点' },\n        { tag_id: 7, tag_name: '高频考点' },\n        { tag_id: 8, tag_name: '易错题' },\n        { tag_id: 9, tag_name: '哲学原理' },\n        { tag_id: 10, tag_name: '政治经济学' },\n        { tag_id: 11, tag_name: '科学社会主义' },\n        { tag_id: 12, tag_name: '新民主主义革命' },\n        { tag_id: 13, tag_name: '社会主义建设' },\n        { tag_id: 14, tag_name: '改革开放' },\n        { tag_id: 15, tag_name: '新时代' },\n        { tag_id: 16, tag_name: '2023年真题' },\n        { tag_id: 17, tag_name: '2022年真题' },\n        { tag_id: 18, tag_name: '模拟题' }\n      ]\n    },\n    addOption() {\n      if (this.questionForm.options.length < 6) {\n        this.questionForm.options.push({ content: '' })\n      }\n    },\n    removeOption(index) {\n      if (this.questionForm.options.length > 2) {\n        this.questionForm.options.splice(index, 1)\n        // 重置答案选择\n        this.questionForm.correct_answer = ''\n      }\n    },\n    handleSave() {\n      this.$refs.questionForm.validate((valid) => {\n        if (valid) {\n          this.saving = true\n\n          // 模拟保存\n          setTimeout(() => {\n            this.saving = false\n            this.$notify({\n              title: '成功',\n              message: '题目添加成功',\n              type: 'success',\n              duration: 2000\n            })\n\n            // 跳转到题目列表\n            this.$router.push('/questions/list')\n          }, 1000)\n        } else {\n          this.$message.error('请完善题目信息')\n        }\n      })\n    },\n    handleCancel() {\n      this.$confirm('确定要取消添加题目吗？未保存的数据将丢失。', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$router.push('/questions/list')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .form-card {\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .question-form {\n      padding: 20px 0;\n\n      .options-container {\n        .option-item {\n          display: flex;\n          align-items: center;\n          margin-bottom: 10px;\n\n          .option-input {\n            flex: 1;\n            margin-right: 10px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}