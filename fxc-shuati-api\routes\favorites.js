const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Mock 收藏数据
const favorites = [
  {
    favorite_id: 1,
    user_id: 1,
    question_id: 1,
    created_at: '2023-01-01T00:00:00.000Z'
  },
  {
    favorite_id: 2,
    user_id: 1,
    question_id: 3,
    created_at: '2023-01-02T00:00:00.000Z'
  }
];

// Mock 题目数据（简化版，实际应该从questions表获取）
const questions = [
  {
    question_id: 1,
    question_type: 'single',
    question_content: "What is the capital of France?",
    question_difficulty: 1,
    question_subject: "考研英语一",
    question_chapter: "阅读理解"
  },
  {
    question_id: 3,
    question_type: 'multiple',
    question_content: "Which of the following cities are in France?",
    question_difficulty: 3,
    question_subject: "考研英语一",
    question_chapter: "阅读理解"
  }
];

// 获取用户收藏列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { page = 1, limit = 20 } = req.query;

    // 获取用户的收藏
    const userFavorites = favorites.filter(f => f.user_id === userId);

    // 获取收藏的题目详情
    const favoriteQuestions = userFavorites.map(favorite => {
      const question = questions.find(q => q.question_id === favorite.question_id);
      return {
        favorite_id: favorite.favorite_id,
        question_id: favorite.question_id,
        created_at: favorite.created_at,
        question: question || null
      };
    }).filter(item => item.question !== null);

    // 分页处理
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedFavorites = favoriteQuestions.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        favorites: paginatedFavorites,
        pagination: {
          current_page: pageNum,
          per_page: pageSize,
          total: favoriteQuestions.length,
          total_pages: Math.ceil(favoriteQuestions.length / pageSize)
        }
      },
      message: '获取收藏列表成功'
    });

  } catch (error) {
    console.error('获取收藏列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取收藏列表失败'
    });
  }
});

// 添加收藏
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { question_id } = req.body;

    if (!question_id) {
      return res.status(400).json({
        success: false,
        message: '题目ID不能为空'
      });
    }

    // 检查题目是否存在
    const question = questions.find(q => q.question_id === parseInt(question_id));
    if (!question) {
      return res.status(404).json({
        success: false,
        message: '题目不存在'
      });
    }

    // 检查是否已经收藏
    const existingFavorite = favorites.find(f => 
      f.user_id === userId && f.question_id === parseInt(question_id)
    );

    if (existingFavorite) {
      return res.status(400).json({
        success: false,
        message: '题目已经收藏过了'
      });
    }

    // 添加收藏
    const newFavorite = {
      favorite_id: favorites.length + 1,
      user_id: userId,
      question_id: parseInt(question_id),
      created_at: new Date().toISOString()
    };

    favorites.push(newFavorite);

    res.json({
      success: true,
      data: newFavorite,
      message: '收藏成功'
    });

  } catch (error) {
    console.error('添加收藏错误:', error);
    res.status(500).json({
      success: false,
      message: '收藏失败'
    });
  }
});

// 取消收藏
router.delete('/:question_id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const questionId = parseInt(req.params.question_id);

    // 查找收藏记录
    const favoriteIndex = favorites.findIndex(f => 
      f.user_id === userId && f.question_id === questionId
    );

    if (favoriteIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '收藏记录不存在'
      });
    }

    // 删除收藏
    favorites.splice(favoriteIndex, 1);

    res.json({
      success: true,
      message: '取消收藏成功'
    });

  } catch (error) {
    console.error('取消收藏错误:', error);
    res.status(500).json({
      success: false,
      message: '取消收藏失败'
    });
  }
});

// 检查题目是否已收藏
router.get('/check/:question_id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const questionId = parseInt(req.params.question_id);

    const favorite = favorites.find(f => 
      f.user_id === userId && f.question_id === questionId
    );

    res.json({
      success: true,
      data: {
        is_favorited: !!favorite,
        favorite_id: favorite ? favorite.favorite_id : null
      },
      message: '检查收藏状态成功'
    });

  } catch (error) {
    console.error('检查收藏状态错误:', error);
    res.status(500).json({
      success: false,
      message: '检查收藏状态失败'
    });
  }
});

// 批量操作收藏
router.post('/batch', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { action, question_ids } = req.body;

    if (!action || !question_ids || !Array.isArray(question_ids)) {
      return res.status(400).json({
        success: false,
        message: '参数错误'
      });
    }

    if (action === 'add') {
      // 批量添加收藏
      const newFavorites = [];
      for (const questionId of question_ids) {
        const existingFavorite = favorites.find(f => 
          f.user_id === userId && f.question_id === parseInt(questionId)
        );

        if (!existingFavorite) {
          const newFavorite = {
            favorite_id: favorites.length + newFavorites.length + 1,
            user_id: userId,
            question_id: parseInt(questionId),
            created_at: new Date().toISOString()
          };
          favorites.push(newFavorite);
          newFavorites.push(newFavorite);
        }
      }

      res.json({
        success: true,
        data: { added_count: newFavorites.length },
        message: `批量收藏成功，共收藏${newFavorites.length}道题目`
      });

    } else if (action === 'remove') {
      // 批量取消收藏
      let removedCount = 0;
      for (const questionId of question_ids) {
        const favoriteIndex = favorites.findIndex(f => 
          f.user_id === userId && f.question_id === parseInt(questionId)
        );

        if (favoriteIndex !== -1) {
          favorites.splice(favoriteIndex, 1);
          removedCount++;
        }
      }

      res.json({
        success: true,
        data: { removed_count: removedCount },
        message: `批量取消收藏成功，共取消${removedCount}道题目`
      });

    } else {
      res.status(400).json({
        success: false,
        message: '不支持的操作类型'
      });
    }

  } catch (error) {
    console.error('批量操作收藏错误:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败'
    });
  }
});

module.exports = router;
