{"_from": "is-docker@^2.0.0", "_id": "is-docker@2.2.1", "_inBundle": false, "_integrity": "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==", "_location": "/is-docker", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-docker@^2.0.0", "name": "is-docker", "escapedName": "is-docker", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/clipboardy/is-wsl"], "_resolved": "https://registry.npmmirror.com/is-docker/-/is-docker-2.2.1.tgz", "_shasum": "33eeabe23cfe86f14bde4408a02c0cfb853acdaa", "_spec": "is-docker@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\clipboardy\\node_modules\\is-wsl", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bin": {"is-docker": "cli.js"}, "bugs": {"url": "https://github.com/sindresorhus/is-docker/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if the process is running inside a Docker container", "devDependencies": {"ava": "^1.4.1", "sinon": "^7.3.2", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts", "cli.js"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/is-docker#readme", "keywords": ["detect", "docker", "dockerized", "container", "inside", "is", "env", "environment", "process"], "license": "MIT", "name": "is-docker", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-docker.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.2.1"}