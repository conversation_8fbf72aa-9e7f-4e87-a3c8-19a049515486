{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Navbar.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgQnJlYWRjcnVtYiBmcm9tICdAL2NvbXBvbmVudHMvQnJlYWRjcnVtYicKaW1wb3J0IEhhbWJ1cmdlciBmcm9tICdAL2NvbXBvbmVudHMvSGFtYnVyZ2VyJwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIEJyZWFkY3J1bWIsCiAgICBIYW1idXJnZXIKICB9LAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKFsKICAgICAgJ3NpZGViYXInLAogICAgICAnYXZhdGFyJwogICAgXSkKICB9LAogIG1ldGhvZHM6IHsKICAgIHRvZ2dsZVNpZGVCYXIoKSB7CiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhcHAvdG9nZ2xlU2lkZUJhcicpCiAgICB9LAogICAgYXN5bmMgbG9nb3V0KCkgewogICAgICBhd2FpdCB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndXNlci9sb2dvdXQnKQogICAgICB0aGlzLiRyb3V0ZXIucHVzaChgL2xvZ2luP3JlZGlyZWN0PSR7dGhpcy4kcm91dGUuZnVsbFBhdGh9YCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n\n    <breadcrumb class=\"breadcrumb-container\" />\n\n    <div class=\"right-menu\">\n      <el-dropdown class=\"avatar-container\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"avatar+'?imageView2/1/w/80/h/80'\" class=\"user-avatar\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\" class=\"user-dropdown\">\n          <router-link to=\"/\">\n            <el-dropdown-item>\n              Home\n            </el-dropdown-item>\n          </router-link>\n          <a target=\"_blank\" href=\"https://github.com/PanJiaChen/vue-admin-template/\">\n            <el-dropdown-item>Github</el-dropdown-item>\n          </a>\n          <a target=\"_blank\" href=\"https://panjiachen.github.io/vue-element-admin-site/#/\">\n            <el-dropdown-item>Docs</el-dropdown-item>\n          </a>\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span style=\"display:block;\">Log Out</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport Hamburger from '@/components/Hamburger'\n\nexport default {\n  components: {\n    Breadcrumb,\n    Hamburger\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar'\n    ])\n  },\n  methods: {\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      await this.$store.dispatch('user/logout')\n      this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 50px;\n  overflow: hidden;\n  position: relative;\n  background: #fff;\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\n\n  .hamburger-container {\n    line-height: 46px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 50px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background .3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, .025)\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 30px;\n\n      .avatar-wrapper {\n        margin-top: 5px;\n        position: relative;\n\n        .user-avatar {\n          cursor: pointer;\n          width: 40px;\n          height: 40px;\n          border-radius: 10px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -20px;\n          top: 25px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}