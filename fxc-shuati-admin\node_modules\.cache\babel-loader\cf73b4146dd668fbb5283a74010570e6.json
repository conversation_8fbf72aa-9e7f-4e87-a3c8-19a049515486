{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\dashboard\\index.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovY29kZS9mYW54aWFvY2hhbmcvZnhjLXNodWF0aS1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMi5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGFzaGJvYXJkJywKICBjb21wdXRlZDogX29iamVjdFNwcmVhZCh7fSwgbWFwR2V0dGVycyhbJ25hbWUnXSkpCn07"}, {"version": 3, "names": ["mapGetters", "name", "computed", "_objectSpread"], "sources": ["src/views/dashboard/index.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <div class=\"dashboard-text\">name: {{ name }}</div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'Dashboard',\n  computed: {\n    ...mapGetters([\n      'name'\n    ])\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard {\n  &-container {\n    margin: 30px;\n  }\n  &-text {\n    font-size: 30px;\n    line-height: 46px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;AAOA,SAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,QAAA,EAAAC,aAAA,KACAH,UAAA,EACA,OACA;AAEA", "ignoreList": []}]}