// app.js
App({
  onLaunch() {
    console.log('刷题小程序启动')

    // 检查更新
    this.checkForUpdate()

    // 初始化用户数据
    this.initUserData()

    // 微信登录
    this.wxLogin()
  },

  onShow() {
    console.log('刷题小程序显示')
  },

  onHide() {
    console.log('刷题小程序隐藏')
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(() => {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: (res) => {
                if (res.confirm) {
                  updateManager.applyUpdate()
                }
              }
            })
          })
        }
      })
    }
  },

  // 初始化用户数据
  initUserData() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
    }
  },

  // 微信登录
  wxLogin() {
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('登录成功，code:', res.code)
          // 这里应该调用后端API换取token
          // 暂时使用mock数据
          this.globalData.token = 'mock_token_' + Date.now()
        } else {
          console.log('登录失败！' + res.errMsg)
        }
      },
      fail: (err) => {
        console.log('登录失败', err)
      }
    })
  },

  globalData: {
    userInfo: null,
    token: null,
    apiBase: 'http://localhost:3000/api'
  }
})
