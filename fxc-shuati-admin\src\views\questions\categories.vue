<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索分类名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加分类
        </el-button>
      </div>
    </el-card>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="treeData"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      row-key="category_id"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="ID" prop="category_id" align="center" width="80" />
      <el-table-column label="分类名称" prop="category_name" min-width="200" />
      <el-table-column label="分类编码" prop="category_code" min-width="150" />
      <el-table-column label="分类描述" prop="category_desc" min-width="200" />
      <el-table-column label="题目数量" prop="question_count" width="100" align="center" />
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort_order" width="80" align="center" />
      <el-table-column label="创建时间" min-width="120" align="center">
        <template slot-scope="{row}">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="父分类" prop="parent_id">
          <el-select v-model="temp.parent_id" placeholder="请选择父分类" style="width: 100%">
            <el-option label="顶级分类" :value="0" />
            <el-option
              v-for="item in parentOptions"
              :key="item.category_id"
              :label="item.category_name"
              :value="item.category_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称" prop="category_name">
          <el-input v-model="temp.category_name" />
        </el-form-item>
        <el-form-item label="分类编码" prop="category_code">
          <el-input v-model="temp.category_code" />
        </el-form-item>
        <el-form-item label="分类描述" prop="category_desc">
          <el-input v-model="temp.category_desc" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择状态">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="temp.sort_order" :min="0" :max="999" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { formatDate } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'QuestionCategories',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 50,
        keyword: '',
        status: ''
      },
      parentOptions: [],
      temp: {
        category_id: undefined,
        parent_id: 0,
        category_name: '',
        category_code: '',
        category_desc: '',
        status: 'active',
        sort_order: 0
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑分类',
        create: '添加分类'
      },
      rules: {
        category_name: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],
        category_code: [{ required: true, message: '分类编码不能为空', trigger: 'blur' }]
      }
    }
  },
  computed: {
    treeData() {
      // 将平铺数据转换为树形结构
      const tree = []
      const map = {}

      // 先创建所有节点的映射
      this.list.forEach(item => {
        map[item.category_id] = { ...item, children: [] }
      })

      // 构建树形结构
      this.list.forEach(item => {
        if (item.parent_id === 0) {
          // 顶级分类
          tree.push(map[item.category_id])
        } else {
          // 子分类
          if (map[item.parent_id]) {
            map[item.parent_id].children.push(map[item.category_id])
          }
        }
      })

      return tree
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true

      // 模拟考研政治分类数据
      setTimeout(() => {
        this.list = [
          // 一级分类
          {
            category_id: 1,
            parent_id: 0,
            category_name: '马克思主义基本原理',
            category_code: 'marxism',
            category_desc: '马克思主义基本原理概论',
            question_count: 156,
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-01'
          },
          {
            category_id: 2,
            parent_id: 0,
            category_name: '毛泽东思想和中国特色社会主义理论体系概论',
            category_code: 'maoism',
            category_desc: '毛泽东思想和中国特色社会主义理论体系概论',
            question_count: 203,
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-01'
          },
          {
            category_id: 3,
            parent_id: 0,
            category_name: '中国近现代史纲要',
            category_code: 'history',
            category_desc: '中国近现代史纲要',
            question_count: 178,
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-01'
          },
          {
            category_id: 4,
            parent_id: 0,
            category_name: '思想道德与法治',
            category_code: 'morality',
            category_desc: '思想道德与法治',
            question_count: 134,
            status: 'active',
            sort_order: 4,
            created_at: '2024-01-01'
          },
          {
            category_id: 5,
            parent_id: 0,
            category_name: '形势与政策',
            category_code: 'policy',
            category_desc: '形势与政策以及当代世界经济与政治',
            question_count: 89,
            status: 'active',
            sort_order: 5,
            created_at: '2024-01-01'
          },
          // 马克思主义基本原理的二级分类
          {
            category_id: 11,
            parent_id: 1,
            category_name: '马克思主义哲学',
            category_code: 'marxism_philosophy',
            category_desc: '马克思主义哲学基本原理',
            question_count: 78,
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-02'
          },
          {
            category_id: 12,
            parent_id: 1,
            category_name: '马克思主义政治经济学',
            category_code: 'marxism_economics',
            category_desc: '马克思主义政治经济学原理',
            question_count: 45,
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-02'
          },
          {
            category_id: 13,
            parent_id: 1,
            category_name: '科学社会主义',
            category_code: 'scientific_socialism',
            category_desc: '科学社会主义基本原理',
            question_count: 33,
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-02'
          },
          // 马克思主义哲学的三级分类
          {
            category_id: 111,
            parent_id: 11,
            category_name: '唯物论',
            category_code: 'materialism',
            category_desc: '马克思主义唯物论',
            question_count: 25,
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-03'
          },
          {
            category_id: 112,
            parent_id: 11,
            category_name: '辩证法',
            category_code: 'dialectics',
            category_desc: '马克思主义辩证法',
            question_count: 28,
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-03'
          },
          {
            category_id: 113,
            parent_id: 11,
            category_name: '认识论',
            category_code: 'epistemology',
            category_desc: '马克思主义认识论',
            question_count: 25,
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-03'
          },
          // 毛泽东思想的二级分类
          {
            category_id: 21,
            parent_id: 2,
            category_name: '毛泽东思想',
            category_code: 'mao_thought',
            category_desc: '毛泽东思想的形成和发展',
            question_count: 89,
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-02'
          },
          {
            category_id: 22,
            parent_id: 2,
            category_name: '邓小平理论',
            category_code: 'deng_theory',
            category_desc: '邓小平理论',
            question_count: 67,
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-02'
          },
          {
            category_id: 23,
            parent_id: 2,
            category_name: '三个代表重要思想',
            category_code: 'three_represents',
            category_desc: '三个代表重要思想',
            question_count: 47,
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-02'
          },
          // 中国近现代史的二级分类
          {
            category_id: 31,
            parent_id: 3,
            category_name: '旧民主主义革命时期',
            category_code: 'old_democratic_revolution',
            category_desc: '1840-1919年旧民主主义革命时期',
            question_count: 56,
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-02'
          },
          {
            category_id: 32,
            parent_id: 3,
            category_name: '新民主主义革命时期',
            category_code: 'new_democratic_revolution',
            category_desc: '1919-1949年新民主主义革命时期',
            question_count: 78,
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-02'
          },
          {
            category_id: 33,
            parent_id: 3,
            category_name: '社会主义革命和建设时期',
            category_code: 'socialist_construction',
            category_desc: '1949年以来社会主义革命和建设时期',
            question_count: 44,
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-02'
          }
        ]
        this.total = this.list.length
        this.listLoading = false

        // 更新父分类选项（只显示可以作为父级的分类）
        this.parentOptions = this.list.filter(item => item.parent_id === 0 || item.parent_id === 1 || item.parent_id === 2 || item.parent_id === 3)
      }, 500)
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        category_id: undefined,
        parent_id: 0,
        category_name: '',
        category_code: '',
        category_desc: '',
        status: 'active',
        sort_order: 0
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$notify({
            title: '成功',
            message: '创建成功（模拟）',
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$notify({
            title: '成功',
            message: '更新成功（模拟）',
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$notify({
          title: '成功',
          message: '删除成功（模拟）',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-card {
    margin-bottom: 20px;
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .filter-item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  .el-table {
    margin-bottom: 20px;
    width: 100% !important;

    .el-table__body-wrapper {
      width: 100% !important;
    }
  }
  .pagination-container {
    padding: 15px 0;
  }
}
</style>
