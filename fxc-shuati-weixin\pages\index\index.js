// index.js
const { api } = require('../../utils/api.js')
const { showError } = require('../../utils/util.js')

Page({
  data: {
    banners: [
      {
        title: '考研英语刷题',
        desc: '海量真题，助力考研',
        color: '#409EFF'
      },
      {
        title: '智能练习',
        desc: '个性化推荐，高效学习',
        color: '#67C23A'
      },
      {
        title: '错题本',
        desc: '查漏补缺，巩固提升',
        color: '#E6A23C'
      }
    ],
    userStats: {
      totalQuestions: 0,
      correctRate: 0,
      studyDays: 0,
      favoriteCount: 0
    },
    recentPractice: []
  },

  onLoad() {
    console.log('首页加载')
    this.loadUserStats()
    this.loadRecentPractice()
  },

  onShow() {
    console.log('首页显示')
    // 每次显示时刷新数据
    this.loadUserStats()
    this.loadRecentPractice()
  },

  // 加载用户统计数据
  loadUserStats() {
    // 使用mock数据，实际应该调用API
    this.setData({
      userStats: {
        totalQuestions: 156,
        correctRate: 78,
        studyDays: 15,
        favoriteCount: 23
      }
    })

    // 实际API调用示例
    // api.getUserStats().then(res => {
    //   this.setData({
    //     userStats: res.data
    //   })
    // }).catch(err => {
    //   console.error('获取用户统计失败', err)
    // })
  },

  // 加载最近练习记录
  loadRecentPractice() {
    // 使用mock数据
    this.setData({
      recentPractice: [
        {
          id: 1,
          title: '考研英语一模拟题',
          description: '2023年真题模拟',
          progress: 65
        },
        {
          id: 2,
          title: '阅读理解专项',
          description: '提升阅读能力',
          progress: 40
        }
      ]
    })

    // 实际API调用示例
    // api.getPracticeRecords({ limit: 5 }).then(res => {
    //   this.setData({
    //     recentPractice: res.data
    //   })
    // }).catch(err => {
    //   console.error('获取练习记录失败', err)
    // })
  },

  // 跳转到练习页面
  goToPractice() {
    wx.switchTab({
      url: '/pages/practice/practice'
    })
  },

  // 跳转到收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 跳转到错题本
  goToErrors() {
    wx.navigateTo({
      url: '/pages/errors/errors'
    })
  },

  // 跳转到个人中心
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 继续练习
  continueQuestion(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages/question/question?practiceId=${item.id}&title=${item.title}`
    })
  }
})
