// index.js
const { api } = require('../../utils/api.js')
const { showError } = require('../../utils/util.js')

Page({
  data: {
    banners: [
      {
        title: '考研政治刷题',
        desc: '海量真题，助力考研',
        color: '#409EFF'
      },
      {
        title: '智能练习',
        desc: '个性化推荐，高效学习',
        color: '#67C23A'
      },
      {
        title: '错题本',
        desc: '查漏补缺，巩固提升',
        color: '#E6A23C'
      }
    ],
    userStats: {
      totalQuestions: 0,
      correctRate: 0,
      studyDays: 0,
      favoriteCount: 0
    },
    recentPractice: []
  },

  onLoad() {
    console.log('首页加载')
    this.loadUserStats()
    this.loadRecentPractice()
  },

  onShow() {
    console.log('首页显示')
    // 每次显示时刷新数据
    this.loadUserStats()
    this.loadRecentPractice()
  },

  // 加载用户统计数据
  loadUserStats() {
    api.getUserStats().then(res => {
      this.setData({
        userStats: {
          totalQuestions: res.data.totalQuestions,
          correctRate: res.data.accuracy,
          studyDays: Math.ceil(res.data.practiceCount / 2), // 简单计算学习天数
          favoriteCount: res.data.favoriteCount
        }
      })
    }).catch(err => {
      console.error('获取用户统计失败', err)
      // 失败时使用默认数据
      this.setData({
        userStats: {
          totalQuestions: 156,
          correctRate: 79,
          studyDays: 15,
          favoriteCount: 23
        }
      })
    })
  },

  // 加载最近练习记录
  loadRecentPractice() {
    // 使用模拟的练习记录数据
    this.setData({
      recentPractice: [
        {
          id: 1,
          title: '马克思主义基本原理',
          description: '哲学、政治经济学、科学社会主义',
          progress: 65,
          category_id: 1
        },
        {
          id: 2,
          title: '毛泽东思想概论',
          description: '毛泽东思想和中国特色社会主义理论',
          progress: 40,
          category_id: 2
        },
        {
          id: 3,
          title: '中国近现代史纲要',
          description: '近代史重要事件和人物',
          progress: 78,
          category_id: 3
        }
      ]
    })
  },

  // 跳转到练习页面
  goToPractice() {
    wx.switchTab({
      url: '/pages/practice/practice'
    })
  },

  // 跳转到收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 跳转到错题本
  goToErrors() {
    wx.navigateTo({
      url: '/pages/errors/errors'
    })
  },

  // 跳转到个人中心
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 继续练习
  continueQuestion(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages/question/question?category_id=${item.category_id}&title=${encodeURIComponent(item.title)}`
    })
  },

  // 开始随机练习
  startRandomPractice() {
    wx.navigateTo({
      url: '/pages/question/question?random=true&title=随机练习'
    })
  },

  // 前往练习页面
  goToPractice() {
    wx.switchTab({
      url: '/pages/practice/practice'
    })
  }
})
