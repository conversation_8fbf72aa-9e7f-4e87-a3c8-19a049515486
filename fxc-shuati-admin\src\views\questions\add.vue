<template>
  <div class="app-container">
    <el-card class="form-card">
      <div slot="header" class="card-header">
        <span>添加题目</span>
        <div>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
        </div>
      </div>

      <el-form
        ref="questionForm"
        :model="questionForm"
        :rules="rules"
        label-width="120px"
        class="question-form"
      >
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="题目类型" prop="question_type">
              <el-select v-model="questionForm.question_type" placeholder="请选择题目类型" style="width: 100%">
                <el-option label="单选题" value="single_choice" />
                <el-option label="多选题" value="multiple_choice" />
                <el-option label="判断题" value="true_false" />
                <el-option label="填空题" value="fill_blank" />
                <el-option label="简答题" value="short_answer" />
                <el-option label="论述题" value="essay" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="难度等级" prop="difficulty">
              <el-select v-model="questionForm.difficulty" placeholder="请选择难度等级" style="width: 100%">
                <el-option label="简单" value="easy" />
                <el-option label="中等" value="medium" />
                <el-option label="困难" value="hard" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="题目分类" prop="category_id">
              <el-cascader
                v-model="questionForm.category_id"
                :options="categoryOptions"
                :props="categoryProps"
                placeholder="请选择题目分类"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="题目标签" prop="tags">
              <el-select
                v-model="questionForm.tags"
                multiple
                placeholder="请选择题目标签"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in tagOptions"
                  :key="tag.tag_id"
                  :label="tag.tag_name"
                  :value="tag.tag_id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 题目内容 -->
        <el-form-item label="题目内容" prop="question_content">
          <el-input
            v-model="questionForm.question_content"
            type="textarea"
            :rows="4"
            placeholder="请输入题目内容"
          />
        </el-form-item>

        <!-- 选择题选项 -->
        <div v-if="questionForm.question_type === 'single_choice' || questionForm.question_type === 'multiple_choice'">
          <el-form-item label="选项设置">
            <div class="options-container">
              <div
                v-for="(option, index) in questionForm.options"
                :key="index"
                class="option-item"
              >
                <el-input
                  v-model="option.content"
                  :placeholder="`选项 ${String.fromCharCode(65 + index)}`"
                  class="option-input"
                />
                <el-button
                  v-if="questionForm.options.length > 2"
                  type="danger"
                  icon="el-icon-delete"
                  size="mini"
                  @click="removeOption(index)"
                />
              </div>
              <el-button
                v-if="questionForm.options.length < 6"
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="addOption"
              >
                添加选项
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="正确答案" prop="correct_answer">
            <el-checkbox-group v-if="questionForm.question_type === 'multiple_choice'" v-model="questionForm.correct_answer">
              <el-checkbox
                v-for="(option, index) in questionForm.options"
                :key="index"
                :label="String.fromCharCode(65 + index)"
              >
                {{ String.fromCharCode(65 + index) }}
              </el-checkbox>
            </el-checkbox-group>
            <el-radio-group v-else v-model="questionForm.correct_answer">
              <el-radio
                v-for="(option, index) in questionForm.options"
                :key="index"
                :label="String.fromCharCode(65 + index)"
              >
                {{ String.fromCharCode(65 + index) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 判断题答案 -->
        <el-form-item v-if="questionForm.question_type === 'true_false'" label="正确答案" prop="correct_answer">
          <el-radio-group v-model="questionForm.correct_answer">
            <el-radio label="true">正确</el-radio>
            <el-radio label="false">错误</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 填空题答案 -->
        <el-form-item v-if="questionForm.question_type === 'fill_blank'" label="参考答案" prop="correct_answer">
          <el-input
            v-model="questionForm.correct_answer"
            type="textarea"
            :rows="2"
            placeholder="请输入参考答案，多个答案用分号分隔"
          />
        </el-form-item>

        <!-- 简答题/论述题答案 -->
        <el-form-item v-if="questionForm.question_type === 'short_answer' || questionForm.question_type === 'essay'" label="参考答案" prop="correct_answer">
          <el-input
            v-model="questionForm.correct_answer"
            type="textarea"
            :rows="6"
            placeholder="请输入参考答案"
          />
        </el-form-item>

        <!-- 解析 -->
        <el-form-item label="题目解析">
          <el-input
            v-model="questionForm.explanation"
            type="textarea"
            :rows="4"
            placeholder="请输入题目解析（可选）"
          />
        </el-form-item>

        <!-- 其他设置 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分值">
              <el-input-number v-model="questionForm.score" :min="1" :max="100" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序">
              <el-input-number v-model="questionForm.sort_order" :min="0" :max="9999" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="questionForm.status" style="width: 100%">
                <el-option label="启用" value="active" />
                <el-option label="禁用" value="disabled" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'QuestionAdd',
  data() {
    return {
      saving: false,
      questionForm: {
        question_type: '',
        difficulty: 'medium',
        category_id: [],
        tags: [],
        question_content: '',
        options: [
          { content: '' },
          { content: '' }
        ],
        correct_answer: '',
        explanation: '',
        score: 5,
        sort_order: 0,
        status: 'active'
      },
      categoryOptions: [],
      tagOptions: [],
      categoryProps: {
        value: 'category_id',
        label: 'category_name',
        children: 'children',
        emitPath: false
      },
      rules: {
        question_type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
        difficulty: [{ required: true, message: '请选择难度等级', trigger: 'change' }],
        category_id: [{ required: true, message: '请选择题目分类', trigger: 'change' }],
        question_content: [{ required: true, message: '请输入题目内容', trigger: 'blur' }],
        correct_answer: [{ required: true, message: '请设置正确答案', trigger: 'change' }]
      }
    }
  },
  created() {
    this.loadCategoryOptions()
    this.loadTagOptions()
  },
  methods: {
    loadCategoryOptions() {
      // 模拟分类数据
      this.categoryOptions = [
        {
          category_id: 1,
          category_name: '马克思主义基本原理',
          children: [
            {
              category_id: 11,
              category_name: '马克思主义哲学',
              children: [
                { category_id: 111, category_name: '唯物论' },
                { category_id: 112, category_name: '辩证法' },
                { category_id: 113, category_name: '认识论' }
              ]
            },
            { category_id: 12, category_name: '马克思主义政治经济学' },
            { category_id: 13, category_name: '科学社会主义' }
          ]
        },
        {
          category_id: 2,
          category_name: '毛泽东思想和中国特色社会主义理论体系概论',
          children: [
            { category_id: 21, category_name: '毛泽东思想' },
            { category_id: 22, category_name: '邓小平理论' },
            { category_id: 23, category_name: '三个代表重要思想' }
          ]
        },
        {
          category_id: 3,
          category_name: '中国近现代史纲要',
          children: [
            { category_id: 31, category_name: '旧民主主义革命时期' },
            { category_id: 32, category_name: '新民主主义革命时期' },
            { category_id: 33, category_name: '社会主义革命和建设时期' }
          ]
        },
        {
          category_id: 4,
          category_name: '思想道德与法治'
        },
        {
          category_id: 5,
          category_name: '形势与政策'
        }
      ]
    },
    loadTagOptions() {
      // 模拟标签数据
      this.tagOptions = [
        { tag_id: 1, tag_name: '马克思主义基本原理' },
        { tag_id: 2, tag_name: '毛泽东思想' },
        { tag_id: 3, tag_name: '中国近现代史纲要' },
        { tag_id: 4, tag_name: '思想道德与法治' },
        { tag_id: 5, tag_name: '形势与政策' },
        { tag_id: 6, tag_name: '重点难点' },
        { tag_id: 7, tag_name: '高频考点' },
        { tag_id: 8, tag_name: '易错题' }
      ]
    },
    addOption() {
      if (this.questionForm.options.length < 6) {
        this.questionForm.options.push({ content: '' })
      }
    },
    removeOption(index) {
      if (this.questionForm.options.length > 2) {
        this.questionForm.options.splice(index, 1)
        // 重置答案选择
        this.questionForm.correct_answer = ''
      }
    },
    handleSave() {
      this.$refs.questionForm.validate((valid) => {
        if (valid) {
          this.saving = true

          // 模拟保存
          setTimeout(() => {
            this.saving = false
            this.$notify({
              title: '成功',
              message: '题目添加成功',
              type: 'success',
              duration: 2000
            })

            // 跳转到题目列表
            this.$router.push('/questions/list')
          }, 1000)
        } else {
          this.$message.error('请完善题目信息')
        }
      })
    },
    handleCancel() {
      this.$confirm('确定要取消添加题目吗？未保存的数据将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.push('/questions/list')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .form-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .question-form {
      padding: 20px 0;

      .options-container {
        .option-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .option-input {
            flex: 1;
            margin-right: 10px;
          }
        }
      }
    }
  }
}
</style>
