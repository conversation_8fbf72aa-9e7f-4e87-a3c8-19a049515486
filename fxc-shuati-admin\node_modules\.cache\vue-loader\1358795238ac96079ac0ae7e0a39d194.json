{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue?vue&type=template&id=414b322e&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue", "mtime": 1752571865094}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDwhLS0g5pCc57Si5qCPIC0tPgogIDxkaXYgY2xhc3M9ImZpbHRlci1jb250YWluZXIiPgogICAgPGVsLWlucHV0CiAgICAgIHYtbW9kZWw9Imxpc3RRdWVyeS5rZXl3b3JkIgogICAgICBwbGFjZWhvbGRlcj0i5pCc57Si6I+c5Y2V5ZCN56ew44CB6Lev5b6EIgogICAgICBzdHlsZT0id2lkdGg6IDIwMHB4OyIKICAgICAgY2xhc3M9ImZpbHRlci1pdGVtIgogICAgICBAa2V5dXAuZW50ZXIubmF0aXZlPSJoYW5kbGVGaWx0ZXIiCiAgICAvPgogICAgPGVsLXNlbGVjdAogICAgICB2LW1vZGVsPSJsaXN0UXVlcnkuc3RhdHVzIgogICAgICBwbGFjZWhvbGRlcj0i54q25oCBIgogICAgICBjbGVhcmFibGUKICAgICAgc3R5bGU9IndpZHRoOiAxMjBweCIKICAgICAgY2xhc3M9ImZpbHRlci1pdGVtIgogICAgPgogICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlkK/nlKgiIHZhbHVlPSJhY3RpdmUiIC8+CiAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuemgeeUqCIgdmFsdWU9ImRpc2FibGVkIiAvPgogICAgPC9lbC1zZWxlY3Q+CiAgICA8ZWwtYnV0dG9uCiAgICAgIHYtd2F2ZXMKICAgICAgY2xhc3M9ImZpbHRlci1pdGVtIgogICAgICB0eXBlPSJwcmltYXJ5IgogICAgICBpY29uPSJlbC1pY29uLXNlYXJjaCIKICAgICAgQGNsaWNrPSJoYW5kbGVGaWx0ZXIiCiAgICA+CiAgICAgIOaQnOe0ogogICAgPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uCiAgICAgIGNsYXNzPSJmaWx0ZXItaXRlbSIKICAgICAgc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4OyIKICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgaWNvbj0iZWwtaWNvbi1wbHVzIgogICAgICBAY2xpY2s9ImhhbmRsZUNyZWF0ZSIKICAgID4KICAgICAg5re75Yqg6I+c5Y2VCiAgICA8L2VsLWJ1dHRvbj4KICA8L2Rpdj4KCiAgPCEtLSDooajmoLwgLS0+CiAgPGVsLXRhYmxlCiAgICA6a2V5PSJ0YWJsZUtleSIKICAgIHYtbG9hZGluZz0ibGlzdExvYWRpbmciCiAgICA6ZGF0YT0ibGlzdCIKICAgIGJvcmRlcgogICAgZml0CiAgICBoaWdobGlnaHQtY3VycmVudC1yb3cKICAgIHN0eWxlPSJ3aWR0aDogMTAwJTsiCiAgICByb3cta2V5PSJtZW51X2lkIgogICAgZGVmYXVsdC1leHBhbmQtYWxsCiAgPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0iSUQiIHByb3A9Im1lbnVfaWQiIGFsaWduPSJjZW50ZXIiIHdpZHRoPSI4MCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuiPnOWNleWQjeensCIgcHJvcD0ibWVudV9uYW1lIiB3aWR0aD0iMTUwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i6I+c5Y2V6Lev5b6EIiBwcm9wPSJtZW51X3BhdGgiIHdpZHRoPSIxODAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLoj5zljZXlm77moIciIHdpZHRoPSIxMDAiIGFsaWduPSJjZW50ZXIiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ie3Jvd30iPgogICAgICAgIDxpIHYtaWY9InJvdy5tZW51X2ljb24iIDpjbGFzcz0icm93Lm1lbnVfaWNvbiIgLz4KICAgICAgICA8c3BhbiB2LWVsc2U+LTwvc3Bhbj4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i57uE5Lu26Lev5b6EIiBwcm9wPSJjb21wb25lbnQiIHdpZHRoPSIxODAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLniLboj5zljZUiIHByb3A9InBhcmVudF9uYW1lIiB3aWR0aD0iMTUwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i54q25oCBIiB3aWR0aD0iMTAwIiBhbGlnbj0iY2VudGVyIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9Intyb3d9Ij4KICAgICAgICA8ZWwtdGFnIDp0eXBlPSJyb3cuc3RhdHVzID09PSAnYWN0aXZlJyA/ICdzdWNjZXNzJyA6ICdpbmZvJyI+CiAgICAgICAgICB7eyByb3cuc3RhdHVzID09PSAnYWN0aXZlJyA/ICflkK/nlKgnIDogJ+emgeeUqCcgfX0KICAgICAgICA8L2VsLXRhZz4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5o6S5bqPIiBwcm9wPSJzb3J0X29yZGVyIiB3aWR0aD0iODAiIGFsaWduPSJjZW50ZXIiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmk43kvZwiIGFsaWduPSJjZW50ZXIiIHdpZHRoPSIyMDAiIGNsYXNzLW5hbWU9InNtYWxsLXBhZGRpbmcgZml4ZWQtd2lkdGgiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ie3Jvd30iPgogICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgc2l6ZT0ibWluaSIgQGNsaWNrPSJoYW5kbGVVcGRhdGUocm93KSI+CiAgICAgICAgICDnvJbovpEKICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0iZGFuZ2VyIgogICAgICAgICAgQGNsaWNrPSJoYW5kbGVEZWxldGUocm93KSIKICAgICAgICA+CiAgICAgICAgICDliKDpmaQKICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogIDwvZWwtdGFibGU+CgogIDwhLS0g5YiG6aG1IC0tPgogIDxwYWdpbmF0aW9uCiAgICB2LXNob3c9InRvdGFsPjAiCiAgICA6dG90YWw9InRvdGFsIgogICAgOnBhZ2Uuc3luYz0ibGlzdFF1ZXJ5LnBhZ2UiCiAgICA6bGltaXQuc3luYz0ibGlzdFF1ZXJ5LnBhZ2VTaXplIgogICAgQHBhZ2luYXRpb249ImdldExpc3QiCiAgLz4KCiAgPCEtLSDmt7vliqAv57yW6L6R5a+56K+d5qGGIC0tPgogIDxlbC1kaWFsb2cgOnRpdGxlPSJ0ZXh0TWFwW2RpYWxvZ1N0YXR1c10iIDp2aXNpYmxlLnN5bmM9ImRpYWxvZ0Zvcm1WaXNpYmxlIj4KICAgIDxlbC1mb3JtCiAgICAgIHJlZj0iZGF0YUZvcm0iCiAgICAgIDpydWxlcz0icnVsZXMiCiAgICAgIDptb2RlbD0idGVtcCIKICAgICAgbGFiZWwtcG9zaXRpb249ImxlZnQiCiAgICAgIGxhYmVsLXdpZHRoPSIxMjBweCIKICAgICAgc3R5bGU9IndpZHRoOiA0NTBweDsgbWFyZ2luLWxlZnQ6NTBweDsiCiAgICA+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IueItuiPnOWNlSIgcHJvcD0icGFyZW50X2lkIj4KICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9InRlbXAucGFyZW50X2lkIiBwbGFjZWhvbGRlcj0i6K+36YCJ5oup54i26I+c5Y2VIiBzdHlsZT0id2lkdGg6IDEwMCUiPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i6aG257qn6I+c5Y2VIiA6dmFsdWU9IjAiIC8+CiAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHBhcmVudE9wdGlvbnMiCiAgICAgICAgICAgIDprZXk9Iml0ZW0ubWVudV9pZCIKICAgICAgICAgICAgOmxhYmVsPSJpdGVtLm1lbnVfbmFtZSIKICAgICAgICAgICAgOnZhbHVlPSJpdGVtLm1lbnVfaWQiCiAgICAgICAgICAvPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6I+c5Y2V5ZCN56ewIiBwcm9wPSJtZW51X25hbWUiPgogICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJ0ZW1wLm1lbnVfbmFtZSIgLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuiPnOWNlei3r+W+hCIgcHJvcD0ibWVudV9wYXRoIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0idGVtcC5tZW51X3BhdGgiIC8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnu4Tku7bot6/lvoQiIHByb3A9ImNvbXBvbmVudCI+CiAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9InRlbXAuY29tcG9uZW50IiAvPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6I+c5Y2V5Zu+5qCHIiBwcm9wPSJtZW51X2ljb24iPgogICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJ0ZW1wLm1lbnVfaWNvbiIgLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IueKtuaAgSIgcHJvcD0ic3RhdHVzIj4KICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9InRlbXAuc3RhdHVzIiBwbGFjZWhvbGRlcj0i6K+36YCJ5oup54q25oCBIiBzdHlsZT0id2lkdGg6IDEwMCUiPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5ZCv55SoIiB2YWx1ZT0iYWN0aXZlIiAvPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i56aB55SoIiB2YWx1ZT0iZGlzYWJsZWQiIC8+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmjpLluo8iIHByb3A9InNvcnRfb3JkZXIiPgogICAgICAgIDxlbC1pbnB1dC1udW1iZXIgdi1tb2RlbD0idGVtcC5zb3J0X29yZGVyIiA6bWluPSIwIiA6bWF4PSI5OTkiIHN0eWxlPSJ3aWR0aDogMTAwJSIgLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8L2VsLWZvcm0+CiAgICA8ZGl2IHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZSI+CiAgICAgICAg5Y+W5raICiAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0iZGlhbG9nU3RhdHVzPT09J2NyZWF0ZSc/Y3JlYXRlRGF0YSgpOnVwZGF0ZURhdGEoKSI+CiAgICAgICAg56Gu5a6aCiAgICAgIDwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CjwvZGl2Pgo="}, null]}