{"_from": "kind-of@^3.0.2", "_id": "kind-of@3.2.2", "_inBundle": false, "_integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "_location": "/is-number/kind-of", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "kind-of@^3.0.2", "name": "kind-of", "escapedName": "kind-of", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/is-number"], "_resolved": "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz", "_shasum": "31ea21a734bab9bbb0f32466d893aea51e4a3c64", "_spec": "kind-of@^3.0.2", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "dependencies": {"is-buffer": "^1.1.5"}, "deprecated": false, "description": "Get the native type of a value.", "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.0.0", "browserify": "^14.3.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.3.0", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/kind-of", "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "license": "MIT", "main": "index.js", "name": "kind-of", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "scripts": {"prepublish": "browserify -o browser.js -e index.js -s index --bare", "test": "mocha"}, "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "version": "3.2.2"}