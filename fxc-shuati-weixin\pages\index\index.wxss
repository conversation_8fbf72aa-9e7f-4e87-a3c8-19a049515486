/**index.wxss**/

/* 轮播图 */
.banner {
  height: 300rpx;
  margin-bottom: 20rpx;
}

.banner-item {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  position: relative;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 功能菜单 */
.menu-section {
  margin-bottom: 20rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  text-align: center;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.menu-text {
  font-size: 24rpx;
  color: #333;
}

/* 学习统计 */
.stats-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 最近练习 */
.recent-section {
  margin-bottom: 20rpx;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.recent-info {
  flex: 1;
  margin-right: 20rpx;
}

.recent-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.recent-desc {
  font-size: 24rpx;
  color: #666;
}

.recent-progress {
  width: 200rpx;
  text-align: right;
}

.progress-text {
  font-size: 24rpx;
  color: #409EFF;
  margin-bottom: 10rpx;
  display: block;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #666;
}
