{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\AppMain.vue?vue&type=style&index=0&id=078753dd&scoped=true&lang=css", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\AppMain.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgouYXBwLW1haW4gewogIC8qNTAgPSBuYXZiYXIgICovCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDUwcHgpOwogIHdpZHRoOiAxMDAlOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBvdmVyZmxvdzogaGlkZGVuOwp9Ci5maXhlZC1oZWFkZXIrLmFwcC1tYWluIHsKICBwYWRkaW5nLXRvcDogNTBweDsKfQo="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <section class=\"app-main\">\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <router-view :key=\"key\" />\n    </transition>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'AppMain',\n  computed: {\n    key() {\n      return this.$route.path\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-main {\n  /*50 = navbar  */\n  min-height: calc(100vh - 50px);\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n.fixed-header+.app-main {\n  padding-top: 50px;\n}\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 15px;\n  }\n}\n</style>\n"]}]}