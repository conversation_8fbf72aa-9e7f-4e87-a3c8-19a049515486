<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索标签名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加标签
        </el-button>
      </div>
    </el-card>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="tag_id" align="center" width="80" />
      <el-table-column label="标签名称" prop="tag_name" min-width="150" />
      <el-table-column label="标签颜色" width="120" align="center">
        <template slot-scope="{row}">
          <el-tag :color="row.tag_color" style="color: white;">
            {{ row.tag_name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="标签描述" prop="tag_desc" min-width="200" />
      <el-table-column label="题目数量" prop="question_count" width="100" align="center" />
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort_order" width="80" align="center" />
      <el-table-column label="创建时间" min-width="120" align="center">
        <template slot-scope="{row}">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="标签名称" prop="tag_name">
          <el-input v-model="temp.tag_name" />
        </el-form-item>
        <el-form-item label="标签颜色" prop="tag_color">
          <el-color-picker v-model="temp.tag_color" />
        </el-form-item>
        <el-form-item label="标签描述" prop="tag_desc">
          <el-input v-model="temp.tag_desc" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择状态">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="temp.sort_order" :min="0" :max="999" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { formatDate } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'QuestionTags',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 20,
        keyword: '',
        status: ''
      },
      temp: {
        tag_id: undefined,
        tag_name: '',
        tag_color: '#409EFF',
        tag_desc: '',
        status: 'active',
        sort_order: 0
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑标签',
        create: '添加标签'
      },
      rules: {
        tag_name: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      
      // 模拟考研政治标签数据
      setTimeout(() => {
        this.list = [
          // 学科标签
          {
            tag_id: 1,
            tag_name: '马克思主义基本原理',
            tag_color: '#E74C3C',
            tag_desc: '马克思主义基本原理概论相关题目',
            question_count: 245,
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-01'
          },
          {
            tag_id: 2,
            tag_name: '毛泽东思想',
            tag_color: '#F39C12',
            tag_desc: '毛泽东思想和中国特色社会主义理论体系概论',
            question_count: 312,
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-01'
          },
          {
            tag_id: 3,
            tag_name: '中国近现代史纲要',
            tag_color: '#27AE60',
            tag_desc: '中国近现代史纲要相关题目',
            question_count: 198,
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-01'
          },
          {
            tag_id: 4,
            tag_name: '思想道德与法治',
            tag_color: '#3498DB',
            tag_desc: '思想道德与法治相关题目',
            question_count: 167,
            status: 'active',
            sort_order: 4,
            created_at: '2024-01-01'
          },
          {
            tag_id: 5,
            tag_name: '形势与政策',
            tag_color: '#9B59B6',
            tag_desc: '形势与政策以及当代世界经济与政治',
            question_count: 123,
            status: 'active',
            sort_order: 5,
            created_at: '2024-01-01'
          },
          // 重要性标签
          {
            tag_id: 6,
            tag_name: '重点难点',
            tag_color: '#E67E22',
            tag_desc: '标记为重点难点的题目',
            question_count: 156,
            status: 'active',
            sort_order: 6,
            created_at: '2024-01-02'
          },
          {
            tag_id: 7,
            tag_name: '高频考点',
            tag_color: '#C0392B',
            tag_desc: '历年考试中的高频考点',
            question_count: 234,
            status: 'active',
            sort_order: 7,
            created_at: '2024-01-02'
          },
          {
            tag_id: 8,
            tag_name: '易错题',
            tag_color: '#8E44AD',
            tag_desc: '学生容易出错的题目',
            question_count: 189,
            status: 'active',
            sort_order: 8,
            created_at: '2024-01-02'
          },
          // 专题标签
          {
            tag_id: 9,
            tag_name: '哲学原理',
            tag_color: '#2C3E50',
            tag_desc: '马克思主义哲学基本原理',
            question_count: 145,
            status: 'active',
            sort_order: 9,
            created_at: '2024-01-03'
          },
          {
            tag_id: 10,
            tag_name: '政治经济学',
            tag_color: '#16A085',
            tag_desc: '马克思主义政治经济学原理',
            question_count: 78,
            status: 'active',
            sort_order: 10,
            created_at: '2024-01-03'
          },
          {
            tag_id: 11,
            tag_name: '科学社会主义',
            tag_color: '#D35400',
            tag_desc: '科学社会主义基本原理',
            question_count: 67,
            status: 'active',
            sort_order: 11,
            created_at: '2024-01-03'
          },
          {
            tag_id: 12,
            tag_name: '新民主主义革命',
            tag_color: '#8B4513',
            tag_desc: '新民主主义革命理论',
            question_count: 89,
            status: 'active',
            sort_order: 12,
            created_at: '2024-01-03'
          },
          {
            tag_id: 13,
            tag_name: '社会主义建设',
            tag_color: '#FF6347',
            tag_desc: '社会主义革命和建设',
            question_count: 112,
            status: 'active',
            sort_order: 13,
            created_at: '2024-01-03'
          },
          {
            tag_id: 14,
            tag_name: '改革开放',
            tag_color: '#4169E1',
            tag_desc: '改革开放和现代化建设',
            question_count: 134,
            status: 'active',
            sort_order: 14,
            created_at: '2024-01-03'
          },
          {
            tag_id: 15,
            tag_name: '新时代',
            tag_color: '#FF1493',
            tag_desc: '新时代中国特色社会主义',
            question_count: 98,
            status: 'active',
            sort_order: 15,
            created_at: '2024-01-03'
          },
          // 年份标签
          {
            tag_id: 16,
            tag_name: '2023年真题',
            tag_color: '#32CD32',
            tag_desc: '2023年考研政治真题',
            question_count: 45,
            status: 'active',
            sort_order: 16,
            created_at: '2024-01-04'
          },
          {
            tag_id: 17,
            tag_name: '2022年真题',
            tag_color: '#20B2AA',
            tag_desc: '2022年考研政治真题',
            question_count: 45,
            status: 'active',
            sort_order: 17,
            created_at: '2024-01-04'
          },
          {
            tag_id: 18,
            tag_name: '模拟题',
            tag_color: '#778899',
            tag_desc: '模拟考试题目',
            question_count: 267,
            status: 'active',
            sort_order: 18,
            created_at: '2024-01-04'
          }
        ]
        this.total = this.list.length
        this.listLoading = false
      }, 500)
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        tag_id: undefined,
        tag_name: '',
        tag_color: '#409EFF',
        tag_desc: '',
        status: 'active',
        sort_order: 0
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$notify({
            title: '成功',
            message: '创建成功（模拟）',
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$notify({
            title: '成功',
            message: '更新成功（模拟）',
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该标签吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$notify({
          title: '成功',
          message: '删除成功（模拟）',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-card {
    margin-bottom: 20px;
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .filter-item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  .el-table {
    margin-bottom: 20px;
    width: 100% !important;
    
    .el-table__body-wrapper {
      width: 100% !important;
    }
  }
  .pagination-container {
    padding: 15px 0;
  }
}
</style>
