const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function initDatabase() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '*************',
      user: process.env.DB_USER || 'fxc-shuati',
      password: process.env.DB_PASSWORD || 'haf6LeMjHLKefKs7',
      port: process.env.DB_PORT || 3306,
      multipleStatements: true
    });

    console.log('数据库连接成功');

    // 读取SQL文件
    const sqlFile = path.join(__dirname, '../config/init.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');

    // 首先创建数据库
    console.log('创建数据库...');
    await connection.query('CREATE DATABASE IF NOT EXISTS `fxc-shuati` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');

    // 使用数据库
    await connection.query('USE `fxc-shuati`');

    // 读取并执行剩余的SQL（去掉前面的创建数据库部分）
    console.log('开始执行数据库初始化脚本...');
    const sqlLines = sqlContent.split('\n');
    let startIndex = 0;
    for (let i = 0; i < sqlLines.length; i++) {
      if (sqlLines[i].includes('-- 用户表')) {
        startIndex = i;
        break;
      }
    }

    const mainSql = sqlLines.slice(startIndex).join('\n');

    // 分割SQL语句并逐个执行
    const statements = mainSql.split(';').filter(stmt => stmt.trim().length > 0);
    for (const statement of statements) {
      if (statement.trim()) {
        await connection.query(statement + ';');
      }
    }
    console.log('数据库初始化完成！');

    // 验证数据
    const [adminRows] = await connection.query('SELECT COUNT(*) as count FROM admins');
    console.log(`管理员表记录数: ${adminRows[0].count}`);

    const [roleRows] = await connection.query('SELECT COUNT(*) as count FROM roles');
    console.log(`角色表记录数: ${roleRows[0].count}`);

    const [permissionRows] = await connection.query('SELECT COUNT(*) as count FROM permissions');
    console.log(`权限表记录数: ${permissionRows[0].count}`);

    const [categoryRows] = await connection.query('SELECT COUNT(*) as count FROM categories');
    console.log(`分类表记录数: ${categoryRows[0].count}`);

    console.log('\n默认管理员账号信息:');
    console.log('用户名: admin');
    console.log('密码: 123456');
    console.log('\n数据库初始化成功！');

  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
