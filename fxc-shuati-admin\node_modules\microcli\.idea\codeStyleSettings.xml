<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectCodeStyleSettingsManager">
    <option name="PER_PROJECT_SETTINGS">
      <value>
        <JSCodeStyleSettings>
          <option name="USE_SEMICOLON_AFTER_STATEMENT" value="false" />
          <option name="FORCE_SEMICOLON_STYLE" value="true" />
          <option name="SPACE_BEFORE_GENERATOR_MULT" value="true" />
          <option name="USE_DOUBLE_QUOTES" value="false" />
          <option name="FORCE_QUOTE_STYlE" value="true" />
          <option name="SPACES_WITHIN_IMPORTS" value="true" />
        </JSCodeStyleSettings>
        <XML>
          <option name="XML_LEGACY_SETTINGS_IMPORTED" value="true" />
        </XML>
        <codeStyleSettings language="JavaScript">
          <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
          <option name="SPACE_BEFORE_METHOD_PARENTHESES" value="true" />
          <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
          <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
          <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
          <indentOptions>
            <option name="INDENT_SIZE" value="2" />
            <option name="CONTINUATION_INDENT_SIZE" value="2" />
            <option name="TAB_SIZE" value="2" />
          </indentOptions>
        </codeStyleSettings>
      </value>
    </option>
    <option name="USE_PER_PROJECT_SETTINGS" value="true" />
  </component>
</project>