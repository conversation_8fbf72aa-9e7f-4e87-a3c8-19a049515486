// profile.js
const { api } = require('../../utils/api.js')
const { setStorage, getStorage, showSuccess, showError } = require('../../utils/util.js')

const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'

Page({
  data: {
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '',
      signature: ''
    },
    userStats: {
      totalQuestions: 0,
      correctRate: 0,
      studyDays: 0,
      rank: 0,
      favoriteCount: 0,
      errorCount: 0,
      achievementCount: 0
    },
    chartData: [],
    showEditPopup: false,
    editForm: {
      signature: ''
    }
  },

  onLoad() {
    console.log('个人中心页面加载')
    this.loadUserInfo()
    this.loadUserStats()
    this.loadChartData()
  },

  onShow() {
    console.log('个人中心页面显示')
    // 页面显示时刷新统计数据
    this.loadUserStats()
  },

  // 加载用户信息
  loadUserInfo() {
    // 从本地存储获取用户信息
    const userInfo = getStorage('userInfo', {
      avatarUrl: defaultAvatarUrl,
      nickName: '',
      signature: ''
    })
    
    this.setData({
      userInfo
    })

    // 实际API调用示例
    // api.getUserInfo().then(res => {
    //   const userInfo = res.data
    //   this.setData({
    //     userInfo
    //   })
    //   setStorage('userInfo', userInfo)
    // }).catch(err => {
    //   console.error('获取用户信息失败', err)
    // })
  },

  // 加载用户统计数据
  loadUserStats() {
    // 使用mock数据
    this.setData({
      userStats: {
        totalQuestions: 156,
        correctRate: 78,
        studyDays: 15,
        rank: 128,
        favoriteCount: 23,
        errorCount: 12,
        achievementCount: 5
      }
    })

    // 实际API调用示例
    // api.getUserStats().then(res => {
    //   this.setData({
    //     userStats: res.data
    //   })
    // }).catch(err => {
    //   console.error('获取用户统计失败', err)
    // })
  },

  // 加载图表数据
  loadChartData() {
    // 生成最近7天的mock数据
    const chartData = []
    const today = new Date()
    const maxCount = 50
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      
      const count = Math.floor(Math.random() * maxCount)
      const height = count > 0 ? Math.max((count / maxCount) * 100, 10) : 0
      
      chartData.push({
        date: date.toISOString().split('T')[0],
        label: i === 0 ? '今天' : `${date.getMonth() + 1}/${date.getDate()}`,
        count,
        height
      })
    }
    
    this.setData({
      chartData
    })
  },

  // 选择头像
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    this.setData({
      'userInfo.avatarUrl': avatarUrl
    })
    this.saveUserInfo()
  },

  // 修改昵称
  onNicknameChange(e) {
    const nickName = e.detail.value
    this.setData({
      'userInfo.nickName': nickName
    })
    this.saveUserInfo()
  },

  // 保存用户信息
  saveUserInfo() {
    const { userInfo } = this.data
    setStorage('userInfo', userInfo)
    
    // 实际API调用示例
    // api.updateUserInfo(userInfo).then(() => {
    //   showSuccess('保存成功')
    // }).catch(err => {
    //   console.error('保存用户信息失败', err)
    //   showError('保存失败')
    // })
  },

  // 编辑资料
  editProfile() {
    this.setData({
      'editForm.signature': this.data.userInfo.signature,
      showEditPopup: true
    })
  },

  // 个性签名输入
  onSignatureInput(e) {
    this.setData({
      'editForm.signature': e.detail.value
    })
  },

  // 取消编辑
  cancelEdit() {
    this.setData({
      showEditPopup: false
    })
  },

  // 保存资料
  saveProfile() {
    const { editForm } = this.data
    
    this.setData({
      'userInfo.signature': editForm.signature,
      showEditPopup: false
    })
    
    this.saveUserInfo()
    showSuccess('保存成功')
  },

  // 跳转到收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 跳转到错题本
  goToErrors() {
    wx.navigateTo({
      url: '/pages/errors/errors'
    })
  },

  // 跳转到练习历史
  goToHistory() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 跳转到成就徽章
  goToAchievements() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 跳转到设置
  goToSettings() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 跳转到意见反馈
  goToFeedback() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 跳转到关于我们
  goToAbout() {
    wx.showModal({
      title: '关于我们',
      content: '刷题小程序 v1.0.0\n\n一个专业的考研英语刷题平台，帮助你高效备考，轻松上岸！\n\n如有问题请联系客服。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享小程序
  onShareAppMessage() {
    const { userStats } = this.data
    return {
      title: `我在刷题小程序已经练习了${userStats.totalQuestions}道题，正确率${userStats.correctRate}%！`,
      path: '/pages/index/index',
      imageUrl: '' // 可以设置分享图片
    }
  }
})
