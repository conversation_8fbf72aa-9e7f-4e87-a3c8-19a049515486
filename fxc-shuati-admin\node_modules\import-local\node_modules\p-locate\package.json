{"_from": "p-locate@^3.0.0", "_id": "p-locate@3.0.0", "_inBundle": false, "_integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "_location": "/import-local/p-locate", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-locate@^3.0.0", "name": "p-locate", "escapedName": "p-locate", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/import-local/locate-path"], "_resolved": "https://registry.npmmirror.com/p-locate/-/p-locate-3.0.0.tgz", "_shasum": "322d69a05c0264b25997d9f40cd8a891ab0064a4", "_spec": "p-locate@^3.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\import-local\\node_modules\\locate-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-locate/issues"}, "bundleDependencies": false, "dependencies": {"p-limit": "^2.0.0"}, "deprecated": false, "description": "Get the first fulfilled promise that satisfies the provided testing function", "devDependencies": {"ava": "*", "delay": "^3.0.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "xo": "*"}, "engines": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/p-locate#readme", "keywords": ["promise", "locate", "find", "finder", "search", "searcher", "test", "array", "collection", "iterable", "iterator", "race", "fulfilled", "fastest", "async", "await", "promises", "bluebird"], "license": "MIT", "name": "p-locate", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-locate.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}