const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'fxc-shuati-secret-key';

// 管理员登录
router.post('/admin/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查询管理员
    const admins = await query(
      'SELECT * FROM admins WHERE username = ? AND status = ?',
      [username, 'active']
    );

    if (admins.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const admin = admins[0];

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, admin.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 更新最后登录时间
    await query(
      'UPDATE admins SET last_login_time = NOW() WHERE admin_id = ?',
      [admin.admin_id]
    );

    // 生成JWT token
    const token = jwt.sign(
      {
        admin_id: admin.admin_id,
        username: admin.username,
        role: admin.role
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        admin: {
          admin_id: admin.admin_id,
          username: admin.username,
          real_name: admin.real_name,
          email: admin.email,
          role: admin.role
        }
      }
    });

  } catch (error) {
    console.error('管理员登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 微信小程序用户登录
router.post('/wechat/login', async (req, res) => {
  try {
    const { openid, nickname = '', avatar_url = '' } = req.body;

    if (!openid) {
      return res.status(400).json({
        success: false,
        message: 'openid不能为空'
      });
    }

    // 查询用户是否存在
    let users = await query(
      'SELECT * FROM users WHERE openid = ?',
      [openid]
    );

    let user;
    if (users.length === 0) {
      // 新用户，创建账号
      const result = await query(
        'INSERT INTO users (openid, nickname, avatar_url) VALUES (?, ?, ?)',
        [openid, nickname, avatar_url]
      );
      
      user = {
        user_id: result.insertId,
        openid,
        nickname,
        avatar_url,
        is_vip: false,
        status: 'active'
      };
    } else {
      user = users[0];
      
      // 更新用户信息
      if (nickname || avatar_url) {
        await query(
          'UPDATE users SET nickname = ?, avatar_url = ?, updated_at = NOW() WHERE user_id = ?',
          [nickname || user.nickname, avatar_url || user.avatar_url, user.user_id]
        );
        user.nickname = nickname || user.nickname;
        user.avatar_url = avatar_url || user.avatar_url;
      }
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        user_id: user.user_id,
        openid: user.openid,
        is_vip: user.is_vip
      },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          user_id: user.user_id,
          openid: user.openid,
          nickname: user.nickname,
          avatar_url: user.avatar_url,
          is_vip: user.is_vip,
          status: user.status
        }
      }
    });

  } catch (error) {
    console.error('微信登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 验证token中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未提供访问令牌'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: '访问令牌无效'
      });
    }
    req.user = decoded;
    next();
  });
};

// 验证管理员权限中间件
const authenticateAdmin = (req, res, next) => {
  authenticateToken(req, res, () => {
    if (!req.user.admin_id) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }
    next();
  });
};

module.exports = {
  router,
  authenticateToken,
  authenticateAdmin
};
