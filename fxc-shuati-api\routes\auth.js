const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'fxc-shuati-secret-key';

// 管理员登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查找管理员及其角色权限
    const adminSql = `
      SELECT a.admin_id, a.username, a.real_name, a.email, a.phone, a.avatar, a.status, a.password,
             GROUP_CONCAT(DISTINCT r.role_id) as role_ids,
             GROUP_CONCAT(DISTINCT r.role_name) as role_names,
             GROUP_CONCAT(DISTINCT r.role_code) as role_codes
      FROM admins a
      LEFT JOIN admin_roles ar ON a.admin_id = ar.admin_id
      LEFT JOIN roles r ON ar.role_id = r.role_id AND r.status = 'active'
      WHERE a.username = ?
      GROUP BY a.admin_id
    `;

    const adminResult = await query(adminSql, [username]);

    if (adminResult.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const adminData = adminResult[0];

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, adminData.password);

    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 检查账号状态
    if (adminData.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '账号已被禁用'
      });
    }

    // 获取用户权限
    const permissionSql = `
      SELECT DISTINCT p.permission_code, p.permission_type, p.permission_url
      FROM permissions p
      JOIN role_permissions rp ON p.permission_id = rp.permission_id
      JOIN admin_roles ar ON rp.role_id = ar.role_id
      WHERE ar.admin_id = ? AND p.status = 'active'
    `;

    const permissions = await query(permissionSql, [adminData.admin_id]);

    // 更新最后登录时间和IP
    const clientIp = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    await query(
      'UPDATE admins SET last_login_time = NOW(), last_login_ip = ? WHERE admin_id = ?',
      [clientIp, adminData.admin_id]
    );

    // 生成JWT token
    const token = jwt.sign(
      {
        admin_id: adminData.admin_id,
        username: adminData.username,
        role_ids: adminData.role_ids ? adminData.role_ids.split(',').map(id => parseInt(id)) : [],
        type: 'admin'
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        admin: {
          admin_id: adminData.admin_id,
          username: adminData.username,
          real_name: adminData.real_name,
          email: adminData.email,
          phone: adminData.phone,
          avatar: adminData.avatar,
          roles: adminData.role_names ? adminData.role_names.split(',') : [],
          role_codes: adminData.role_codes ? adminData.role_codes.split(',') : []
        },
        permissions: permissions.map(p => p.permission_code)
      }
    });
  } catch (error) {
    console.error('管理员登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 微信小程序用户登录
router.post('/wechat/login', async (req, res) => {
  try {
    const { openid, nickname = '', avatar_url = '' } = req.body;

    if (!openid) {
      return res.status(400).json({
        success: false,
        message: 'openid不能为空'
      });
    }

    // 查询用户是否存在
    let users = await query(
      'SELECT * FROM users WHERE openid = ?',
      [openid]
    );

    let user;
    if (users.length === 0) {
      // 新用户，创建账号
      const result = await query(
        'INSERT INTO users (openid, nickname, avatar_url) VALUES (?, ?, ?)',
        [openid, nickname, avatar_url]
      );
      
      user = {
        user_id: result.insertId,
        openid,
        nickname,
        avatar_url,
        is_vip: false,
        status: 'active'
      };
    } else {
      user = users[0];
      
      // 更新用户信息
      if (nickname || avatar_url) {
        await query(
          'UPDATE users SET nickname = ?, avatar_url = ?, updated_at = NOW() WHERE user_id = ?',
          [nickname || user.nickname, avatar_url || user.avatar_url, user.user_id]
        );
        user.nickname = nickname || user.nickname;
        user.avatar_url = avatar_url || user.avatar_url;
      }
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        user_id: user.user_id,
        openid: user.openid,
        is_vip: user.is_vip
      },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          user_id: user.user_id,
          openid: user.openid,
          nickname: user.nickname,
          avatar_url: user.avatar_url,
          is_vip: user.is_vip,
          status: user.status
        }
      }
    });

  } catch (error) {
    console.error('微信登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 获取当前用户信息
router.get('/info', async (req, res) => {
  try {
    console.log('获取用户信息请求头:', req.headers);
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      console.log('未提供认证令牌');
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    console.log('解析token:', token);
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('解析结果:', decoded);

    if (decoded.type !== 'admin') {
      console.log('无效的令牌类型:', decoded.type);
      return res.status(401).json({
        success: false,
        message: '无效的令牌类型'
      });
    }

    // 查找管理员及其角色权限
    const adminSql = `
      SELECT a.admin_id, a.username, a.real_name, a.email, a.phone, a.avatar, a.status,
             GROUP_CONCAT(DISTINCT r.role_id) as role_ids,
             GROUP_CONCAT(DISTINCT r.role_name) as role_names,
             GROUP_CONCAT(DISTINCT r.role_code) as role_codes
      FROM admins a
      LEFT JOIN admin_roles ar ON a.admin_id = ar.admin_id
      LEFT JOIN roles r ON ar.role_id = r.role_id AND r.status = 'active'
      WHERE a.admin_id = ? AND a.status = 'active'
      GROUP BY a.admin_id
    `;

    console.log('查询管理员SQL:', adminSql);
    console.log('管理员ID:', decoded.admin_id);
    const adminResult = await query(adminSql, [decoded.admin_id]);
    console.log('查询结果:', adminResult);

    if (adminResult.length === 0) {
      console.log('用户不存在或已被禁用');
      return res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用'
      });
    }

    const adminData = adminResult[0];
    console.log('管理员数据:', adminData);

    // 获取用户权限
    const permissionSql = `
      SELECT DISTINCT p.permission_code, p.permission_type, p.permission_url
      FROM permissions p
      JOIN role_permissions rp ON p.permission_id = rp.permission_id
      JOIN admin_roles ar ON rp.role_id = ar.role_id
      WHERE ar.admin_id = ? AND p.status = 'active'
    `;

    console.log('查询权限SQL:', permissionSql);
    const permissions = await query(permissionSql, [adminData.admin_id]);
    console.log('权限数据:', permissions);

    const responseData = {
      success: true,
      data: {
        admin: {
          admin_id: adminData.admin_id,
          username: adminData.username,
          real_name: adminData.real_name,
          email: adminData.email,
          phone: adminData.phone,
          avatar: adminData.avatar,
          roles: adminData.role_names ? adminData.role_names.split(',') : [],
          role_codes: adminData.role_codes ? adminData.role_codes.split(',') : []
        },
        permissions: permissions.map(p => p.permission_code)
      }
    };

    console.log('响应数据:', responseData);
    res.json(responseData);
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期'
      });
    }

    console.error('获取用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 退出登录
router.post('/logout', async (req, res) => {
  try {
    // 这里可以实现token黑名单机制
    res.json({
      success: true,
      message: '退出登录成功'
    });
  } catch (error) {
    console.error('退出登录失败:', error);
    res.status(500).json({
      success: false,
      message: '退出登录失败'
    });
  }
});

// 修改密码
router.post('/change-password', async (req, res) => {
  try {
    const { old_password, new_password } = req.body;
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    if (!old_password || !new_password) {
      return res.status(400).json({
        success: false,
        message: '旧密码和新密码不能为空'
      });
    }

    const decoded = jwt.verify(token, JWT_SECRET);

    // 查找管理员
    const admin = await query('SELECT password FROM admins WHERE admin_id = ?', [decoded.admin_id]);

    if (admin.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证旧密码
    const isValidPassword = await bcrypt.compare(old_password, admin[0].password);

    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        message: '旧密码错误'
      });
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(new_password, 10);

    // 更新密码
    await query(
      'UPDATE admins SET password = ?, updated_at = NOW() WHERE admin_id = ?',
      [hashedNewPassword, decoded.admin_id]
    );

    res.json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({
      success: false,
      message: '修改密码失败'
    });
  }
});

// 验证token中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未提供访问令牌'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: '访问令牌无效'
      });
    }
    req.user = decoded;
    next();
  });
};

// 验证管理员权限中间件
const authenticateAdmin = (req, res, next) => {
  authenticateToken(req, res, () => {
    if (!req.user.admin_id) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }
    next();
  });
};

module.exports = {
  router,
  authenticateToken,
  authenticateAdmin
};
