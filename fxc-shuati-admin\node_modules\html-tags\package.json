{"_from": "html-tags@^2.0.0", "_id": "html-tags@2.0.0", "_inBundle": false, "_integrity": "sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g==", "_location": "/html-tags", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "html-tags@^2.0.0", "name": "html-tags", "escapedName": "html-tags", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/@vue/babel-plugin-transform-vue-jsx", "/@vue/babel-sugar-v-model"], "_resolved": "https://registry.npmmirror.com/html-tags/-/html-tags-2.0.0.tgz", "_shasum": "10b30a386085f43cede353cc8fa7cb0deeea668b", "_spec": "html-tags@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\@vue\\babel-plugin-transform-vue-jsx", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/html-tags/issues"}, "bundleDependencies": false, "deprecated": false, "description": "List of standard HTML tags", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "void.js", "html-tags.json", "html-tags-void.json"], "homepage": "https://github.com/sindresorhus/html-tags#readme", "keywords": ["html", "html5", "tags", "elements", "list", "whatwg", "w3c", "void", "self-closing"], "license": "MIT", "name": "html-tags", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/html-tags.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0"}