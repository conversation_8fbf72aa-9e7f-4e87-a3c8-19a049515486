{"_from": "normalize-url@1.9.1", "_id": "normalize-url@1.9.1", "_inBundle": false, "_integrity": "sha512-A48My/mtCklowHBlI8Fq2jFWK4tX4lJ5E6ytFsSOq1fzpvT0SQSgKhSg7lN5c2uYFOrUAOQp6zhhJnpp1eMloQ==", "_location": "/mini-css-extract-plugin/normalize-url", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "normalize-url@1.9.1", "name": "normalize-url", "escapedName": "normalize-url", "rawSpec": "1.9.1", "saveSpec": null, "fetchSpec": "1.9.1"}, "_requiredBy": ["/mini-css-extract-plugin"], "_resolved": "https://registry.npmmirror.com/normalize-url/-/normalize-url-1.9.1.tgz", "_shasum": "2cc0d66b31ea23036458436e3620d85954c66c3c", "_spec": "normalize-url@1.9.1", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\mini-css-extract-plugin", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/normalize-url/issues"}, "bundleDependencies": false, "dependencies": {"object-assign": "^4.0.1", "prepend-http": "^1.0.0", "query-string": "^4.1.0", "sort-keys": "^1.0.0"}, "deprecated": false, "description": "Normalize a URL", "devDependencies": {"ava": "*", "xo": "^0.16.0"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/normalize-url#readme", "keywords": ["normalize", "url", "uri", "address", "string", "str", "normalise", "normalization", "normalisation", "query", "string", "querystring", "unicode", "simplify", "strip", "trim", "canonical"], "license": "MIT", "name": "normalize-url", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/normalize-url.git"}, "scripts": {"test": "xo && ava"}, "version": "1.9.1"}