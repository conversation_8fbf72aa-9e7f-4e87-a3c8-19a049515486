{"_from": "loader-utils@^0.2.16", "_id": "loader-utils@0.2.17", "_inBundle": false, "_integrity": "sha512-tiv66G0SmiOx+pLWMtGEkfSEejxvb6N6uRrQjfWJIT79W9GMpgKeCAmm9aVBKtd4WEgntciI8CsGqjpDoCWJug==", "_location": "/html-webpack-plugin/loader-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "loader-utils@^0.2.16", "name": "loader-utils", "escapedName": "loader-utils", "rawSpec": "^0.2.16", "saveSpec": null, "fetchSpec": "^0.2.16"}, "_requiredBy": ["/html-webpack-plugin"], "_resolved": "https://registry.npmmirror.com/loader-utils/-/loader-utils-0.2.17.tgz", "_shasum": "f86e6374d43205a6e6c60e9196f17c0299bfb348", "_spec": "loader-utils@^0.2.16", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\html-webpack-plugin", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/loader-utils/issues"}, "bundleDependencies": false, "dependencies": {"big.js": "^3.1.3", "emojis-list": "^2.0.0", "json5": "^0.5.0", "object-assign": "^4.0.1"}, "deprecated": false, "description": "utils for webpack loaders", "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.14", "mocha": "^1.21.4"}, "files": ["index.js"], "homepage": "https://github.com/webpack/loader-utils#readme", "license": "MIT", "name": "loader-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack/loader-utils.git"}, "scripts": {"cover": "istanbul cover -x *.runtime.js node_modules/mocha/bin/_mocha", "publish-patch": "mocha && npm version patch && git push && git push --tags && npm publish", "test": "mocha", "travis": "npm run cover -- --report lcovonly"}, "version": "0.2.17"}