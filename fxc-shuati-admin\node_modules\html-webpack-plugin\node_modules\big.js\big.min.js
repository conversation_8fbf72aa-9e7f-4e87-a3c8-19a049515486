/* big.js v3.2.0 https://github.com/MikeMcl/big.js/LICENCE */
!function(e){"use strict";function t(){function e(r){var i=this;return i instanceof e?(r instanceof e?(i.s=r.s,i.e=r.e,i.c=r.c.slice()):n(i,r),void(i.constructor=e)):void 0===r?t():new e(r)}return e.prototype=g,e.DP=c,e.RM=u,e.E_NEG=l,e.E_POS=a,e}function r(e,t,r){var n=e.constructor,s=t-(e=new n(e)).e,o=e.c;for(o.length>++t&&i(e,s,n.RM),o[0]?r?s=t:(o=e.c,s=e.e+s+1):++s;o.length<s;o.push(0));return s=e.e,1===r||r&&(s>=t||s<=n.E_NEG)?(e.s<0&&o[0]?"-":"")+(o.length>1?o[0]+"."+o.join("").slice(1):o[0])+(0>s?"e":"e+")+s:e.toString()}function n(e,t){var r,n,i;for(0===t&&0>1/t?t="-0":p.test(t+="")||s(NaN),e.s="-"==t.charAt(0)?(t=t.slice(1),-1):1,(r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(0>r&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):0>r&&(r=t.length),i=t.length,n=0;i>n&&"0"==t.charAt(n);n++);if(n==i)e.c=[e.e=0];else{for(;i>0&&"0"==t.charAt(--i););for(e.e=r-n-1,e.c=[];i>=n;e.c.push(+t.charAt(n++)));}return e}function i(e,t,r,n){var i,o=e.c,c=e.e+t+1;if(1===r?n=o[c]>=5:2===r?n=o[c]>5||5==o[c]&&(n||0>c||o[c+1]!==i||1&o[c-1]):3===r?n=n||o[c]!==i||0>c:(n=!1,0!==r&&s("!Big.RM!")),1>c||!o[0])n?(e.e=-t,e.c=[1]):e.c=[e.e=0];else{if(o.length=c--,n)for(;++o[c]>9;)o[c]=0,c--||(++e.e,o.unshift(1));for(c=o.length;!o[--c];o.pop());}return e}function s(e){var t=new Error(e);throw t.name="BigError",t}var o,c=20,u=1,f=1e6,h=1e6,l=-7,a=21,g={},p=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;g.abs=function(){var e=new this.constructor(this);return e.s=1,e},g.cmp=function(e){var t,r=this,n=r.c,i=(e=new r.constructor(e)).c,s=r.s,o=e.s,c=r.e,u=e.e;if(!n[0]||!i[0])return n[0]?s:i[0]?-o:0;if(s!=o)return s;if(t=0>s,c!=u)return c>u^t?1:-1;for(s=-1,o=(c=n.length)<(u=i.length)?c:u;++s<o;)if(n[s]!=i[s])return n[s]>i[s]^t?1:-1;return c==u?0:c>u^t?1:-1},g.div=function(e){var t=this,r=t.constructor,n=t.c,o=(e=new r(e)).c,c=t.s==e.s?1:-1,u=r.DP;if((u!==~~u||0>u||u>f)&&s("!Big.DP!"),!n[0]||!o[0])return n[0]==o[0]&&s(NaN),o[0]||s(c/0),new r(0*c);var h,l,a,g,p,v,d=o.slice(),w=h=o.length,m=n.length,E=n.slice(0,h),N=E.length,P=e,S=P.c=[],M=0,_=u+(P.e=t.e-e.e)+1;for(P.s=c,c=0>_?0:_,d.unshift(0);N++<h;E.push(0));do{for(a=0;10>a;a++){if(h!=(N=E.length))g=h>N?1:-1;else for(p=-1,g=0;++p<h;)if(o[p]!=E[p]){g=o[p]>E[p]?1:-1;break}if(!(0>g))break;for(l=N==h?o:d;N;){if(E[--N]<l[N]){for(p=N;p&&!E[--p];E[p]=9);--E[p],E[N]+=10}E[N]-=l[N]}for(;!E[0];E.shift());}S[M++]=g?a:++a,E[0]&&g?E[N]=n[w]||0:E=[n[w]]}while((w++<m||E[0]!==v)&&c--);return S[0]||1==M||(S.shift(),P.e--),M>_&&i(P,u,r.RM,E[0]!==v),P},g.eq=function(e){return!this.cmp(e)},g.gt=function(e){return this.cmp(e)>0},g.gte=function(e){return this.cmp(e)>-1},g.lt=function(e){return this.cmp(e)<0},g.lte=function(e){return this.cmp(e)<1},g.sub=g.minus=function(e){var t,r,n,i,s=this,o=s.constructor,c=s.s,u=(e=new o(e)).s;if(c!=u)return e.s=-u,s.plus(e);var f=s.c.slice(),h=s.e,l=e.c,a=e.e;if(!f[0]||!l[0])return l[0]?(e.s=-u,e):new o(f[0]?s:0);if(c=h-a){for((i=0>c)?(c=-c,n=f):(a=h,n=l),n.reverse(),u=c;u--;n.push(0));n.reverse()}else for(r=((i=f.length<l.length)?f:l).length,c=u=0;r>u;u++)if(f[u]!=l[u]){i=f[u]<l[u];break}if(i&&(n=f,f=l,l=n,e.s=-e.s),(u=(r=l.length)-(t=f.length))>0)for(;u--;f[t++]=0);for(u=t;r>c;){if(f[--r]<l[r]){for(t=r;t&&!f[--t];f[t]=9);--f[t],f[r]+=10}f[r]-=l[r]}for(;0===f[--u];f.pop());for(;0===f[0];)f.shift(),--a;return f[0]||(e.s=1,f=[a=0]),e.c=f,e.e=a,e},g.mod=function(e){var t,r=this,n=r.constructor,i=r.s,o=(e=new n(e)).s;return e.c[0]||s(NaN),r.s=e.s=1,t=1==e.cmp(r),r.s=i,e.s=o,t?new n(r):(i=n.DP,o=n.RM,n.DP=n.RM=0,r=r.div(e),n.DP=i,n.RM=o,this.minus(r.times(e)))},g.add=g.plus=function(e){var t,r=this,n=r.constructor,i=r.s,s=(e=new n(e)).s;if(i!=s)return e.s=-s,r.minus(e);var o=r.e,c=r.c,u=e.e,f=e.c;if(!c[0]||!f[0])return f[0]?e:new n(c[0]?r:0*i);if(c=c.slice(),i=o-u){for(i>0?(u=o,t=f):(i=-i,t=c),t.reverse();i--;t.push(0));t.reverse()}for(c.length-f.length<0&&(t=f,f=c,c=t),i=f.length,s=0;i;)s=(c[--i]=c[i]+f[i]+s)/10|0,c[i]%=10;for(s&&(c.unshift(s),++u),i=c.length;0===c[--i];c.pop());return e.c=c,e.e=u,e},g.pow=function(e){var t=this,r=new t.constructor(1),n=r,i=0>e;for((e!==~~e||-h>e||e>h)&&s("!pow!"),e=i?-e:e;1&e&&(n=n.times(t)),e>>=1,e;)t=t.times(t);return i?r.div(n):n},g.round=function(e,t){var r=this,n=r.constructor;return null==e?e=0:(e!==~~e||0>e||e>f)&&s("!round!"),i(r=new n(r),e,null==t?n.RM:t),r},g.sqrt=function(){var e,t,r,n=this,o=n.constructor,c=n.c,u=n.s,f=n.e,h=new o("0.5");if(!c[0])return new o(n);0>u&&s(NaN),u=Math.sqrt(n.toString()),0===u||u===1/0?(e=c.join(""),e.length+f&1||(e+="0"),t=new o(Math.sqrt(e).toString()),t.e=((f+1)/2|0)-(0>f||1&f)):t=new o(u.toString()),u=t.e+(o.DP+=4);do r=t,t=h.times(r.plus(n.div(r)));while(r.c.slice(0,u).join("")!==t.c.slice(0,u).join(""));return i(t,o.DP-=4,o.RM),t},g.mul=g.times=function(e){var t,r=this,n=r.constructor,i=r.c,s=(e=new n(e)).c,o=i.length,c=s.length,u=r.e,f=e.e;if(e.s=r.s==e.s?1:-1,!i[0]||!s[0])return new n(0*e.s);for(e.e=u+f,c>o&&(t=i,i=s,s=t,f=o,o=c,c=f),t=new Array(f=o+c);f--;t[f]=0);for(u=c;u--;){for(c=0,f=o+u;f>u;)c=t[f]+s[u]*i[f-u-1]+c,t[f--]=c%10,c=c/10|0;t[f]=(t[f]+c)%10}for(c&&++e.e,t[0]||t.shift(),u=t.length;!t[--u];t.pop());return e.c=t,e},g.toString=g.valueOf=g.toJSON=function(){var e=this,t=e.constructor,r=e.e,n=e.c.join(""),i=n.length;if(r<=t.E_NEG||r>=t.E_POS)n=n.charAt(0)+(i>1?"."+n.slice(1):"")+(0>r?"e":"e+")+r;else if(0>r){for(;++r;n="0"+n);n="0."+n}else if(r>0)if(++r>i)for(r-=i;r--;n+="0");else i>r&&(n=n.slice(0,r)+"."+n.slice(r));else i>1&&(n=n.charAt(0)+"."+n.slice(1));return e.s<0&&e.c[0]?"-"+n:n},g.toExponential=function(e){return null==e?e=this.c.length-1:(e!==~~e||0>e||e>f)&&s("!toExp!"),r(this,e,1)},g.toFixed=function(e){var t,n=this,i=n.constructor,o=i.E_NEG,c=i.E_POS;return i.E_NEG=-(i.E_POS=1/0),null==e?t=n.toString():e===~~e&&e>=0&&f>=e&&(t=r(n,n.e+e),n.s<0&&n.c[0]&&t.indexOf("-")<0&&(t="-"+t)),i.E_NEG=o,i.E_POS=c,t||s("!toFix!"),t},g.toPrecision=function(e){return null==e?this.toString():((e!==~~e||1>e||e>f)&&s("!toPre!"),r(this,e-1,2))},o=t(),"function"==typeof define&&define.amd?define(function(){return o}):"undefined"!=typeof module&&module.exports?(module.exports=o,module.exports.Big=o):e.Big=o}(this);
//# sourceMappingURL=doc/big.js.map