<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索菜单名称、路径"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加菜单
        </el-button>
      </div>
    </el-card>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="treeData"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      row-key="menu_id"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="ID" prop="menu_id" align="center" width="80" />
      <el-table-column label="菜单名称" prop="menu_name" min-width="150" />
      <el-table-column label="菜单路径" prop="menu_path" min-width="180" />
      <el-table-column label="菜单图标" width="100" align="center">
        <template slot-scope="{row}">
          <i v-if="row.menu_icon" :class="row.menu_icon" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="组件路径" prop="component" min-width="180" />
      <el-table-column label="父菜单" prop="parent_name" min-width="120" />
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort_order" width="80" align="center" />
      <el-table-column label="创建时间" min-width="120" align="center">
        <template slot-scope="{row}">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 450px; margin-left:50px;"
      >
        <el-form-item label="父菜单" prop="parent_id">
          <el-select v-model="temp.parent_id" placeholder="请选择父菜单" style="width: 100%">
            <el-option label="顶级菜单" :value="0" />
            <el-option
              v-for="item in parentOptions"
              :key="item.menu_id"
              :label="item.menu_name"
              :value="item.menu_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="菜单名称" prop="menu_name">
          <el-input v-model="temp.menu_name" />
        </el-form-item>
        <el-form-item label="菜单路径" prop="menu_path">
          <el-input v-model="temp.menu_path" />
        </el-form-item>
        <el-form-item label="组件路径" prop="component">
          <el-input v-model="temp.component" />
        </el-form-item>
        <el-form-item label="菜单图标" prop="menu_icon">
          <el-input v-model="temp.menu_icon" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="temp.sort_order" :min="0" :max="999" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { formatDate } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'MenuManagement',
  components: { Pagination },
  directives: { waves },
  filters: {
    parseTime
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 50,
        keyword: '',
        status: ''
      },
      parentOptions: [],
      temp: {
        menu_id: undefined,
        parent_id: 0,
        menu_name: '',
        menu_path: '',
        component: '',
        menu_icon: '',
        status: 'active',
        sort_order: 0
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑菜单',
        create: '添加菜单'
      },
      rules: {
        menu_name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
        menu_path: [{ required: true, message: '菜单路径不能为空', trigger: 'blur' }]
      }
    }
  },
  computed: {
    treeData() {
      // 将平铺数据转换为树形结构
      const tree = []
      const map = {}

      // 先创建所有节点的映射
      this.list.forEach(item => {
        map[item.menu_id] = { ...item, children: [] }
      })

      // 构建树形结构
      this.list.forEach(item => {
        if (item.parent_id === 0) {
          // 顶级菜单
          tree.push(map[item.menu_id])
        } else {
          // 子菜单
          if (map[item.parent_id]) {
            map[item.parent_id].children.push(map[item.menu_id])
          }
        }
      })

      return tree
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      
      // 模拟数据，实际项目中应该调用API
      setTimeout(() => {
        this.list = [
          // 首页
          {
            menu_id: 1,
            menu_name: '首页',
            menu_path: '/dashboard',
            component: 'Layout',
            menu_icon: 'dashboard',
            parent_id: 0,
            parent_name: '顶级菜单',
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-01'
          },
          {
            menu_id: 2,
            menu_name: '首页',
            menu_path: '/dashboard/index',
            component: 'dashboard/index',
            menu_icon: 'dashboard',
            parent_id: 1,
            parent_name: '首页',
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-01'
          },
          // 系统管理
          {
            menu_id: 10,
            menu_name: '系统管理',
            menu_path: '/system',
            component: 'Layout',
            menu_icon: 'el-icon-setting',
            parent_id: 0,
            parent_name: '顶级菜单',
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-02'
          },
          {
            menu_id: 11,
            menu_name: '用户管理',
            menu_path: '/system/users',
            component: 'users/list',
            menu_icon: 'el-icon-user-solid',
            parent_id: 10,
            parent_name: '系统管理',
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-02'
          },
          {
            menu_id: 12,
            menu_name: '角色管理',
            menu_path: '/system/roles',
            component: 'users/roles',
            menu_icon: 'el-icon-s-custom',
            parent_id: 10,
            parent_name: '系统管理',
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-02'
          },
          {
            menu_id: 13,
            menu_name: '权限管理',
            menu_path: '/system/permissions',
            component: 'users/permissions',
            menu_icon: 'el-icon-key',
            parent_id: 10,
            parent_name: '系统管理',
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-02'
          },
          {
            menu_id: 14,
            menu_name: '菜单管理',
            menu_path: '/system/menus',
            component: 'system/menus',
            menu_icon: 'el-icon-menu',
            parent_id: 10,
            parent_name: '系统管理',
            status: 'active',
            sort_order: 4,
            created_at: '2024-01-02'
          },
          // 题库管理
          {
            menu_id: 20,
            menu_name: '题库管理',
            menu_path: '/questions',
            component: 'Layout',
            menu_icon: 'el-icon-document',
            parent_id: 0,
            parent_name: '顶级菜单',
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-03'
          },
          {
            menu_id: 21,
            menu_name: '题目分类',
            menu_path: '/questions/categories',
            component: 'questions/categories',
            menu_icon: 'el-icon-folder',
            parent_id: 20,
            parent_name: '题库管理',
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-03'
          },
          {
            menu_id: 22,
            menu_name: '题目列表',
            menu_path: '/questions/list',
            component: 'questions/list',
            menu_icon: 'el-icon-document-copy',
            parent_id: 20,
            parent_name: '题库管理',
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-03'
          },
          {
            menu_id: 23,
            menu_name: '添加题目',
            menu_path: '/questions/add',
            component: 'questions/add',
            menu_icon: 'el-icon-plus',
            parent_id: 20,
            parent_name: '题库管理',
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-03'
          },
          {
            menu_id: 24,
            menu_name: '批量导入',
            menu_path: '/questions/upload',
            component: 'questions/upload',
            menu_icon: 'el-icon-upload',
            parent_id: 20,
            parent_name: '题库管理',
            status: 'active',
            sort_order: 4,
            created_at: '2024-01-03'
          },
          // 学习数据
          {
            menu_id: 30,
            menu_name: '学习数据',
            menu_path: '/learning',
            component: 'Layout',
            menu_icon: 'el-icon-data-analysis',
            parent_id: 0,
            parent_name: '顶级菜单',
            status: 'active',
            sort_order: 4,
            created_at: '2024-01-04'
          },
          {
            menu_id: 31,
            menu_name: '收藏管理',
            menu_path: '/learning/favorites',
            component: 'learning/favorites',
            menu_icon: 'el-icon-star-on',
            parent_id: 30,
            parent_name: '学习数据',
            status: 'active',
            sort_order: 1,
            created_at: '2024-01-04'
          },
          {
            menu_id: 32,
            menu_name: '错题管理',
            menu_path: '/learning/errors',
            component: 'learning/errors',
            menu_icon: 'el-icon-warning',
            parent_id: 30,
            parent_name: '学习数据',
            status: 'active',
            sort_order: 2,
            created_at: '2024-01-04'
          },
          {
            menu_id: 33,
            menu_name: '练习记录',
            menu_path: '/learning/records',
            component: 'learning/records',
            menu_icon: 'el-icon-tickets',
            parent_id: 30,
            parent_name: '学习数据',
            status: 'active',
            sort_order: 3,
            created_at: '2024-01-04'
          }
        ]
        this.total = this.list.length
        this.listLoading = false
        
        // 更新父菜单选项
        this.parentOptions = this.list.filter(item => item.parent_id === 0)
      }, 500)
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        menu_id: undefined,
        parent_id: 0,
        menu_name: '',
        menu_path: '',
        component: '',
        menu_icon: '',
        status: 'active',
        sort_order: 0
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$notify({
            title: '成功',
            message: '创建成功（模拟）',
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$notify({
            title: '成功',
            message: '更新成功（模拟）',
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该菜单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$notify({
          title: '成功',
          message: '删除成功（模拟）',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-card {
    margin-bottom: 20px;
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .filter-item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  .el-table {
    margin-bottom: 20px;
    width: 100% !important;

    .el-table__body-wrapper {
      width: 100% !important;
    }
  }
  .pagination-container {
    padding: 15px 0;
  }
}
</style>
