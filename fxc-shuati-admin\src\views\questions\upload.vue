<template>
  <div class="app-container">
    <!-- 导入说明 -->
    <el-card class="instruction-card">
      <div slot="header">
        <span>批量导入题目</span>
      </div>
      <div class="instruction-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div slot="default">
            <p>1. 请下载模板文件，按照模板格式填写题目数据</p>
            <p>2. 支持的文件格式：Excel (.xlsx, .xls)</p>
            <p>3. 单次最多导入1000道题目</p>
            <p>4. 题目类型：single_choice(单选), multiple_choice(多选), true_false(判断), fill_blank(填空), short_answer(简答), essay(论述)</p>
            <p>5. 难度等级：easy(简单), medium(中等), hard(困难)</p>
          </div>
        </el-alert>

        <div class="template-download">
          <el-button type="primary" icon="el-icon-download" @click="downloadTemplate">
            下载导入模板
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 文件上传 -->
    <el-card class="upload-card">
      <div slot="header">
        <span>上传文件</span>
      </div>

      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        :action="uploadUrl"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-progress="handleProgress"
        :file-list="fileList"
        :auto-upload="false"
        accept=".xlsx,.xls"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，且不超过10MB</div>
      </el-upload>

      <div class="upload-actions">
        <el-button type="primary" @click="submitUpload" :loading="uploading">
          开始导入
        </el-button>
        <el-button @click="clearFiles">清空文件</el-button>
      </div>
    </el-card>

    <!-- 导入进度 -->
    <el-card v-if="showProgress" class="progress-card">
      <div slot="header">
        <span>导入进度</span>
      </div>

      <el-progress
        :percentage="uploadProgress"
        :status="uploadStatus"
        :stroke-width="20"
      />

      <div class="progress-info">
        <p>{{ progressText }}</p>
      </div>
    </el-card>

    <!-- 导入结果 -->
    <el-card v-if="importResult" class="result-card">
      <div slot="header">
        <span>导入结果</span>
      </div>

      <div class="result-summary">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item success">
              <div class="stat-number">{{ importResult.success_count }}</div>
              <div class="stat-label">成功导入</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item error">
              <div class="stat-number">{{ importResult.error_count }}</div>
              <div class="stat-label">导入失败</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item warning">
              <div class="stat-number">{{ importResult.skip_count }}</div>
              <div class="stat-label">跳过重复</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item info">
              <div class="stat-number">{{ importResult.total_count }}</div>
              <div class="stat-label">总计处理</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 错误详情 -->
      <div v-if="importResult.errors && importResult.errors.length > 0" class="error-details">
        <h4>错误详情：</h4>
        <el-table
          :data="importResult.errors"
          border
          size="small"
          max-height="300"
        >
          <el-table-column prop="row" label="行号" width="80" />
          <el-table-column prop="field" label="字段" width="120" />
          <el-table-column prop="message" label="错误信息" />
        </el-table>
      </div>

      <div class="result-actions">
        <el-button type="primary" @click="goToQuestionList">查看题目列表</el-button>
        <el-button @click="resetImport">重新导入</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'QuestionUpload',
  data() {
    return {
      uploadUrl: '/api/questions/import', // 实际项目中的上传接口
      fileList: [],
      uploading: false,
      showProgress: false,
      uploadProgress: 0,
      uploadStatus: '',
      progressText: '',
      importResult: null
    }
  },
  methods: {
    downloadTemplate() {
      // 创建模板数据
      const templateData = [
        {
          '题目类型': 'single_choice',
          '难度等级': 'medium',
          '分类ID': '1111',
          '标签ID': '1,9,7',
          '题目内容': '马克思主义哲学的基本问题是（）',
          '选项A': '物质和意识的关系问题',
          '选项B': '理论和实践的关系问题',
          '选项C': '个人和社会的关系问题',
          '选项D': '自由和必然的关系问题',
          '选项E': '',
          '选项F': '',
          '正确答案': 'A',
          '题目解析': '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。它包括两个方面：第一，物质和意识何者为第一性；第二，物质和意识是否具有同一性。',
          '分值': '2',
          '排序': '1',
          '状态': 'active'
        },
        {
          '题目类型': 'single_choice',
          '难度等级': 'easy',
          '分类ID': '1111',
          '标签ID': '1,9,16',
          '题目内容': '物质的唯一特性是（）',
          '选项A': '运动性',
          '选项B': '客观实在性',
          '选项C': '可知性',
          '选项D': '绝对性',
          '选项E': '',
          '选项F': '',
          '正确答案': 'B',
          '题目解析': '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在，并能为人的意识所反映。',
          '分值': '2',
          '排序': '2',
          '状态': 'active'
        },
        {
          '题目类型': 'multiple_choice',
          '难度等级': 'medium',
          '分类ID': '1112',
          '标签ID': '1,9,7',
          '题目内容': '意识的本质是（）',
          '选项A': '意识是人脑的机能',
          '选项B': '意识是客观存在的反映',
          '选项C': '意识是社会的产物',
          '选项D': '意识具有主观能动性',
          '选项E': '',
          '选项F': '',
          '正确答案': 'A,B,C,D',
          '题目解析': '意识的本质包括：（1）意识是人脑的机能；（2）意识是客观存在的反映；（3）意识是社会的产物；（4）意识具有主观能动性。这四个方面构成了意识本质的完整内容。',
          '分值': '2',
          '排序': '3',
          '状态': 'active'
        },
        {
          '题目类型': 'true_false',
          '难度等级': 'easy',
          '分类ID': '1111',
          '标签ID': '1,9',
          '题目内容': '马克思主义哲学认为，世界的真正统一性在于它的物质性。',
          '选项A': '',
          '选项B': '',
          '选项C': '',
          '选项D': '',
          '选项E': '',
          '选项F': '',
          '正确答案': 'true',
          '题目解析': '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。这是马克思主义一元论的基本观点，强调世界万物都是物质的不同表现形式。',
          '分值': '2',
          '排序': '4',
          '状态': 'active'
        },
        {
          '题目类型': 'fill_blank',
          '难度等级': 'easy',
          '分类ID': '1123',
          '标签ID': '1,9,7',
          '题目内容': '矛盾的基本属性是_____和_____。',
          '选项A': '',
          '选项B': '',
          '选项C': '',
          '选项D': '',
          '选项E': '',
          '选项F': '',
          '正确答案': '同一性；斗争性',
          '题目解析': '矛盾的基本属性是同一性和斗争性。同一性是指矛盾双方相互依存、相互贯通的性质和趋势；斗争性是指矛盾双方相互排斥、相互对立的性质和趋势。',
          '分值': '2',
          '排序': '5',
          '状态': 'active'
        },
        {
          '题目类型': 'short_answer',
          '难度等级': 'hard',
          '分类ID': '113',
          '标签ID': '1,9,6',
          '题目内容': '简述实践是认识基础的主要表现。',
          '选项A': '',
          '选项B': '',
          '选项C': '',
          '选项D': '',
          '选项E': '',
          '选项F': '',
          '正确答案': '实践是认识的基础，主要表现在四个方面：（1）实践是认识的来源；（2）实践是认识发展的动力；（3）实践是检验认识真理性的唯一标准；（4）实践是认识的目的。',
          '题目解析': '这道题考查实践与认识的关系。要从四个方面来回答：来源、动力、标准、目的。每个方面都要简要说明其含义。',
          '分值': '6',
          '排序': '6',
          '状态': 'active'
        }
      ]

      // 转换为CSV格式并下载
      this.downloadCSV(templateData, '题目导入模板.csv')
    },

    downloadCSV(data, filename) {
      const csvContent = this.convertToCSV(data)
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', filename)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    },

    convertToCSV(data) {
      if (!data || data.length === 0) return ''

      const headers = Object.keys(data[0])
      const csvHeaders = headers.join(',')

      const csvRows = data.map(row => {
        return headers.map(header => {
          const value = row[header] || ''
          return `"${value.toString().replace(/"/g, '""')}"`
        }).join(',')
      })

      return [csvHeaders, ...csvRows].join('\n')
    },

    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10MB!')
        return false
      }
      return true
    },

    submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      this.uploading = true
      this.showProgress = true
      this.uploadProgress = 0
      this.uploadStatus = ''
      this.progressText = '开始上传文件...'

      // 模拟上传过程
      this.simulateUpload()
    },

    simulateUpload() {
      const timer = setInterval(() => {
        this.uploadProgress += Math.random() * 15

        if (this.uploadProgress < 30) {
          this.progressText = '正在上传文件...'
        } else if (this.uploadProgress < 60) {
          this.progressText = '正在解析文件内容...'
        } else if (this.uploadProgress < 90) {
          this.progressText = '正在导入题目数据...'
        } else {
          this.uploadProgress = 100
          this.progressText = '导入完成！'
          this.uploadStatus = 'success'
          this.uploading = false

          // 模拟导入结果
          setTimeout(() => {
            this.importResult = {
              success_count: 245,
              error_count: 5,
              skip_count: 12,
              total_count: 262,
              errors: [
                { row: 15, field: '题目内容', message: '题目内容不能为空' },
                { row: 23, field: '正确答案', message: '正确答案格式错误' },
                { row: 45, field: '分类ID', message: '分类ID不存在' },
                { row: 67, field: '题目类型', message: '不支持的题目类型' },
                { row: 89, field: '选项A', message: '单选题至少需要2个选项' }
              ]
            }
          }, 500)

          clearInterval(timer)
        }
      }, 200)
    },

    handleSuccess(response, file, fileList) {
      this.$message.success('文件上传成功')
    },

    handleError(err, file, fileList) {
      this.$message.error('文件上传失败')
      this.uploading = false
      this.showProgress = false
    },

    handleProgress(event, file, fileList) {
      // 实际项目中可以在这里处理真实的上传进度
    },

    clearFiles() {
      this.$refs.upload.clearFiles()
      this.fileList = []
      this.showProgress = false
      this.importResult = null
    },

    resetImport() {
      this.clearFiles()
      this.uploadProgress = 0
      this.uploadStatus = ''
      this.progressText = ''
    },

    goToQuestionList() {
      this.$router.push('/questions/list')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .instruction-card,
  .upload-card,
  .progress-card,
  .result-card {
    margin-bottom: 20px;
  }

  .instruction-content {
    .template-download {
      margin-top: 20px;
      text-align: center;
    }
  }

  .upload-demo {
    margin-bottom: 20px;
  }

  .upload-actions {
    text-align: center;
    margin-top: 20px;
  }

  .progress-info {
    margin-top: 20px;
    text-align: center;

    p {
      margin: 0;
      color: #606266;
    }
  }

  .result-summary {
    margin-bottom: 30px;

    .stat-item {
      text-align: center;
      padding: 20px;
      border-radius: 8px;

      .stat-number {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }

      &.success {
        background: #f0f9ff;
        .stat-number { color: #67c23a; }
      }

      &.error {
        background: #fef0f0;
        .stat-number { color: #f56c6c; }
      }

      &.warning {
        background: #fdf6ec;
        .stat-number { color: #e6a23c; }
      }

      &.info {
        background: #f4f4f5;
        .stat-number { color: #909399; }
      }
    }
  }

  .error-details {
    margin-bottom: 30px;

    h4 {
      margin-bottom: 15px;
      color: #f56c6c;
    }
  }

  .result-actions {
    text-align: center;
  }
}
</style>
