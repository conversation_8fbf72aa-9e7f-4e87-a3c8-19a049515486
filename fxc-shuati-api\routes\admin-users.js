const express = require('express');
const bcrypt = require('bcryptjs');
const { query, transaction } = require('../config/database');
const router = express.Router();

// 获取管理员用户列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, keyword = '', status = '' } = req.query;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (keyword) {
      whereClause += ' AND (username LIKE ? OR real_name LIKE ? OR email LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }
    
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM admins ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    const listSql = `
      SELECT a.admin_id, a.username, a.real_name, a.email, a.phone, a.avatar, 
             a.status, a.last_login_time, a.last_login_ip, a.created_at,
             GROUP_CONCAT(r.role_name) as roles
      FROM admins a
      LEFT JOIN admin_roles ar ON a.admin_id = ar.admin_id
      LEFT JOIN roles r ON ar.role_id = r.role_id
      ${whereClause}
      GROUP BY a.admin_id
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `;
    params.push(parseInt(pageSize), offset);
    
    const list = await query(listSql, params);
    
    res.json({
      success: true,
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取管理员列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取管理员列表失败'
    });
  }
});

// 获取管理员详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT a.admin_id, a.username, a.real_name, a.email, a.phone, a.avatar,
             a.status, a.last_login_time, a.last_login_ip, a.created_at,
             GROUP_CONCAT(ar.role_id) as role_ids,
             GROUP_CONCAT(r.role_name) as role_names
      FROM admins a
      LEFT JOIN admin_roles ar ON a.admin_id = ar.admin_id
      LEFT JOIN roles r ON ar.role_id = r.role_id
      WHERE a.admin_id = ?
      GROUP BY a.admin_id
    `;
    
    const result = await query(sql, [id]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在'
      });
    }
    
    const admin = result[0];
    admin.role_ids = admin.role_ids ? admin.role_ids.split(',').map(id => parseInt(id)) : [];
    admin.role_names = admin.role_names ? admin.role_names.split(',') : [];
    
    res.json({
      success: true,
      data: admin
    });
  } catch (error) {
    console.error('获取管理员详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取管理员详情失败'
    });
  }
});

// 创建管理员
router.post('/', async (req, res) => {
  try {
    const { username, password, real_name, email, phone, avatar, status = 'active', role_ids = [] } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }
    
    // 检查用户名是否已存在
    const existCheck = await query('SELECT admin_id FROM admins WHERE username = ?', [username]);
    if (existCheck.length > 0) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }
    
    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);
    
    await transaction(async (connection) => {
      // 插入管理员
      const insertSql = `
        INSERT INTO admins (username, password, real_name, email, phone, avatar, status)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      const [result] = await connection.execute(insertSql, [
        username, hashedPassword, real_name || '', email || '', phone || '', avatar || '', status
      ]);
      
      const adminId = result.insertId;
      
      // 关联角色
      if (role_ids.length > 0) {
        const roleValues = role_ids.map(roleId => [adminId, roleId]);
        const roleSql = 'INSERT INTO admin_roles (admin_id, role_id) VALUES ?';
        await connection.query(roleSql, [roleValues]);
      }
      
      return adminId;
    });
    
    res.json({
      success: true,
      message: '创建管理员成功'
    });
  } catch (error) {
    console.error('创建管理员失败:', error);
    res.status(500).json({
      success: false,
      message: '创建管理员失败'
    });
  }
});

// 更新管理员
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { username, password, real_name, email, phone, avatar, status, role_ids = [] } = req.body;
    
    // 检查管理员是否存在
    const existCheck = await query('SELECT admin_id FROM admins WHERE admin_id = ?', [id]);
    if (existCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在'
      });
    }
    
    // 检查用户名是否被其他用户使用
    if (username) {
      const usernameCheck = await query('SELECT admin_id FROM admins WHERE username = ? AND admin_id != ?', [username, id]);
      if (usernameCheck.length > 0) {
        return res.status(400).json({
          success: false,
          message: '用户名已被使用'
        });
      }
    }
    
    await transaction(async (connection) => {
      // 更新管理员信息
      let updateFields = [];
      let updateValues = [];
      
      if (username) {
        updateFields.push('username = ?');
        updateValues.push(username);
      }
      if (password) {
        updateFields.push('password = ?');
        updateValues.push(await bcrypt.hash(password, 10));
      }
      if (real_name !== undefined) {
        updateFields.push('real_name = ?');
        updateValues.push(real_name);
      }
      if (email !== undefined) {
        updateFields.push('email = ?');
        updateValues.push(email);
      }
      if (phone !== undefined) {
        updateFields.push('phone = ?');
        updateValues.push(phone);
      }
      if (avatar !== undefined) {
        updateFields.push('avatar = ?');
        updateValues.push(avatar);
      }
      if (status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(status);
      }
      
      if (updateFields.length > 0) {
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        updateValues.push(id);
        
        const updateSql = `UPDATE admins SET ${updateFields.join(', ')} WHERE admin_id = ?`;
        await connection.execute(updateSql, updateValues);
      }
      
      // 更新角色关联
      await connection.execute('DELETE FROM admin_roles WHERE admin_id = ?', [id]);
      if (role_ids.length > 0) {
        const roleValues = role_ids.map(roleId => [id, roleId]);
        const roleSql = 'INSERT INTO admin_roles (admin_id, role_id) VALUES ?';
        await connection.query(roleSql, [roleValues]);
      }
    });
    
    res.json({
      success: true,
      message: '更新管理员成功'
    });
  } catch (error) {
    console.error('更新管理员失败:', error);
    res.status(500).json({
      success: false,
      message: '更新管理员失败'
    });
  }
});

// 删除管理员
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查是否为默认管理员
    if (id === '1') {
      return res.status(400).json({
        success: false,
        message: '不能删除默认管理员'
      });
    }
    
    const result = await query('DELETE FROM admins WHERE admin_id = ?', [id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在'
      });
    }
    
    res.json({
      success: true,
      message: '删除管理员成功'
    });
  } catch (error) {
    console.error('删除管理员失败:', error);
    res.status(500).json({
      success: false,
      message: '删除管理员失败'
    });
  }
});

module.exports = router;
