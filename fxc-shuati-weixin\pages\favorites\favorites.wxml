<!--favorites.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section card">
    <view class="search-input-wrapper">
      <input 
        value="{{searchKeyword}}" 
        placeholder="搜索收藏的题目" 
        class="search-input"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <button class="search-btn" bindtap="onSearch">搜索</button>
    </view>
  </view>

  <!-- 筛选条件 -->
  <view class="filter-section card">
    <scroll-view scroll-x="true" class="filter-scroll">
      <view 
        wx:for="{{typeOptions}}" 
        wx:key="value"
        class="filter-item {{selectedType === item.value ? 'active' : ''}}"
        bindtap="selectType"
        data-value="{{item.value}}"
      >
        {{item.label}}
      </view>
    </scroll-view>
  </view>

  <!-- 收藏列表 -->
  <view class="favorite-list" wx:if="{{favoriteList.length > 0}}">
    <view 
      wx:for="{{favoriteList}}" 
      wx:key="favorite_id"
      class="favorite-item card"
      bindtap="goToQuestion"
      data-item="{{item}}"
    >
      <view class="question-info">
        <view class="question-title">{{item.question.question_content}}</view>
        <view class="question-meta">
          <text class="question-type">{{item.question.typeText}}</text>
          <text class="question-difficulty">难度: {{item.question.difficultyText}}</text>
          <text class="favorite-time">{{item.timeText}}</text>
        </view>
      </view>
      <view class="favorite-actions">
        <button 
          class="practice-btn btn btn-primary" 
          size="mini" 
          bindtap="goToQuestion" 
          data-item="{{item}}"
          catchtap=""
        >
          练习
        </button>
        <button 
          class="remove-btn btn btn-danger" 
          size="mini" 
          bindtap="removeFavorite" 
          data-item="{{item}}"
          catchtap=""
        >
          取消收藏
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <text class="empty-icon">❤️</text>
    <text class="empty-text">暂无收藏题目</text>
    <text class="empty-desc">去练习页面收藏喜欢的题目吧</text>
    <button class="go-practice-btn btn btn-primary" bindtap="goToPractice">去练习</button>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions" wx:if="{{favoriteList.length > 0}}">
    <button class="action-btn btn-secondary" bindtap="clearAllFavorites">清空收藏</button>
    <button class="action-btn btn-primary" bindtap="practiceAllFavorites">全部练习</button>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && favoriteList.length > 0}}">
    <button class="load-more-btn" bindtap="loadMore" loading="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>
</view>
