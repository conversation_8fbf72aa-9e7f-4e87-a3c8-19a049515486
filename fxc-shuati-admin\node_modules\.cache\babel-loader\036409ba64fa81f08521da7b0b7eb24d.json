{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\roles.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\roles.vue", "mtime": 1752566462265}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getRoleList", "createRole", "updateRole", "deleteRole", "getRolePermissions", "updateRolePermissions", "getPermissionTree", "waves", "parseTime", "Pagination", "name", "components", "directives", "filters", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "status", "temp", "role_id", "undefined", "role_name", "role_code", "role_desc", "sort_order", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "permissionDialogVisible", "permissionLoading", "permissionTree", "permissionProps", "label", "children", "currentRole", "created", "getList", "methods", "_this", "then", "response", "handleFilter", "resetTemp", "handleCreate", "_this2", "$nextTick", "$refs", "clearValidate", "createData", "_this3", "validate", "valid", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this4", "Object", "assign", "updateData", "_this5", "tempData", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "handlePermission", "_this7", "res", "set<PERSON><PERSON><PERSON><PERSON>eys", "permissions", "updatePermissions", "_this8", "checked<PERSON>eys", "getChe<PERSON><PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "getHalfCheckedKeys", "concat", "_toConsumableArray"], "sources": ["src/views/users/roles.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <div class=\"filter-container\">\n      <el-input\n        v-model=\"listQuery.keyword\"\n        placeholder=\"搜索角色名称、编码\"\n        style=\"width: 200px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-select\n        v-model=\"listQuery.status\"\n        placeholder=\"状态\"\n        clearable\n        style=\"width: 120px\"\n        class=\"filter-item\"\n      >\n        <el-option label=\"启用\" value=\"active\" />\n        <el-option label=\"禁用\" value=\"disabled\" />\n      </el-select>\n      <el-button\n        v-waves\n        class=\"filter-item\"\n        type=\"primary\"\n        icon=\"el-icon-search\"\n        @click=\"handleFilter\"\n      >\n        搜索\n      </el-button>\n      <el-button\n        class=\"filter-item\"\n        style=\"margin-left: 10px;\"\n        type=\"primary\"\n        icon=\"el-icon-plus\"\n        @click=\"handleCreate\"\n      >\n        添加角色\n      </el-button>\n    </div>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"role_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"角色名称\" prop=\"role_name\" width=\"150\" />\n      <el-table-column label=\"角色编码\" prop=\"role_code\" width=\"150\" />\n      <el-table-column label=\"角色描述\" prop=\"role_desc\" />\n      <el-table-column label=\"管理员数\" prop=\"admin_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"权限数\" prop=\"permission_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"250\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handlePermission(row)\">\n            权限\n          </el-button>\n          <el-button\n            v-if=\"row.role_id !== 1 && row.role_id !== 2\"\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"角色名称\" prop=\"role_name\">\n          <el-input v-model=\"temp.role_name\" />\n        </el-form-item>\n        <el-form-item label=\"角色编码\" prop=\"role_code\">\n          <el-input v-model=\"temp.role_code\" />\n        </el-form-item>\n        <el-form-item label=\"角色描述\" prop=\"role_desc\">\n          <el-input v-model=\"temp.role_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 权限设置对话框 -->\n    <el-dialog title=\"权限设置\" :visible.sync=\"permissionDialogVisible\" width=\"600px\">\n      <div v-loading=\"permissionLoading\">\n        <el-tree\n          ref=\"permissionTree\"\n          :data=\"permissionTree\"\n          :props=\"permissionProps\"\n          show-checkbox\n          node-key=\"permission_id\"\n          default-expand-all\n        />\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"permissionDialogVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"updatePermissions\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRoleList, createRole, updateRole, deleteRole, getRolePermissions, updateRolePermissions } from '@/api/users'\nimport { getPermissionTree } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'RoleList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: null,\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      temp: {\n        role_id: undefined,\n        role_name: '',\n        role_code: '',\n        role_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑角色',\n        create: '添加角色'\n      },\n      rules: {\n        role_name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],\n        role_code: [{ required: true, message: '角色编码不能为空', trigger: 'blur' }]\n      },\n      permissionDialogVisible: false,\n      permissionLoading: false,\n      permissionTree: [],\n      permissionProps: {\n        label: 'permission_name',\n        children: 'children'\n      },\n      currentRole: null\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getRoleList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        role_id: undefined,\n        role_name: '',\n        role_code: '',\n        role_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createRole(this.temp).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          updateRole(tempData.role_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该角色吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteRole(row.role_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    },\n    handlePermission(row) {\n      this.currentRole = row\n      this.permissionDialogVisible = true\n      this.permissionLoading = true\n      \n      // 获取权限树\n      getPermissionTree().then(response => {\n        this.permissionTree = response.data\n        \n        // 获取角色已有权限\n        getRolePermissions(row.role_id).then(res => {\n          this.$nextTick(() => {\n            this.$refs.permissionTree.setCheckedKeys(res.data.permissions)\n            this.permissionLoading = false\n          })\n        })\n      })\n    },\n    updatePermissions() {\n      const checkedKeys = this.$refs.permissionTree.getCheckedKeys()\n      const halfCheckedKeys = this.$refs.permissionTree.getHalfCheckedKeys()\n      const permissions = [...checkedKeys, ...halfCheckedKeys]\n      \n      updateRolePermissions(this.currentRole.role_id, { permissions }).then(() => {\n        this.permissionDialogVisible = false\n        this.$notify({\n          title: '成功',\n          message: '权限设置成功',\n          type: 'success',\n          duration: 2000\n        })\n        this.getList()\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA,SAAAA,WAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA;AACA,SAAAC,iBAAA;AACA,OAAAC,KAAA;AACA,SAAAC,SAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,OAAA;IACAL,SAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,IAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAN,MAAA;QACAO,UAAA;MACA;MACAC,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAT,SAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAX,SAAA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,uBAAA;MACAC,iBAAA;MACAC,cAAA;MACAC,eAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAhC,WAAA;MACAlB,WAAA,MAAAmB,SAAA,EAAAgC,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAlC,IAAA,GAAAoC,QAAA,CAAAtC,IAAA,CAAAE,IAAA;QACAkC,KAAA,CAAAjC,KAAA,GAAAmC,QAAA,CAAAtC,IAAA,CAAAG,KAAA;QACAiC,KAAA,CAAAhC,WAAA;MACA;IACA;IACAmC,YAAA,WAAAA,aAAA;MACA,KAAAlC,SAAA,CAAAC,IAAA;MACA,KAAA4B,OAAA;IACA;IACAM,SAAA,WAAAA,UAAA;MACA,KAAA9B,IAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAN,MAAA;QACAO,UAAA;MACA;IACA;IACAyB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAAtB,YAAA;MACA,KAAAD,iBAAA;MACA,KAAA0B,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA9D,UAAA,CAAA4D,MAAA,CAAArC,IAAA,EAAA2B,IAAA;YACAU,MAAA,CAAA9B,iBAAA;YACA8B,MAAA,CAAAG,OAAA;cACAC,KAAA;cACA3B,OAAA;cACA4B,IAAA;cACAC,QAAA;YACA;YACAN,MAAA,CAAAb,OAAA;UACA;QACA;MACA;IACA;IACAoB,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA9C,IAAA,GAAA+C,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAArC,YAAA;MACA,KAAAD,iBAAA;MACA,KAAA0B,SAAA;QACAa,MAAA,CAAAZ,KAAA,aAAAC,aAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAY,QAAA,GAAAJ,MAAA,CAAAC,MAAA,KAAAE,MAAA,CAAAlD,IAAA;UACAtB,UAAA,CAAAyE,QAAA,CAAAlD,OAAA,EAAAkD,QAAA,EAAAxB,IAAA;YACAuB,MAAA,CAAA3C,iBAAA;YACA2C,MAAA,CAAAV,OAAA;cACAC,KAAA;cACA3B,OAAA;cACA4B,IAAA;cACAC,QAAA;YACA;YACAO,MAAA,CAAA1B,OAAA;UACA;QACA;MACA;IACA;IACA4B,YAAA,WAAAA,aAAAP,GAAA;MAAA,IAAAQ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAd,IAAA;MACA,GAAAf,IAAA;QACAhD,UAAA,CAAAkE,GAAA,CAAA5C,OAAA,EAAA0B,IAAA;UACA0B,MAAA,CAAAb,OAAA;YACAC,KAAA;YACA3B,OAAA;YACA4B,IAAA;YACAC,QAAA;UACA;UACAU,MAAA,CAAA7B,OAAA;QACA;MACA;IACA;IACAiC,gBAAA,WAAAA,iBAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,KAAApC,WAAA,GAAAuB,GAAA;MACA,KAAA7B,uBAAA;MACA,KAAAC,iBAAA;;MAEA;MACAnC,iBAAA,GAAA6C,IAAA,WAAAC,QAAA;QACA8B,MAAA,CAAAxC,cAAA,GAAAU,QAAA,CAAAtC,IAAA;;QAEA;QACAV,kBAAA,CAAAiE,GAAA,CAAA5C,OAAA,EAAA0B,IAAA,WAAAgC,GAAA;UACAD,MAAA,CAAAzB,SAAA;YACAyB,MAAA,CAAAxB,KAAA,CAAAhB,cAAA,CAAA0C,cAAA,CAAAD,GAAA,CAAArE,IAAA,CAAAuE,WAAA;YACAH,MAAA,CAAAzC,iBAAA;UACA;QACA;MACA;IACA;IACA6C,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,WAAA,QAAA9B,KAAA,CAAAhB,cAAA,CAAA+C,cAAA;MACA,IAAAC,eAAA,QAAAhC,KAAA,CAAAhB,cAAA,CAAAiD,kBAAA;MACA,IAAAN,WAAA,MAAAO,MAAA,CAAAC,kBAAA,CAAAL,WAAA,GAAAK,kBAAA,CAAAH,eAAA;MAEArF,qBAAA,MAAAyC,WAAA,CAAArB,OAAA;QAAA4D,WAAA,EAAAA;MAAA,GAAAlC,IAAA;QACAoC,MAAA,CAAA/C,uBAAA;QACA+C,MAAA,CAAAvB,OAAA;UACAC,KAAA;UACA3B,OAAA;UACA4B,IAAA;UACAC,QAAA;QACA;QACAoB,MAAA,CAAAvC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}