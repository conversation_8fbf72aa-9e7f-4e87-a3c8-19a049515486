/**practice.wxss**/

/* 搜索栏 */
.search-section {
  margin-bottom: 20rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 25rpx;
  padding: 10rpx 20rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
}

.search-btn {
  background-color: #409EFF;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
}

/* 分类列表 */
.category-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.category-item:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.category-info {
  flex: 1;
}

.category-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.category-count {
  font-size: 24rpx;
  color: #666;
}

.category-arrow {
  margin-left: 20rpx;
}

.arrow-icon {
  color: #ccc;
  font-size: 24rpx;
}

/* 推荐练习 */
.recommend-section {
  margin-bottom: 20rpx;
}

.recommend-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.recommend-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.recommend-item:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.recommend-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background-color: #fff;
}

.recommend-info {
  flex: 1;
}

.recommend-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.recommend-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.recommend-meta {
  display: flex;
  gap: 20rpx;
}

.meta-item {
  font-size: 22rpx;
  color: #999;
  background-color: #e9ecef;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.recommend-action {
  margin-left: 20rpx;
}

.start-btn {
  width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
  padding: 0;
  line-height: 60rpx;
}

/* 快速练习 */
.quick-section {
  margin-bottom: 20rpx;
}

.quick-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.quick-item:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.quick-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  background-color: #fff;
  border-radius: 50%;
}

.quick-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 响应式布局 */
@media (max-width: 750rpx) {
  .quick-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .category-item,
  .recommend-item {
    padding: 20rpx;
  }
  
  .category-icon,
  .recommend-cover {
    width: 50rpx;
    height: 50rpx;
    font-size: 30rpx;
  }
  
  .quick-icon {
    width: 50rpx;
    height: 50rpx;
    font-size: 30rpx;
  }
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 24rpx;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}
