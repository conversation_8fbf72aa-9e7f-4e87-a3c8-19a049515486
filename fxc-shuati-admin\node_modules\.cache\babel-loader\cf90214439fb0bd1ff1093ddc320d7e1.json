{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\utils\\request.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\utils\\request.js", "mtime": 1752570649854}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "MessageBox", "Message", "store", "getToken", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "getters", "token", "headers", "concat", "error", "console", "log", "Promise", "reject", "response", "res", "data", "success", "message", "type", "duration", "status", "confirm", "confirmButtonText", "cancelButtonText", "then", "dispatch", "location", "reload", "Error"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\nimport { MessageBox, Message } from 'element-ui'\nimport store from '@/store'\nimport { getToken } from '@/utils/auth'\n\n// create an axios instance\nconst service = axios.create({\n  baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:3000', // url = base url + request url\n  // withCredentials: true, // send cookies when cross-domain requests\n  timeout: 10000 // request timeout\n})\n\n// request interceptor\nservice.interceptors.request.use(\n  config => {\n    // do something before request is sent\n\n    if (store.getters.token) {\n      // let each request carry token\n      // use Authorization header with Bearer prefix\n      config.headers['Authorization'] = `Bearer ${getToken()}`\n    }\n    return config\n  },\n  error => {\n    // do something with request error\n    console.log(error) // for debug\n    return Promise.reject(error)\n  }\n)\n\n// response interceptor\nservice.interceptors.response.use(\n  /**\n   * If you want to get http information such as headers or status\n   * Please return  response => response\n  */\n\n  /**\n   * Determine the request status by custom code\n   * Here is just an example\n   * You can also judge the status by HTTP Status Code\n   */\n  response => {\n    const res = response.data\n\n    // 检查我们的API格式：success字段\n    if (res.success === false) {\n      Message({\n        message: res.message || 'Error',\n        type: 'error',\n        duration: 5 * 1000\n      })\n\n      // 如果是认证相关错误，跳转到登录页\n      if (response.status === 401) {\n        MessageBox.confirm('登录已过期，请重新登录', '确认登出', {\n          confirmButtonText: '重新登录',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          store.dispatch('user/resetToken').then(() => {\n            location.reload()\n          })\n        })\n      }\n      return Promise.reject(new Error(res.message || 'Error'))\n    } else {\n      return res\n    }\n  },\n  error => {\n    console.log('err' + error) // for debug\n    Message({\n      message: error.message,\n      type: 'error',\n      duration: 5 * 1000\n    })\n    return Promise.reject(error)\n  }\n)\n\nexport default service\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,QAAQ,YAAY;AAChD,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;;AAEvC;AACA,IAAMC,OAAO,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,uBAAuB;EAAE;EAClE;EACAC,OAAO,EAAE,KAAK,CAAC;AACjB,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAAAC,MAAM,EAAI;EACR;;EAEA,IAAIZ,KAAK,CAACa,OAAO,CAACC,KAAK,EAAE;IACvB;IACA;IACAF,MAAM,CAACG,OAAO,CAAC,eAAe,CAAC,aAAAC,MAAA,CAAaf,QAAQ,CAAC,CAAC,CAAE;EAC1D;EACA,OAAOW,MAAM;AACf,CAAC,EACD,UAAAK,KAAK,EAAI;EACP;EACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAC;EACnB,OAAOG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,OAAO,CAACO,YAAY,CAACa,QAAQ,CAACX,GAAG;AAC/B;AACF;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACE,UAAAW,QAAQ,EAAI;EACV,IAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEzB;EACA,IAAID,GAAG,CAACE,OAAO,KAAK,KAAK,EAAE;IACzB1B,OAAO,CAAC;MACN2B,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI,OAAO;MAC/BC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;;IAEF;IACA,IAAIN,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MAC3B/B,UAAU,CAACgC,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE;QACxCC,iBAAiB,EAAE,MAAM;QACzBC,gBAAgB,EAAE,IAAI;QACtBL,IAAI,EAAE;MACR,CAAC,CAAC,CAACM,IAAI,CAAC,YAAM;QACZjC,KAAK,CAACkC,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;UAC3CE,QAAQ,CAACC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,OAAOhB,OAAO,CAACC,MAAM,CAAC,IAAIgB,KAAK,CAACd,GAAG,CAACG,OAAO,IAAI,OAAO,CAAC,CAAC;EAC1D,CAAC,MAAM;IACL,OAAOH,GAAG;EACZ;AACF,CAAC,EACD,UAAAN,KAAK,EAAI;EACPC,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGF,KAAK,CAAC,EAAC;EAC3BlB,OAAO,CAAC;IACN2B,OAAO,EAAET,KAAK,CAACS,OAAO;IACtBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CAAC,GAAG;EAChB,CAAC,CAAC;EACF,OAAOR,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAef,OAAO", "ignoreList": []}]}