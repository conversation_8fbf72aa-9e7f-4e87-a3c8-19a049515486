{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\utils\\request.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\utils\\request.js", "mtime": 1752570418639}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "MessageBox", "Message", "store", "getToken", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "getters", "token", "headers", "error", "console", "log", "Promise", "reject", "response", "res", "data", "success", "message", "type", "duration", "status", "confirm", "confirmButtonText", "cancelButtonText", "then", "dispatch", "location", "reload", "Error"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\nimport { MessageBox, Message } from 'element-ui'\nimport store from '@/store'\nimport { getToken } from '@/utils/auth'\n\n// create an axios instance\nconst service = axios.create({\n  baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:3000', // url = base url + request url\n  // withCredentials: true, // send cookies when cross-domain requests\n  timeout: 10000 // request timeout\n})\n\n// request interceptor\nservice.interceptors.request.use(\n  config => {\n    // do something before request is sent\n\n    if (store.getters.token) {\n      // let each request carry token\n      // ['X-Token'] is a custom headers key\n      // please modify it according to the actual situation\n      config.headers['X-Token'] = getToken()\n    }\n    return config\n  },\n  error => {\n    // do something with request error\n    console.log(error) // for debug\n    return Promise.reject(error)\n  }\n)\n\n// response interceptor\nservice.interceptors.response.use(\n  /**\n   * If you want to get http information such as headers or status\n   * Please return  response => response\n  */\n\n  /**\n   * Determine the request status by custom code\n   * Here is just an example\n   * You can also judge the status by HTTP Status Code\n   */\n  response => {\n    const res = response.data\n\n    // 检查我们的API格式：success字段\n    if (res.success === false) {\n      Message({\n        message: res.message || 'Error',\n        type: 'error',\n        duration: 5 * 1000\n      })\n\n      // 如果是认证相关错误，跳转到登录页\n      if (response.status === 401) {\n        MessageBox.confirm('登录已过期，请重新登录', '确认登出', {\n          confirmButtonText: '重新登录',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          store.dispatch('user/resetToken').then(() => {\n            location.reload()\n          })\n        })\n      }\n      return Promise.reject(new Error(res.message || 'Error'))\n    } else {\n      return res\n    }\n  },\n  error => {\n    console.log('err' + error) // for debug\n    Message({\n      message: error.message,\n      type: 'error',\n      duration: 5 * 1000\n    })\n    return Promise.reject(error)\n  }\n)\n\nexport default service\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,QAAQ,YAAY;AAChD,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;;AAEvC;AACA,IAAMC,OAAO,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,uBAAuB;EAAE;EAClE;EACAC,OAAO,EAAE,KAAK,CAAC;AACjB,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAAAC,MAAM,EAAI;EACR;;EAEA,IAAIZ,KAAK,CAACa,OAAO,CAACC,KAAK,EAAE;IACvB;IACA;IACA;IACAF,MAAM,CAACG,OAAO,CAAC,SAAS,CAAC,GAAGd,QAAQ,CAAC,CAAC;EACxC;EACA,OAAOW,MAAM;AACf,CAAC,EACD,UAAAI,KAAK,EAAI;EACP;EACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAC;EACnB,OAAOG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,OAAO,CAACO,YAAY,CAACY,QAAQ,CAACV,GAAG;AAC/B;AACF;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACE,UAAAU,QAAQ,EAAI;EACV,IAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEzB;EACA,IAAID,GAAG,CAACE,OAAO,KAAK,KAAK,EAAE;IACzBzB,OAAO,CAAC;MACN0B,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI,OAAO;MAC/BC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;;IAEF;IACA,IAAIN,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MAC3B9B,UAAU,CAAC+B,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE;QACxCC,iBAAiB,EAAE,MAAM;QACzBC,gBAAgB,EAAE,IAAI;QACtBL,IAAI,EAAE;MACR,CAAC,CAAC,CAACM,IAAI,CAAC,YAAM;QACZhC,KAAK,CAACiC,QAAQ,CAAC,iBAAiB,CAAC,CAACD,IAAI,CAAC,YAAM;UAC3CE,QAAQ,CAACC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,OAAOhB,OAAO,CAACC,MAAM,CAAC,IAAIgB,KAAK,CAACd,GAAG,CAACG,OAAO,IAAI,OAAO,CAAC,CAAC;EAC1D,CAAC,MAAM;IACL,OAAOH,GAAG;EACZ;AACF,CAAC,EACD,UAAAN,KAAK,EAAI;EACPC,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGF,KAAK,CAAC,EAAC;EAC3BjB,OAAO,CAAC;IACN0B,OAAO,EAAET,KAAK,CAACS,OAAO;IACtBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,CAAC,GAAG;EAChB,CAAC,CAAC;EACF,OAAOR,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAed,OAAO", "ignoreList": []}]}