// 模拟数据
const { getQuestionTypeText, getDifficultyText, getOptionLabel } = require('./util.js')

// 模拟题目数据
const mockQuestions = [
  {
    question_id: 1,
    question_type: 'single',
    question_content: 'What is the capital of France?',
    question_difficulty: 1,
    question_subject: '考研英语一',
    question_chapter: '阅读理解',
    options: [
      { option_content: 'Paris', is_correct: 1 },
      { option_content: 'London', is_correct: 0 },
      { option_content: 'Berlin', is_correct: 0 },
      { option_content: 'Madrid', is_correct: 0 }
    ],
    explanation: {
      explanation_content: 'Paris is the capital and most populous city of France.'
    }
  },
  {
    question_id: 2,
    question_type: 'multiple',
    question_content: 'Which of the following cities are in France?',
    question_difficulty: 2,
    question_subject: '考研英语一',
    question_chapter: '阅读理解',
    options: [
      { option_content: 'Paris', is_correct: 1 },
      { option_content: 'Lyon', is_correct: 1 },
      { option_content: 'Berlin', is_correct: 0 },
      { option_content: 'Marseille', is_correct: 1 }
    ],
    explanation: {
      explanation_content: 'Paris, Lyon, and Marseille are all cities in France. Berlin is in Germany.'
    }
  },
  {
    question_id: 3,
    question_type: 'judge',
    question_content: 'The Eiffel Tower is located in London.',
    question_difficulty: 1,
    question_subject: '考研英语二',
    question_chapter: '阅读理解',
    options: [
      { option_content: '正确', is_correct: 0 },
      { option_content: '错误', is_correct: 1 }
    ],
    explanation: {
      explanation_content: 'The Eiffel Tower is located in Paris, France, not London.'
    }
  },
  {
    question_id: 4,
    question_type: 'fill',
    question_content: 'The capital of France is __________.',
    question_difficulty: 1,
    question_subject: '考研英语二',
    question_chapter: '完型填空',
    correct_answer: 'Paris',
    explanation: {
      explanation_content: 'Paris is the capital city of France.'
    }
  },
  {
    question_id: 5,
    question_type: 'essay',
    question_content: 'Please describe the importance of learning English.',
    question_difficulty: 3,
    question_subject: '考研英语一',
    question_chapter: '写作',
    explanation: {
      explanation_content: 'This is an open-ended question. Students should discuss various aspects of English learning importance.'
    }
  }
]

// 模拟分类数据
const mockCategories = [
  {
    id: 1,
    name: '考研英语一',
    icon: '📖',
    questionCount: 1250,
    description: '考研英语一真题及模拟题'
  },
  {
    id: 2,
    name: '考研英语二',
    icon: '📚',
    questionCount: 980,
    description: '考研英语二真题及模拟题'
  },
  {
    id: 3,
    name: '阅读理解',
    icon: '👁️',
    questionCount: 650,
    description: '阅读理解专项练习'
  },
  {
    id: 4,
    name: '完型填空',
    icon: '✏️',
    questionCount: 320,
    description: '完型填空专项练习'
  },
  {
    id: 5,
    name: '翻译',
    icon: '🔄',
    questionCount: 180,
    description: '英译汉专项练习'
  },
  {
    id: 6,
    name: '写作',
    icon: '✍️',
    questionCount: 120,
    description: '英语写作专项练习'
  }
]

// 模拟推荐练习
const mockRecommendList = [
  {
    id: 1,
    title: '2023年考研英语一真题',
    description: '最新真题，全面覆盖考点',
    cover: '🎯',
    questionCount: 50,
    difficulty: 3
  },
  {
    id: 2,
    title: '阅读理解专项训练',
    description: '提升阅读理解能力',
    cover: '📖',
    questionCount: 30,
    difficulty: 2
  },
  {
    id: 3,
    title: '语法基础强化',
    description: '巩固语法基础知识',
    cover: '📝',
    questionCount: 40,
    difficulty: 1
  }
]

// 模拟用户统计数据
const mockUserStats = {
  totalQuestions: 156,
  correctRate: 78,
  studyDays: 15,
  rank: 128,
  favoriteCount: 23,
  errorCount: 12,
  achievementCount: 5
}

// 模拟收藏数据
const mockFavorites = [
  {
    favorite_id: 1,
    question_id: 1,
    created_at: '2023-12-01T00:00:00.000Z',
    question: mockQuestions[0]
  },
  {
    favorite_id: 2,
    question_id: 3,
    created_at: '2023-12-02T00:00:00.000Z',
    question: mockQuestions[2]
  }
]

// 模拟错题数据
const mockErrors = [
  {
    error_id: 1,
    question_id: 2,
    user_answer: 'Paris, Berlin',
    correct_answer: 'Paris, Lyon, Marseille',
    error_count: 2,
    last_error_time: '2023-12-01T10:30:00.000Z',
    question: mockQuestions[1]
  },
  {
    error_id: 2,
    question_id: 4,
    user_answer: 'London',
    correct_answer: 'Paris',
    error_count: 1,
    last_error_time: '2023-12-01T15:20:00.000Z',
    question: mockQuestions[3]
  }
]

// 模拟错题统计
const mockErrorStats = {
  total_errors: 15,
  accuracy_rate: 68,
  recent_errors: 5,
  review_count: 8
}

// 处理题目数据
function processQuestions(questions) {
  return questions.map(q => ({
    ...q,
    typeText: getQuestionTypeText(q.question_type),
    difficultyText: getDifficultyText(q.question_difficulty),
    options: q.options ? q.options.map((opt, index) => ({
      ...opt,
      label: getOptionLabel(index),
      selected: false
    })) : null
  }))
}

// 处理推荐列表
function processRecommendList(list) {
  return list.map(item => ({
    ...item,
    difficultyText: getDifficultyText(item.difficulty)
  }))
}

module.exports = {
  mockQuestions,
  mockCategories,
  mockRecommendList,
  mockUserStats,
  mockFavorites,
  mockErrors,
  mockErrorStats,
  processQuestions,
  processRecommendList
}
