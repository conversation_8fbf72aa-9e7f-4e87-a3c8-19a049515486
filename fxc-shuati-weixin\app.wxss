/**app.wxss**/
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用容器 */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 通用卡片 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 通用按钮 */
.btn {
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #409EFF;
  color: #fff;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

.btn-success {
  background-color: #67C23A;
  color: #fff;
}

.btn-danger {
  background-color: #F56C6C;
  color: #fff;
}

.btn-warning {
  background-color: #E6A23C;
  color: #fff;
}

.btn:disabled {
  opacity: 0.5;
}

/* 文本样式 */
.text-primary {
  color: #409EFF;
}

.text-success {
  color: #67C23A;
}

.text-danger {
  color: #F56C6C;
}

.text-warning {
  color: #E6A23C;
}

.text-muted {
  color: #999;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 字体大小 */
.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 40rpx;
}

/* 布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 宽高 */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 圆角 */
.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影 */
.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}
