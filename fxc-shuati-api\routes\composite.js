const express = require('express');
const { query } = require('../config/database');
const { authenticateAdmin } = require('./auth');

const router = express.Router();

// Mock 组合题数据
const compositeQuestions = [
  {
    composite_id: 1,
    category_id: 4,
    composite_title: "Climate Change Reading Comprehension",
    composite_content: `<p>Climate change is one of the most pressing issues facing our planet today. The Earth's average temperature has increased by about 1°C since pre-industrial times, primarily due to human activities such as burning fossil fuels, deforestation, and industrial processes that release greenhouse gases into the atmosphere.</p>
    <p>Scientists have observed various effects of climate change, including rising sea levels, more frequent and intense heat waves, changes in precipitation patterns, and more severe storms. These changes have significant impacts on ecosystems, agriculture, human health, and infrastructure.</p>
    <p>While there are natural factors that can influence climate, such as volcanic eruptions and changes in solar radiation, the scientific consensus is that human activities are the dominant cause of the observed warming since the mid-20th century.</p>`,
    composite_difficulty: 3,
    status: "active",
    created_at: "2023-01-04T00:00:00.000Z",
    updated_at: "2023-01-04T00:00:00.000Z"
  }
];

// Mock 组合题关联数据
const compositeQuestionLinks = [
  {
    link_id: 1,
    composite_id: 1,
    question_id: 4,
    question_order: 1,
    created_at: "2023-01-04T00:00:00.000Z",
    updated_at: "2023-01-04T00:00:00.000Z"
  },
  {
    link_id: 2,
    composite_id: 1,
    question_id: 5,
    question_order: 2,
    created_at: "2023-01-04T00:00:00.000Z",
    updated_at: "2023-01-04T00:00:00.000Z"
  }
];

// Mock 题目数据（简化版）
const questions = [
  {
    question_id: 4,
    question_type: 'judge',
    question_content: "Climate change is primarily caused by human activities.",
    question_difficulty: 2,
    options: [
      { option_content: "True", is_correct: 1 },
      { option_content: "False", is_correct: 0 }
    ]
  },
  {
    question_id: 5,
    question_type: 'single',
    question_content: "What is the main cause of rising sea levels?",
    question_difficulty: 3,
    options: [
      { option_content: "Thermal expansion of seawater", is_correct: 1 },
      { option_content: "Underwater earthquakes", is_correct: 0 },
      { option_content: "Ocean currents", is_correct: 0 },
      { option_content: "Tidal forces", is_correct: 0 }
    ]
  }
];

// 获取组合题列表
router.get('/', async (req, res) => {
  try {
    const { category_id, status = 'active', page = 1, limit = 20 } = req.query;

    let filteredComposites = compositeQuestions.filter(comp => comp.status === status);

    if (category_id) {
      filteredComposites = filteredComposites.filter(comp => 
        comp.category_id === parseInt(category_id)
      );
    }

    // 分页处理
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedComposites = filteredComposites.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        composites: paginatedComposites,
        pagination: {
          current_page: pageNum,
          per_page: pageSize,
          total: filteredComposites.length,
          total_pages: Math.ceil(filteredComposites.length / pageSize)
        }
      },
      message: '获取组合题列表成功'
    });

  } catch (error) {
    console.error('获取组合题列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取组合题列表失败'
    });
  }
});

// 获取组合题详情
router.get('/:id', async (req, res) => {
  try {
    const compositeId = parseInt(req.params.id);
    const composite = compositeQuestions.find(c => c.composite_id === compositeId);

    if (!composite) {
      return res.status(404).json({
        success: false,
        message: '组合题不存在'
      });
    }

    // 获取关联的题目
    const links = compositeQuestionLinks.filter(l => l.composite_id === compositeId);
    const linkedQuestions = links.map(link => {
      const question = questions.find(q => q.question_id === link.question_id);
      return {
        ...question,
        question_order: link.question_order
      };
    }).filter(q => q.question_id).sort((a, b) => a.question_order - b.question_order);

    res.json({
      success: true,
      data: {
        ...composite,
        questions: linkedQuestions
      },
      message: '获取组合题详情成功'
    });

  } catch (error) {
    console.error('获取组合题详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取组合题详情失败'
    });
  }
});

// 创建组合题（管理员）
router.post('/', authenticateAdmin, async (req, res) => {
  try {
    const {
      category_id,
      composite_title,
      composite_content,
      composite_difficulty = 1,
      question_ids = []
    } = req.body;

    if (!composite_title || !composite_content) {
      return res.status(400).json({
        success: false,
        message: '组合题标题和内容不能为空'
      });
    }

    const newComposite = {
      composite_id: compositeQuestions.length + 1,
      category_id: parseInt(category_id) || 1,
      composite_title,
      composite_content,
      composite_difficulty: parseInt(composite_difficulty),
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    compositeQuestions.push(newComposite);

    // 添加题目关联
    if (question_ids.length > 0) {
      question_ids.forEach((questionId, index) => {
        const newLink = {
          link_id: compositeQuestionLinks.length + index + 1,
          composite_id: newComposite.composite_id,
          question_id: parseInt(questionId),
          question_order: index + 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        compositeQuestionLinks.push(newLink);
      });
    }

    res.json({
      success: true,
      data: newComposite,
      message: '创建组合题成功'
    });

  } catch (error) {
    console.error('创建组合题错误:', error);
    res.status(500).json({
      success: false,
      message: '创建组合题失败'
    });
  }
});

// 更新组合题（管理员）
router.put('/:id', authenticateAdmin, async (req, res) => {
  try {
    const compositeId = parseInt(req.params.id);
    const {
      composite_title,
      composite_content,
      composite_difficulty,
      status
    } = req.body;

    const compositeIndex = compositeQuestions.findIndex(c => c.composite_id === compositeId);
    if (compositeIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '组合题不存在'
      });
    }

    const composite = compositeQuestions[compositeIndex];

    // 更新组合题信息
    if (composite_title) composite.composite_title = composite_title;
    if (composite_content) composite.composite_content = composite_content;
    if (composite_difficulty) composite.composite_difficulty = parseInt(composite_difficulty);
    if (status) composite.status = status;
    composite.updated_at = new Date().toISOString();

    res.json({
      success: true,
      data: composite,
      message: '更新组合题成功'
    });

  } catch (error) {
    console.error('更新组合题错误:', error);
    res.status(500).json({
      success: false,
      message: '更新组合题失败'
    });
  }
});

// 删除组合题（管理员）
router.delete('/:id', authenticateAdmin, async (req, res) => {
  try {
    const compositeId = parseInt(req.params.id);

    const compositeIndex = compositeQuestions.findIndex(c => c.composite_id === compositeId);
    if (compositeIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '组合题不存在'
      });
    }

    // 软删除
    compositeQuestions[compositeIndex].status = 'disabled';
    compositeQuestions[compositeIndex].updated_at = new Date().toISOString();

    res.json({
      success: true,
      message: '删除组合题成功'
    });

  } catch (error) {
    console.error('删除组合题错误:', error);
    res.status(500).json({
      success: false,
      message: '删除组合题失败'
    });
  }
});

module.exports = router;
