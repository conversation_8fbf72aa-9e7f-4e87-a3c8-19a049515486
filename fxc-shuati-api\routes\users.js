const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, authenticateAdmin } = require('./auth');

const router = express.Router();

// Mock 用户数据
const users = [
  {
    user_id: 1,
    openid: 'mock_openid_001',
    nickname: '测试用户1',
    avatar_url: 'https://example.com/avatar1.jpg',
    phone: '',
    email: '',
    is_vip: false,
    vip_expire_time: null,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    user_id: 2,
    openid: 'mock_openid_002',
    nickname: '测试用户2',
    avatar_url: 'https://example.com/avatar2.jpg',
    phone: '13800138000',
    email: '<EMAIL>',
    is_vip: true,
    vip_expire_time: '2024-12-31T23:59:59.000Z',
    status: 'active',
    created_at: '2023-01-02T00:00:00.000Z',
    updated_at: '2023-01-02T00:00:00.000Z'
  }
];

// 获取用户列表（管理员）
router.get('/', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, keyword = '', status = '' } = req.query;

    let filteredUsers = [...users];

    // 关键词搜索
    if (keyword) {
      filteredUsers = filteredUsers.filter(user => 
        user.nickname.includes(keyword) || 
        user.phone.includes(keyword) || 
        user.email.includes(keyword)
      );
    }

    // 状态筛选
    if (status) {
      filteredUsers = filteredUsers.filter(user => user.status === status);
    }

    // 分页处理
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        users: paginatedUsers,
        pagination: {
          current_page: pageNum,
          per_page: pageSize,
          total: filteredUsers.length,
          total_pages: Math.ceil(filteredUsers.length / pageSize)
        }
      },
      message: '获取用户列表成功'
    });

  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

// 获取当前用户信息
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const user = users.find(u => u.user_id === userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user,
      message: '获取用户信息成功'
    });

  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 更新用户信息
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { nickname, phone, email } = req.body;

    const userIndex = users.findIndex(u => u.user_id === userId);
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    if (nickname) users[userIndex].nickname = nickname;
    if (phone) users[userIndex].phone = phone;
    if (email) users[userIndex].email = email;
    users[userIndex].updated_at = new Date().toISOString();

    res.json({
      success: true,
      data: users[userIndex],
      message: '更新用户信息成功'
    });

  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '更新用户信息失败'
    });
  }
});

// 获取用户统计信息
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;

    // Mock 统计数据
    const stats = {
      total_questions: 150,
      answered_questions: 45,
      correct_answers: 38,
      wrong_answers: 7,
      accuracy_rate: 84.4,
      favorite_count: 12,
      error_count: 7,
      study_days: 15,
      total_study_time: 1800 // 秒
    };

    res.json({
      success: true,
      data: stats,
      message: '获取用户统计成功'
    });

  } catch (error) {
    console.error('获取用户统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计失败'
    });
  }
});

// 管理员更新用户状态
router.put('/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { status } = req.body;

    if (!['active', 'banned'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }

    const userIndex = users.findIndex(u => u.user_id === userId);
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    users[userIndex].status = status;
    users[userIndex].updated_at = new Date().toISOString();

    res.json({
      success: true,
      data: users[userIndex],
      message: '更新用户状态成功'
    });

  } catch (error) {
    console.error('更新用户状态错误:', error);
    res.status(500).json({
      success: false,
      message: '更新用户状态失败'
    });
  }
});

// 管理员设置用户VIP
router.put('/:id/vip', authenticateAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { is_vip, vip_expire_time } = req.body;

    const userIndex = users.findIndex(u => u.user_id === userId);
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    users[userIndex].is_vip = is_vip;
    users[userIndex].vip_expire_time = vip_expire_time;
    users[userIndex].updated_at = new Date().toISOString();

    res.json({
      success: true,
      data: users[userIndex],
      message: '设置用户VIP成功'
    });

  } catch (error) {
    console.error('设置用户VIP错误:', error);
    res.status(500).json({
      success: false,
      message: '设置用户VIP失败'
    });
  }
});

module.exports = router;
