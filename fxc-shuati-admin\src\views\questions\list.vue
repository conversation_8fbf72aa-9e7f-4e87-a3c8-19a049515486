<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索题目内容"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.question_type"
          placeholder="题目类型"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="单选题" value="single_choice" />
          <el-option label="多选题" value="multiple_choice" />
          <el-option label="判断题" value="true_false" />
          <el-option label="填空题" value="fill_blank" />
          <el-option label="简答题" value="short_answer" />
          <el-option label="论述题" value="essay" />
        </el-select>
        <el-select
          v-model="listQuery.difficulty"
          placeholder="难度等级"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="简单" value="easy" />
          <el-option label="中等" value="medium" />
          <el-option label="困难" value="hard" />
        </el-select>
        <el-select
          v-model="listQuery.category_id"
          placeholder="题目分类"
          clearable
          style="width: 150px"
          class="filter-item"
        >
          <el-option
            v-for="category in categoryOptions"
            :key="category.category_id"
            :label="category.category_name"
            :value="category.category_id"
          />
        </el-select>
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加题目
        </el-button>
        <el-button
          class="filter-item"
          type="success"
          icon="el-icon-upload"
          @click="handleImport"
        >
          批量导入
        </el-button>
      </div>
    </el-card>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="question_id" align="center" width="80" />
      <el-table-column label="题目内容" prop="question_content" min-width="300">
        <template slot-scope="{row}">
          <div class="question-content">
            {{ row.question_content.length > 100 ? row.question_content.substring(0, 100) + '...' : row.question_content }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="题目类型" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="typeTagMap[row.question_type]">
            {{ typeTextMap[row.question_type] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="难度" width="80" align="center">
        <template slot-scope="{row}">
          <el-tag :type="difficultyTagMap[row.difficulty]" size="mini">
            {{ difficultyTextMap[row.difficulty] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分类" prop="category_name" min-width="120" />
      <el-table-column label="标签" min-width="150">
        <template slot-scope="{row}">
          <el-tag
            v-for="tag in row.tags"
            :key="tag.tag_id"
            size="mini"
            style="margin-right: 5px;"
          >
            {{ tag.tag_name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分值" prop="score" width="80" align="center" />
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="120" align="center">
        <template slot-scope="{row}">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button type="info" size="mini" @click="handleView(row)">
            查看
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 查看题目对话框 -->
    <el-dialog title="题目详情" :visible.sync="viewDialogVisible" width="60%">
      <div v-if="currentQuestion" class="question-detail">
        <div class="detail-item">
          <label>题目类型：</label>
          <el-tag :type="typeTagMap[currentQuestion.question_type]">
            {{ typeTextMap[currentQuestion.question_type] }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>难度等级：</label>
          <el-tag :type="difficultyTagMap[currentQuestion.difficulty]">
            {{ difficultyTextMap[currentQuestion.difficulty] }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>题目内容：</label>
          <div class="content">{{ currentQuestion.question_content }}</div>
        </div>
        <div v-if="currentQuestion.options && currentQuestion.options.length > 0" class="detail-item">
          <label>选项：</label>
          <div class="options">
            <div
              v-for="(option, index) in currentQuestion.options"
              :key="index"
              class="option"
            >
              {{ String.fromCharCode(65 + index) }}. {{ option.content }}
            </div>
          </div>
        </div>
        <div class="detail-item">
          <label>正确答案：</label>
          <div class="answer">{{ currentQuestion.correct_answer }}</div>
        </div>
        <div v-if="currentQuestion.explanation" class="detail-item">
          <label>题目解析：</label>
          <div class="explanation">{{ currentQuestion.explanation }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { formatDate } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'QuestionList',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 20,
        keyword: '',
        question_type: '',
        difficulty: '',
        category_id: '',
        status: ''
      },
      categoryOptions: [],
      viewDialogVisible: false,
      currentQuestion: null,
      typeTagMap: {
        single_choice: 'primary',
        multiple_choice: 'success',
        true_false: 'warning',
        fill_blank: 'info',
        short_answer: 'danger',
        essay: 'danger'
      },
      typeTextMap: {
        single_choice: '单选题',
        multiple_choice: '多选题',
        true_false: '判断题',
        fill_blank: '填空题',
        short_answer: '简答题',
        essay: '论述题'
      },
      difficultyTagMap: {
        easy: 'success',
        medium: 'warning',
        hard: 'danger'
      },
      difficultyTextMap: {
        easy: '简单',
        medium: '中等',
        hard: '困难'
      }
    }
  },
  created() {
    this.getList()
    this.loadCategoryOptions()
  },
  methods: {
    getList() {
      this.listLoading = true

      // 模拟题目数据
      setTimeout(() => {
        this.list = [
          // 马克思主义基本原理 - 哲学部分
          {
            question_id: 1,
            question_content: '马克思主义哲学的基本问题是（）',
            question_type: 'single_choice',
            difficulty: 'medium',
            category_id: 1111,
            category_name: '物质概念',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 9, tag_name: '哲学原理' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '物质和意识的关系问题' },
              { content: '理论和实践的关系问题' },
              { content: '个人和社会的关系问题' },
              { content: '自由和必然的关系问题' }
            ],
            correct_answer: 'A',
            explanation: '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。它包括两个方面：第一，物质和意识何者为第一性；第二，物质和意识是否具有同一性。',
            score: 2,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 2,
            question_content: '物质的唯一特性是（）',
            question_type: 'single_choice',
            difficulty: 'easy',
            category_id: 1111,
            category_name: '物质概念',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 9, tag_name: '哲学原理' },
              { tag_id: 16, tag_name: '2023年真题' }
            ],
            options: [
              { content: '运动性' },
              { content: '客观实在性' },
              { content: '可知性' },
              { content: '绝对性' }
            ],
            correct_answer: 'B',
            explanation: '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在，并能为人的意识所反映。',
            score: 2,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 3,
            question_content: '意识的本质是（）',
            question_type: 'multiple_choice',
            difficulty: 'medium',
            category_id: 1112,
            category_name: '意识本质',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 9, tag_name: '哲学原理' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '意识是人脑的机能' },
              { content: '意识是客观存在的反映' },
              { content: '意识是社会的产物' },
              { content: '意识具有主观能动性' }
            ],
            correct_answer: 'A,B,C,D',
            explanation: '意识的本质包括：（1）意识是人脑的机能；（2）意识是客观存在的反映；（3）意识是社会的产物；（4）意识具有主观能动性。这四个方面构成了意识本质的完整内容。',
            score: 2,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 4,
            question_content: '马克思主义哲学认为，世界的真正统一性在于它的物质性。',
            question_type: 'true_false',
            difficulty: 'easy',
            category_id: 1111,
            category_name: '物质概念',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 9, tag_name: '哲学原理' }
            ],
            options: [],
            correct_answer: 'true',
            explanation: '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。这是马克思主义一元论的基本观点，强调世界万物都是物质的不同表现形式。',
            score: 2,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 5,
            question_content: '联系的特点包括（）',
            question_type: 'multiple_choice',
            difficulty: 'medium',
            category_id: 1121,
            category_name: '联系观',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 9, tag_name: '哲学原理' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '客观性' },
              { content: '普遍性' },
              { content: '多样性' },
              { content: '条件性' }
            ],
            correct_answer: 'A,B,C,D',
            explanation: '联系具有客观性、普遍性、多样性和条件性等特点。客观性指联系是事物本身所固有的；普遍性指任何事物都处在联系之中；多样性指联系的形式多种多样；条件性指联系是有条件的。',
            score: 2,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 6,
            question_content: '矛盾的基本属性是_____和_____。',
            question_type: 'fill_blank',
            difficulty: 'easy',
            category_id: 1123,
            category_name: '矛盾规律',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 9, tag_name: '哲学原理' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [],
            correct_answer: '同一性；斗争性',
            explanation: '矛盾的基本属性是同一性和斗争性。同一性是指矛盾双方相互依存、相互贯通的性质和趋势；斗争性是指矛盾双方相互排斥、相互对立的性质和趋势。',
            score: 2,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 7,
            question_content: '实践是认识的基础，主要表现在（）',
            question_type: 'multiple_choice',
            difficulty: 'medium',
            category_id: 113,
            category_name: '认识论',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 9, tag_name: '哲学原理' },
              { tag_id: 6, tag_name: '重点难点' }
            ],
            options: [
              { content: '实践是认识的来源' },
              { content: '实践是认识发展的动力' },
              { content: '实践是检验认识真理性的唯一标准' },
              { content: '实践是认识的目的' }
            ],
            correct_answer: 'A,B,C,D',
            explanation: '实践是认识的基础，主要表现在四个方面：（1）实践是认识的来源；（2）实践是认识发展的动力；（3）实践是检验认识真理性的唯一标准；（4）实践是认识的目的。',
            score: 2,
            status: 'active',
            created_at: '2024-01-15'
          },
          // 毛泽东思想部分
          {
            question_id: 8,
            question_content: '毛泽东思想形成的时代背景是（）',
            question_type: 'single_choice',
            difficulty: 'medium',
            category_id: 21,
            category_name: '毛泽东思想',
            tags: [
              { tag_id: 2, tag_name: '毛泽东思想' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '帝国主义和无产阶级革命的时代' },
              { content: '资本主义向社会主义过渡的时代' },
              { content: '和平与发展的时代' },
              { content: '全球化的时代' }
            ],
            correct_answer: 'A',
            explanation: '毛泽东思想形成的时代背景是帝国主义和无产阶级革命的时代。这一时代特征为毛泽东思想的形成和发展提供了重要的历史条件。',
            score: 2,
            status: 'active',
            created_at: '2024-01-16'
          },
          {
            question_id: 9,
            question_content: '新民主主义革命的总路线是（）',
            question_type: 'single_choice',
            difficulty: 'hard',
            category_id: 21,
            category_name: '毛泽东思想',
            tags: [
              { tag_id: 2, tag_name: '毛泽东思想' },
              { tag_id: 12, tag_name: '新民主主义革命' },
              { tag_id: 6, tag_name: '重点难点' }
            ],
            options: [
              { content: '无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命' },
              { content: '工人阶级领导的，以工农联盟为基础的人民民主专政' },
              { content: '中国共产党领导的多党合作和政治协商制度' },
              { content: '人民当家作主的社会主义民主政治' }
            ],
            correct_answer: 'A',
            explanation: '新民主主义革命的总路线是：无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命。这是毛泽东对新民主主义革命本质的科学概括。',
            score: 2,
            status: 'active',
            created_at: '2024-01-16'
          },
          {
            question_id: 10,
            question_content: '中国革命的基本问题是（）',
            question_type: 'single_choice',
            difficulty: 'medium',
            category_id: 21,
            category_name: '毛泽东思想',
            tags: [
              { tag_id: 2, tag_name: '毛泽东思想' },
              { tag_id: 12, tag_name: '新民主主义革命' },
              { tag_id: 17, tag_name: '2022年真题' }
            ],
            options: [
              { content: '农民问题' },
              { content: '武装斗争问题' },
              { content: '统一战线问题' },
              { content: '党的建设问题' }
            ],
            correct_answer: 'A',
            explanation: '中国革命的基本问题是农民问题。毛泽东指出，农民问题乃国民革命的中心问题，农民是中国革命的主力军，农民问题是中国革命的基本问题。',
            score: 2,
            status: 'active',
            created_at: '2024-01-16'
          },
          // 中国近现代史纲要部分
          {
            question_id: 11,
            question_content: '中国近代史的起点是（）',
            question_type: 'single_choice',
            difficulty: 'easy',
            category_id: 31,
            category_name: '旧民主主义革命时期',
            tags: [
              { tag_id: 3, tag_name: '中国近现代史纲要' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '1840年鸦片战争' },
              { content: '1842年《南京条约》签订' },
              { content: '1851年太平天国运动' },
              { content: '1894年甲午中日战争' }
            ],
            correct_answer: 'A',
            explanation: '中国近代史的起点是1840年鸦片战争。鸦片战争是中国历史的转折点，标志着中国开始沦为半殖民地半封建社会，中国近代史由此开始。',
            score: 2,
            status: 'active',
            created_at: '2024-01-17'
          },
          {
            question_id: 12,
            question_content: '五四运动的历史意义包括（）',
            question_type: 'multiple_choice',
            difficulty: 'medium',
            category_id: 32,
            category_name: '新民主主义革命时期',
            tags: [
              { tag_id: 3, tag_name: '中国近现代史纲要' },
              { tag_id: 12, tag_name: '新民主主义革命' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '是中国新民主主义革命的开端' },
              { content: '促进了马克思主义在中国的传播' },
              { content: '促进了马克思主义与中国工人运动的结合' },
              { content: '为中国共产党的成立作了思想和干部准备' }
            ],
            correct_answer: 'A,B,C,D',
            explanation: '五四运动的历史意义重大：（1）是中国新民主主义革命的开端；（2）促进了马克思主义在中国的传播；（3）促进了马克思主义与中国工人运动的结合；（4）为中国共产党的成立作了思想和干部准备。',
            score: 2,
            status: 'active',
            created_at: '2024-01-17'
          },
          // 思想道德与法治部分
          {
            question_id: 13,
            question_content: '理想信念的作用主要表现在（）',
            question_type: 'multiple_choice',
            difficulty: 'medium',
            category_id: 4,
            category_name: '思想道德与法治',
            tags: [
              { tag_id: 4, tag_name: '思想道德与法治' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '指引人生的奋斗目标' },
              { content: '提供人生的前进动力' },
              { content: '提高人生的精神境界' },
              { content: '增强人生的使命感' }
            ],
            correct_answer: 'A,B,C',
            explanation: '理想信念的作用主要表现在三个方面：（1）指引人生的奋斗目标；（2）提供人生的前进动力；（3）提高人生的精神境界。理想信念是人生的精神支柱和前进动力。',
            score: 2,
            status: 'active',
            created_at: '2024-01-18'
          },
          {
            question_id: 14,
            question_content: '社会主义核心价值观的基本内容是_____、_____、_____。',
            question_type: 'fill_blank',
            difficulty: 'easy',
            category_id: 4,
            category_name: '思想道德与法治',
            tags: [
              { tag_id: 4, tag_name: '思想道德与法治' },
              { tag_id: 15, tag_name: '新时代' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [],
            correct_answer: '富强、民主、文明、和谐；自由、平等、公正、法治；爱国、敬业、诚信、友善',
            explanation: '社会主义核心价值观的基本内容是：国家层面的价值要求是富强、民主、文明、和谐；社会层面的价值要求是自由、平等、公正、法治；个人层面的价值要求是爱国、敬业、诚信、友善。',
            score: 3,
            status: 'active',
            created_at: '2024-01-18'
          },
          {
            question_id: 15,
            question_content: '请论述新时代爱国主义的基本要求。',
            question_type: 'essay',
            difficulty: 'hard',
            category_id: 4,
            category_name: '思想道德与法治',
            tags: [
              { tag_id: 4, tag_name: '思想道德与法治' },
              { tag_id: 15, tag_name: '新时代' },
              { tag_id: 6, tag_name: '重点难点' }
            ],
            options: [],
            correct_answer: '新时代爱国主义的基本要求包括：（1）坚持爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）坚持立足民族又面向世界。新时代的爱国主义必须坚持爱国和爱党、爱社会主义相统一，这是当代中国爱国主义精神最重要的体现。',
            explanation: '这道题考查新时代爱国主义的基本要求。答题要点：（1）爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）立足民族又面向世界。要结合新时代特点进行论述。',
            score: 10,
            status: 'active',
            created_at: '2024-01-18'
          }
        ]
        this.total = this.list.length
        this.listLoading = false
      }, 500)
    },

    loadCategoryOptions() {
      // 模拟分类选项（扁平化）
      this.categoryOptions = [
        { category_id: 111, category_name: '唯物论' },
        { category_id: 112, category_name: '辩证法' },
        { category_id: 113, category_name: '认识论' },
        { category_id: 12, category_name: '马克思主义政治经济学' },
        { category_id: 13, category_name: '科学社会主义' },
        { category_id: 21, category_name: '毛泽东思想' },
        { category_id: 22, category_name: '邓小平理论' },
        { category_id: 23, category_name: '三个代表重要思想' },
        { category_id: 31, category_name: '旧民主主义革命时期' },
        { category_id: 32, category_name: '新民主主义革命时期' },
        { category_id: 33, category_name: '社会主义革命和建设时期' }
      ]
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    handleCreate() {
      this.$router.push('/questions/add')
    },

    handleImport() {
      this.$router.push('/questions/upload')
    },

    handleUpdate(row) {
      this.$router.push(`/questions/edit/${row.question_id}`)
    },

    handleView(row) {
      this.currentQuestion = row
      this.viewDialogVisible = true
    },

    handleDelete(row) {
      this.$confirm('确定要删除该题目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$notify({
          title: '成功',
          message: '删除成功（模拟）',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-card {
    margin-bottom: 20px;
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .filter-item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  .el-table {
    margin-bottom: 20px;
    width: 100% !important;

    .el-table__body-wrapper {
      width: 100% !important;
    }

    .question-content {
      line-height: 1.5;
      word-break: break-word;
    }
  }
  .pagination-container {
    padding: 15px 0;
  }
}

.question-detail {
  .detail-item {
    margin-bottom: 20px;

    label {
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
      display: block;
    }

    .content,
    .answer,
    .explanation {
      background: #f5f7fa;
      padding: 12px;
      border-radius: 4px;
      line-height: 1.6;
    }

    .options {
      .option {
        padding: 8px 12px;
        margin-bottom: 8px;
        background: #f5f7fa;
        border-radius: 4px;
        border-left: 3px solid #409eff;
      }
    }
  }
}
</style>
