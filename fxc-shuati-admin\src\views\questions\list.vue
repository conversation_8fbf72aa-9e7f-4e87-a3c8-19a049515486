<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索题目内容"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.question_type"
          placeholder="题目类型"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="单选题" value="single_choice" />
          <el-option label="多选题" value="multiple_choice" />
          <el-option label="判断题" value="true_false" />
          <el-option label="填空题" value="fill_blank" />
          <el-option label="简答题" value="short_answer" />
          <el-option label="论述题" value="essay" />
        </el-select>
        <el-select
          v-model="listQuery.difficulty"
          placeholder="难度等级"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="简单" value="easy" />
          <el-option label="中等" value="medium" />
          <el-option label="困难" value="hard" />
        </el-select>
        <el-select
          v-model="listQuery.category_id"
          placeholder="题目分类"
          clearable
          style="width: 150px"
          class="filter-item"
        >
          <el-option
            v-for="category in categoryOptions"
            :key="category.category_id"
            :label="category.category_name"
            :value="category.category_id"
          />
        </el-select>
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加题目
        </el-button>
        <el-button
          class="filter-item"
          type="success"
          icon="el-icon-upload"
          @click="handleImport"
        >
          批量导入
        </el-button>
      </div>
    </el-card>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="question_id" align="center" width="80" />
      <el-table-column label="题目内容" prop="question_content" min-width="300">
        <template slot-scope="{row}">
          <div class="question-content">
            {{ row.question_content.length > 100 ? row.question_content.substring(0, 100) + '...' : row.question_content }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="题目类型" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="typeTagMap[row.question_type]">
            {{ typeTextMap[row.question_type] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="难度" width="80" align="center">
        <template slot-scope="{row}">
          <el-tag :type="difficultyTagMap[row.difficulty]" size="mini">
            {{ difficultyTextMap[row.difficulty] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分类" prop="category_name" min-width="120" />
      <el-table-column label="标签" min-width="150">
        <template slot-scope="{row}">
          <el-tag
            v-for="tag in row.tags"
            :key="tag.tag_id"
            size="mini"
            style="margin-right: 5px;"
          >
            {{ tag.tag_name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分值" prop="score" width="80" align="center" />
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="120" align="center">
        <template slot-scope="{row}">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button type="info" size="mini" @click="handleView(row)">
            查看
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 查看题目对话框 -->
    <el-dialog title="题目详情" :visible.sync="viewDialogVisible" width="60%">
      <div v-if="currentQuestion" class="question-detail">
        <div class="detail-item">
          <label>题目类型：</label>
          <el-tag :type="typeTagMap[currentQuestion.question_type]">
            {{ typeTextMap[currentQuestion.question_type] }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>难度等级：</label>
          <el-tag :type="difficultyTagMap[currentQuestion.difficulty]">
            {{ difficultyTextMap[currentQuestion.difficulty] }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>题目内容：</label>
          <div class="content">{{ currentQuestion.question_content }}</div>
        </div>
        <div v-if="currentQuestion.options && currentQuestion.options.length > 0" class="detail-item">
          <label>选项：</label>
          <div class="options">
            <div
              v-for="(option, index) in currentQuestion.options"
              :key="index"
              class="option"
            >
              {{ String.fromCharCode(65 + index) }}. {{ option.content }}
            </div>
          </div>
        </div>
        <div class="detail-item">
          <label>正确答案：</label>
          <div class="answer">{{ currentQuestion.correct_answer }}</div>
        </div>
        <div v-if="currentQuestion.explanation" class="detail-item">
          <label>题目解析：</label>
          <div class="explanation">{{ currentQuestion.explanation }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { formatDate } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'QuestionList',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 20,
        keyword: '',
        question_type: '',
        difficulty: '',
        category_id: '',
        status: ''
      },
      categoryOptions: [],
      viewDialogVisible: false,
      currentQuestion: null,
      typeTagMap: {
        single_choice: 'primary',
        multiple_choice: 'success',
        true_false: 'warning',
        fill_blank: 'info',
        short_answer: 'danger',
        essay: 'danger'
      },
      typeTextMap: {
        single_choice: '单选题',
        multiple_choice: '多选题',
        true_false: '判断题',
        fill_blank: '填空题',
        short_answer: '简答题',
        essay: '论述题'
      },
      difficultyTagMap: {
        easy: 'success',
        medium: 'warning',
        hard: 'danger'
      },
      difficultyTextMap: {
        easy: '简单',
        medium: '中等',
        hard: '困难'
      }
    }
  },
  created() {
    this.getList()
    this.loadCategoryOptions()
  },
  methods: {
    getList() {
      this.listLoading = true

      // 模拟题目数据
      setTimeout(() => {
        this.list = [
          {
            question_id: 1,
            question_content: '马克思主义哲学的基本问题是什么？',
            question_type: 'single_choice',
            difficulty: 'medium',
            category_id: 111,
            category_name: '唯物论',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 6, tag_name: '重点难点' }
            ],
            options: [
              { content: '物质和意识的关系问题' },
              { content: '理论和实践的关系问题' },
              { content: '个人和社会的关系问题' },
              { content: '自由和必然的关系问题' }
            ],
            correct_answer: 'A',
            explanation: '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。',
            score: 5,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 2,
            question_content: '马克思主义哲学的特征包括哪些？',
            question_type: 'multiple_choice',
            difficulty: 'medium',
            category_id: 111,
            category_name: '唯物论',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [
              { content: '实践性' },
              { content: '科学性' },
              { content: '革命性' },
              { content: '阶级性' }
            ],
            correct_answer: 'A,B,C,D',
            explanation: '马克思主义哲学具有实践性、科学性、革命性和阶级性等特征。',
            score: 5,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 3,
            question_content: '物质是标志客观实在的哲学范畴。',
            question_type: 'true_false',
            difficulty: 'easy',
            category_id: 111,
            category_name: '唯物论',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' }
            ],
            options: [],
            correct_answer: 'true',
            explanation: '这是马克思主义哲学对物质概念的经典定义。',
            score: 3,
            status: 'active',
            created_at: '2024-01-15'
          },
          {
            question_id: 4,
            question_content: '请简述马克思主义哲学的实践观点。',
            question_type: 'short_answer',
            difficulty: 'hard',
            category_id: 111,
            category_name: '唯物论',
            tags: [
              { tag_id: 1, tag_name: '马克思主义基本原理' },
              { tag_id: 6, tag_name: '重点难点' }
            ],
            options: [],
            correct_answer: '实践是人类能动地改造世界的客观物质性活动，是认识的基础，是检验真理的唯一标准，是推动认识发展的动力。',
            explanation: '马克思主义哲学强调实践的重要性，认为实践是认识的来源、动力、目的和检验标准。',
            score: 10,
            status: 'active',
            created_at: '2024-01-16'
          },
          {
            question_id: 5,
            question_content: '毛泽东思想形成的时代背景是_____。',
            question_type: 'fill_blank',
            difficulty: 'medium',
            category_id: 21,
            category_name: '毛泽东思想',
            tags: [
              { tag_id: 2, tag_name: '毛泽东思想' },
              { tag_id: 7, tag_name: '高频考点' }
            ],
            options: [],
            correct_answer: '帝国主义和无产阶级革命的时代',
            explanation: '毛泽东思想形成于帝国主义和无产阶级革命的时代，这是其重要的时代背景。',
            score: 4,
            status: 'active',
            created_at: '2024-01-16'
          }
        ]
        this.total = this.list.length
        this.listLoading = false
      }, 500)
    },

    loadCategoryOptions() {
      // 模拟分类选项（扁平化）
      this.categoryOptions = [
        { category_id: 111, category_name: '唯物论' },
        { category_id: 112, category_name: '辩证法' },
        { category_id: 113, category_name: '认识论' },
        { category_id: 12, category_name: '马克思主义政治经济学' },
        { category_id: 13, category_name: '科学社会主义' },
        { category_id: 21, category_name: '毛泽东思想' },
        { category_id: 22, category_name: '邓小平理论' },
        { category_id: 23, category_name: '三个代表重要思想' },
        { category_id: 31, category_name: '旧民主主义革命时期' },
        { category_id: 32, category_name: '新民主主义革命时期' },
        { category_id: 33, category_name: '社会主义革命和建设时期' }
      ]
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    handleCreate() {
      this.$router.push('/questions/add')
    },

    handleImport() {
      this.$router.push('/questions/upload')
    },

    handleUpdate(row) {
      this.$router.push(`/questions/edit/${row.question_id}`)
    },

    handleView(row) {
      this.currentQuestion = row
      this.viewDialogVisible = true
    },

    handleDelete(row) {
      this.$confirm('确定要删除该题目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$notify({
          title: '成功',
          message: '删除成功（模拟）',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-card {
    margin-bottom: 20px;
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .filter-item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  .el-table {
    margin-bottom: 20px;
    width: 100% !important;

    .el-table__body-wrapper {
      width: 100% !important;
    }

    .question-content {
      line-height: 1.5;
      word-break: break-word;
    }
  }
  .pagination-container {
    padding: 15px 0;
  }
}

.question-detail {
  .detail-item {
    margin-bottom: 20px;

    label {
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
      display: block;
    }

    .content,
    .answer,
    .explanation {
      background: #f5f7fa;
      padding: 12px;
      border-radius: 4px;
      line-height: 1.6;
    }

    .options {
      .option {
        padding: 8px 12px;
        margin-bottom: 8px;
        background: #f5f7fa;
        border-radius: 4px;
        border-left: 3px solid #409eff;
      }
    }
  }
}
</style>
