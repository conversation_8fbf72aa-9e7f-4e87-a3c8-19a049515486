{"_from": "dotenv", "_id": "dotenv@17.2.0", "_inBundle": false, "_integrity": "sha512-Q4sgBT60gzd0BB0lSyYD3xM4YxrXA9y4uBDof1JNYGzOXrQdQ6yX+7XIAqoFOGQFOTK1D3Hts5OllpxMDZFONQ==", "_location": "/dotenv", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "dotenv", "name": "dotenv", "escapedName": "dotenv", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/dotenv/-/dotenv-17.2.0.tgz", "_shasum": "e19678fdabcf86d4bfdb6764a758d7d44efbb6a2", "_spec": "dotenv", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-api", "browser": {"fs": false}, "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Loads environment variables from .env file", "devDependencies": {"@types/node": "^18.11.3", "decache": "^4.6.2", "sinon": "^14.0.1", "standard": "^17.0.0", "standard-version": "^9.5.0", "tap": "^19.2.0", "typescript": "^4.8.4"}, "engines": {"node": ">=12"}, "exports": {".": {"types": "./lib/main.d.ts", "require": "./lib/main.js", "default": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./lib/env-options": "./lib/env-options.js", "./lib/env-options.js": "./lib/env-options.js", "./lib/cli-options": "./lib/cli-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./package.json": "./package.json"}, "funding": "https://dotenvx.com", "homepage": "https://github.com/motdotla/dotenv#readme", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/main.js", "name": "dotenv", "repository": {"type": "git", "url": "git://github.com/motdotla/dotenv.git"}, "scripts": {"dts-check": "tsc --project tests/types/tsconfig.json", "lint": "standard", "prerelease": "npm test", "pretest": "npm run lint && npm run dts-check", "release": "standard-version", "test": "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov"}, "types": "lib/main.d.ts", "version": "17.2.0"}