{"_from": "ignore@^4.0.3", "_id": "ignore@4.0.6", "_inBundle": false, "_integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==", "_location": "/ignore", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ignore@^4.0.3", "name": "ignore", "escapedName": "ignore", "rawSpec": "^4.0.3", "saveSpec": null, "fetchSpec": "^4.0.3"}, "_requiredBy": ["/eslint", "/globby"], "_resolved": "https://registry.npmmirror.com/ignore/-/ignore-4.0.6.tgz", "_shasum": "750e3db5862087b4737ebac8207ffd1ef27b25fc", "_spec": "ignore@^4.0.3", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\globby", "author": {"name": "kael"}, "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "bundleDependencies": false, "deprecated": false, "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "codecov": "^3.0.4", "eslint": "^5.3.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0", "mkdirp": "^0.5.1", "pre-suf": "^1.1.0", "rimraf": "^2.6.2", "spawn-sync": "^2.0.0", "tap": "^12.0.1", "tmp": "0.0.33", "typescript": "^3.0.1"}, "engines": {"node": ">= 4"}, "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "homepage": "https://github.com/kaelzhang/node-ignore#readme", "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "license": "MIT", "name": "ignore", "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "scripts": {"build": "babel -o legacy.js index.js", "posttest": "tap --coverage-report=html && codecov", "prepublish": "npm run build", "test": "npm run test-no-cov", "test-no-cov": "npm run test:lint && npm run test:tsc && tap test/*.js --coverage", "test:git": "tap test/git-check-ignore.js", "test:ignore": "tap test/ignore.js --coverage", "test:lint": "eslint .", "test:tsc": "tsc ./test/ts/simple.ts"}, "version": "4.0.6"}