{"_from": "import-local@^2.0.0", "_id": "import-local@2.0.0", "_inBundle": false, "_integrity": "sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==", "_location": "/import-local", "_phantomChildren": {"p-limit": "2.3.0"}, "_requested": {"type": "range", "registry": true, "raw": "import-local@^2.0.0", "name": "import-local", "escapedName": "import-local", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/jest", "/jest/jest-cli", "/webpack-dev-server"], "_resolved": "https://registry.npmmirror.com/import-local/-/import-local-2.0.0.tgz", "_shasum": "55070be38a5993cf18ef6db7e961f5bee5c5a09d", "_spec": "import-local@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\jest", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "bundleDependencies": false, "dependencies": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "deprecated": false, "description": "Let a globally installed package use a locally installed version of itself if available", "devDependencies": {"ava": "*", "cpy": "^7.0.1", "del": "^3.0.0", "execa": "^0.11.0", "xo": "*"}, "engines": {"node": ">=6"}, "files": ["index.js", "fixtures/cli.js"], "homepage": "https://github.com/sindresorhus/import-local#readme", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "license": "MIT", "name": "import-local", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/import-local.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0", "xo": {"ignores": ["fixtures"]}}