# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.0.3](https://github.com/inspect-js/is-map/compare/v2.0.2...v2.0.3) - 2024-03-08

### Commits

- [actions] reuse common workflows [`ce10d0f`](https://github.com/inspect-js/is-map/commit/ce10d0f82fcec150b5d283202c1988887d618895)
- [meta] use `npmignore` to autogenerate an npmignore file [`e07e23a`](https://github.com/inspect-js/is-map/commit/e07e23affca99f469937dade44abc02e05a26739)
- add types [`cd13cfb`](https://github.com/inspect-js/is-map/commit/cd13cfb54647def94a0df9a276a92298891f7bdd)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`1e055f9`](https://github.com/inspect-js/is-map/commit/1e055f9ea79c6c7cb6f8182e644c08ae167d358b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`12d125e`](https://github.com/inspect-js/is-map/commit/12d125ef5bd4d6cf0468f406bf3dd3b873aa3af9)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es5-shim`, `object-inspect`, `tape` [`adfb18e`](https://github.com/inspect-js/is-map/commit/adfb18ee26fa3ecadfdb16657a5423dda4248ca3)
- [actions] remove redundant finisher [`c5511b7`](https://github.com/inspect-js/is-map/commit/c5511b79c739a08f7da40b9cae2391d10b4b613c)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `es6-shim`, `npmignore`, `object-inspect`, `tape` [`b2c7d67`](https://github.com/inspect-js/is-map/commit/b2c7d674d2e78f5fb67a7e69b83ae177255fb8da)
- [actions] update rebase action to use reusable workflow [`bbad644`](https://github.com/inspect-js/is-map/commit/bbad64428c5b777070ed86130669211ec1645714)
- [actions] update codecov uploader [`8f57f98`](https://github.com/inspect-js/is-map/commit/8f57f98d3e3897fa82e87a155f05b7fdb174c222)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `es5-shim`, `object-inspect`, `tape` [`d330ff4`](https://github.com/inspect-js/is-map/commit/d330ff4cbdbbce8402da928cab040e2c85126506)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`454e31c`](https://github.com/inspect-js/is-map/commit/454e31ccecaa2ac78c7397afe2b0101576ad5b11)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `es5-shim`, `tape` [`b43283d`](https://github.com/inspect-js/is-map/commit/b43283dcd906d2024d2b78448bf8b679922d791b)
- [readme] add actions and codecov badges [`0fc119e`](https://github.com/inspect-js/is-map/commit/0fc119ed01da39b3444478f7912447f6f298339f)
- [Dev Deps] update `eslint`, `object-inspect` [`e2311f8`](https://github.com/inspect-js/is-map/commit/e2311f8984f2e2efda5011b4636275bfa7b17e8d)
- [meta] add missing `engines.node` [`9bddaf2`](https://github.com/inspect-js/is-map/commit/9bddaf20a47fc5f359d171c8a7d43ac667d4680d)
- [meta] use `prepublishOnly` script for npm 7+ [`d3b7661`](https://github.com/inspect-js/is-map/commit/d3b76613fcd34381a1ccdf17f4ab6e3e892dfc5f)
- [Dev Deps] update `safe-publish-latest` [`00d7b69`](https://github.com/inspect-js/is-map/commit/00d7b69c315b9404b49c8d0ca85774f739f25a61)
- [meta] add `sideEffects` flag [`bab4457`](https://github.com/inspect-js/is-map/commit/bab445707d11d590f2650f43b58bf9fa8dd664d1)

## [v2.0.2](https://github.com/inspect-js/is-map/compare/v2.0.1...v2.0.2) - 2020-12-13

### Commits

- [Tests] migrate tests to Github Actions [`349a036`](https://github.com/inspect-js/is-map/commit/349a0362a744d024937a4356134389cbebf0c1a7)
- [meta] do not publish github action workflow files [`f473ae7`](https://github.com/inspect-js/is-map/commit/f473ae777d15c5d247002f5aaa52ed4ada3a5dd4)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es6-shim`, `object-inspect`, `tape` [`12dbda3`](https://github.com/inspect-js/is-map/commit/12dbda37a97c0dab0a3874a6cff086cd44f1c94c)
- [Tests] run `nyc` on all tests; use `tape` runner; add `core-js` tests [`b280737`](https://github.com/inspect-js/is-map/commit/b280737c513588fef4b88c16328627744c8ab946)
- [actions] add "Allow Edits" workflow [`d8dcf17`](https://github.com/inspect-js/is-map/commit/d8dcf17dd6b1cc09b8de369aa87188f469297b7c)
- [readme] remove travis badge [`eab86f9`](https://github.com/inspect-js/is-map/commit/eab86f94cca4941861784e5eb8b7ca05e847e0b5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`9c87af5`](https://github.com/inspect-js/is-map/commit/9c87af5008a4ff79bffc3a6de55bf2d65979db6d)
- [actions] switch Automatic Rease workflow to `pull_request_tarbget` event [`71647b8`](https://github.com/inspect-js/is-map/commit/71647b805066ecbc096d5742fd69046d22f2b5c4)
- [Dev Deps] update `es5-shim`, `tape` [`3a91230`](https://github.com/inspect-js/is-map/commit/3a912305d7d836e8d6e4f80e9047e3beff8ea887)
- [Dev Deps] update `auto-changelog`; add `aud` [`d3cd3da`](https://github.com/inspect-js/is-map/commit/d3cd3da9008756a02c2b26b45292c477bf9594a9)
- [Tests] only audit prod deps [`83ef327`](https://github.com/inspect-js/is-map/commit/83ef327c62d54a48193bf95ed8cb6c4dff0a2035)
- [meta] normalize line endings [`81a9eec`](https://github.com/inspect-js/is-map/commit/81a9eec713f8e309fa1f0ffb7e4b154c359b367b)

## [v2.0.1](https://github.com/inspect-js/is-map/compare/v2.0.0...v2.0.1) - 2019-12-17

### Fixed

- [Refactor] avoid top-level return, because babel and webpack are broken [`#5`](https://github.com/inspect-js/is-map/issues/5) [`#4`](https://github.com/inspect-js/is-map/issues/4) [`#3`](https://github.com/inspect-js/is-map/issues/3) [`#78`](https://github.com/inspect-js/node-deep-equal/issues/78) [`#7`](https://github.com/es-shims/Promise.allSettled/issues/7) [`#12`](https://github.com/airbnb/js-shims/issues/12)

### Commits

- [actions] add automatic rebasing / merge commit blocking [`743f29f`](https://github.com/inspect-js/is-map/commit/743f29fc527b4a8a56a7045ad3d56ecfc798b1a3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`8ced854`](https://github.com/inspect-js/is-map/commit/8ced854c842c86cb126b86618cb4f90ef6a04f2b)

## [v2.0.0](https://github.com/inspect-js/is-map/compare/v1.0.1...v2.0.0) - 2019-11-12

### Commits

- Initial commit [`38592bc`](https://github.com/inspect-js/is-map/commit/38592bcb928d97b244cca6cee91142a44bcf5ab1)
- Tests [`ca54632`](https://github.com/inspect-js/is-map/commit/ca546326943385052e8b5a04377f1f8b110b7306)
- readme [`9ad8bb6`](https://github.com/inspect-js/is-map/commit/9ad8bb6bc2fb295ada21e1cd901c89aa55acad37)
- implementation [`03e1dbc`](https://github.com/inspect-js/is-map/commit/03e1dbc64eb09e6caba919c9ae5662992f0a9b52)
- npm init [`d05ce8b`](https://github.com/inspect-js/is-map/commit/d05ce8b0ad797c97ed23a7730a9e211e5fe0fe92)
- [meta] add `funding` field; create `FUNDING.yml` [`2d56b4e`](https://github.com/inspect-js/is-map/commit/2d56b4e2a44e6eb4557d9d192a863c92b68c6597)
- [meta] add `safe-publish-latest`, `auto-changelog` [`2ebecb5`](https://github.com/inspect-js/is-map/commit/2ebecb5a3fe5fa682d5d04d1cd87f4d88ba22ec9)
- [Tests] add `npm run lint` [`ddc3e32`](https://github.com/inspect-js/is-map/commit/ddc3e320c3d181b9111dd3a86df486604710e08c)
- [Tests] use shared travis-ci configs [`69f6d9c`](https://github.com/inspect-js/is-map/commit/69f6d9c52a06dda27419eb41572b8db6009f6d49)
- Only apps should have lockfiles [`408cccd`](https://github.com/inspect-js/is-map/commit/408cccdc824c017547573d816b2201e9cfb9a292)
- [Tests] add `npx aud` in `posttest` [`5eadb02`](https://github.com/inspect-js/is-map/commit/5eadb02075754732df3532bc2e98ca6307c46537)

## [v1.0.1](https://github.com/inspect-js/is-map/compare/v1.0.0...v1.0.1) - 2015-07-02

### Commits

- small tweaks [`2bd7622`](https://github.com/inspect-js/is-map/commit/2bd762263930d4f72eedd3a54678e1692062d53f)
- Add `related` section to readme [`3231e74`](https://github.com/inspect-js/is-map/commit/3231e748fbf1d4d7d1662b8a559e73cc1e69468b)
- Update license info in `readme.md` [`3a03b38`](https://github.com/inspect-js/is-map/commit/3a03b387b798d5eda09965dcf63e0c9fb9c7ddac)
- editorconfig: indent yml using 2 spaces [`d724177`](https://github.com/inspect-js/is-map/commit/d724177b7eb103174cd9ca1dce4a914e3dfdb1cd)

## v1.0.0 - 2015-02-18

### Commits

- init [`73b9f38`](https://github.com/inspect-js/is-map/commit/73b9f38e3d3c0435e639a7e054714d71b6ddae9b)
