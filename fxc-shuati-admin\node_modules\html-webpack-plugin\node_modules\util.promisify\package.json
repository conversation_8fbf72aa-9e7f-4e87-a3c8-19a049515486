{"_from": "util.promisify@1.0.0", "_id": "util.promisify@1.0.0", "_inBundle": false, "_integrity": "sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA==", "_location": "/html-webpack-plugin/util.promisify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "util.promisify@1.0.0", "name": "util.promisify", "escapedName": "util.promisify", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/html-webpack-plugin"], "_resolved": "https://registry.npmmirror.com/util.promisify/-/util.promisify-1.0.0.tgz", "_shasum": "440f7165a459c9a16dc145eb8e72f35687097030", "_spec": "util.promisify@1.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\html-webpack-plugin", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/util.promisify/issues"}, "bundleDependencies": false, "dependencies": {"define-properties": "^1.1.2", "object.getownpropertydescriptors": "^2.0.3"}, "deprecated": false, "description": "Polyfill/shim for util.promisify in node versions < v8", "devDependencies": {"@es-shims/api": "^1.2.0", "@ljharb/eslint-config": "^11.0.0", "eslint": "^3.19.0", "safe-publish-latest": "^1.1.1"}, "homepage": "https://github.com/ljharb/util.promisify#readme", "keywords": ["promisify", "promise", "util", "polyfill", "shim", "util.promisify"], "license": "MIT", "main": "index.js", "name": "util.promisify", "repository": {"type": "git", "url": "git+https://github.com/ljharb/util.promisify.git"}, "scripts": {"lint": "eslint .", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "es-shim-api --bound"}, "version": "1.0.0"}