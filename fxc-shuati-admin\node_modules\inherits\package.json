{"_from": "inherits@~2.0.3", "_id": "inherits@2.0.4", "_inBundle": false, "_integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "_location": "/inherits", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "inherits@~2.0.3", "name": "inherits", "escapedName": "inherits", "rawSpec": "~2.0.3", "saveSpec": null, "fetchSpec": "~2.0.3"}, "_requiredBy": ["/asn1.js", "/browserify-aes", "/browserify-des", "/browserify-sign", "/cipher-base", "/concat-stream", "/create-hash", "/create-hmac", "/crypto-browserify", "/css", "/des.js", "/duplexify", "/elliptic", "/flush-write-stream", "/from2", "/glob", "/hash-base", "/hash.js", "/hpack.js", "/http-errors", "/md5.js", "/parallel-transform", "/pbkdf2/create-hash", "/pbkdf2/hash-base", "/pbkdf2/ripemd160", "/posthtml-parser/htmlparser2", "/posthtml-parser/readable-stream", "/pumpify", "/readable-stream", "/ripemd160", "/sha.js", "/sockjs-client", "/spdy-transport/readable-stream", "/stream-browserify", "/stream-http", "/watchpack-chokidar2/chokidar", "/webpack-dev-server/chokidar"], "_resolved": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "_shasum": "0fa2c64f932917c3433a0ded55363aae37416b7c", "_spec": "inherits@~2.0.3", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\readable-stream", "browser": "./inherits_browser.js", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "devDependencies": {"tap": "^14.2.4"}, "files": ["inherits.js", "inherits_browser.js"], "homepage": "https://github.com/isaacs/inherits#readme", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "license": "ISC", "main": "./inherits.js", "name": "inherits", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "scripts": {"test": "tap"}, "version": "2.0.4"}