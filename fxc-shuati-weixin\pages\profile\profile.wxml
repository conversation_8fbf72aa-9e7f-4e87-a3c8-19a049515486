<!--profile.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card card">
    <view class="user-info">
      <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      </button>
      <view class="user-details">
        <input 
          type="nickname" 
          class="user-nickname" 
          value="{{userInfo.nickName}}" 
          placeholder="请输入昵称"
          bind:change="onNicknameChange"
        />
        <text class="user-signature">{{userInfo.signature || '这个人很懒，什么都没留下~'}}</text>
      </view>
      <button class="edit-btn" bindtap="editProfile">
        <text class="edit-icon">✏️</text>
      </button>
    </view>
    
    <!-- 学习统计 -->
    <view class="study-stats">
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalQuestions}}</text>
        <text class="stats-label">已练习</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.correctRate}}%</text>
        <text class="stats-label">正确率</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.studyDays}}</text>
        <text class="stats-label">学习天数</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.rank}}</text>
        <text class="stats-label">排名</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section card">
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-icon favorite-icon">❤️</view>
      <text class="menu-text">我的收藏</text>
      <view class="menu-badge" wx:if="{{userStats.favoriteCount > 0}}">{{userStats.favoriteCount}}</view>
      <view class="menu-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
    
    <view class="menu-item" bindtap="goToErrors">
      <view class="menu-icon error-icon">❌</view>
      <text class="menu-text">错题本</text>
      <view class="menu-badge" wx:if="{{userStats.errorCount > 0}}">{{userStats.errorCount}}</view>
      <view class="menu-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
    
    <view class="menu-item" bindtap="goToHistory">
      <view class="menu-icon history-icon">🕐</view>
      <text class="menu-text">练习历史</text>
      <view class="menu-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
    
    <view class="menu-item" bindtap="goToAchievements">
      <view class="menu-icon achievement-icon">🏆</view>
      <text class="menu-text">成就徽章</text>
      <view class="menu-badge" wx:if="{{userStats.achievementCount > 0}}">{{userStats.achievementCount}}</view>
      <view class="menu-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
  </view>

  <!-- 设置菜单 -->
  <view class="menu-section card">
    <view class="menu-item" bindtap="goToSettings">
      <view class="menu-icon settings-icon">⚙️</view>
      <text class="menu-text">设置</text>
      <view class="menu-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
    
    <view class="menu-item" bindtap="goToFeedback">
      <view class="menu-icon feedback-icon">💬</view>
      <text class="menu-text">意见反馈</text>
      <view class="menu-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
    
    <view class="menu-item" bindtap="goToAbout">
      <view class="menu-icon about-icon">ℹ️</view>
      <text class="menu-text">关于我们</text>
      <view class="menu-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
  </view>

  <!-- 学习进度图表 -->
  <view class="chart-section card">
    <view class="section-title">最近7天学习情况</view>
    <view class="chart-container">
      <view class="chart-bars">
        <view 
          wx:for="{{chartData}}" 
          wx:key="date"
          class="chart-bar"
          style="height: {{item.height}}%"
        >
          <view class="bar-value">{{item.count}}</view>
        </view>
      </view>
      <view class="chart-labels">
        <text wx:for="{{chartData}}" wx:key="date" class="chart-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 编辑资料弹窗 -->
  <view class="edit-popup-mask" wx:if="{{showEditPopup}}" bindtap="cancelEdit">
    <view class="edit-popup" catchtap="">
      <view class="popup-title">编辑资料</view>
      <view class="form-item">
        <text class="form-label">个性签名</text>
        <textarea 
          value="{{editForm.signature}}" 
          class="form-textarea" 
          placeholder="请输入个性签名"
          bindinput="onSignatureInput"
          maxlength="50"
        ></textarea>
      </view>
      <view class="popup-actions">
        <button class="cancel-btn" bindtap="cancelEdit">取消</button>
        <button class="confirm-btn" bindtap="saveProfile">保存</button>
      </view>
    </view>
  </view>
</view>
