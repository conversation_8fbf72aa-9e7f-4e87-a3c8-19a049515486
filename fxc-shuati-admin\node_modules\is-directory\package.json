{"_from": "is-directory@^0.3.1", "_id": "is-directory@0.3.1", "_inBundle": false, "_integrity": "sha512-yVChGzahRFvbkscn2MlwGismPO12i9+znNruC5gVEntG3qu0xQMzsGg/JFbrsqDOHtHFPci+V5aP5T9I+yeKqw==", "_location": "/is-directory", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-directory@^0.3.1", "name": "is-directory", "escapedName": "is-directory", "rawSpec": "^0.3.1", "saveSpec": null, "fetchSpec": "^0.3.1"}, "_requiredBy": ["/cosmiconfig"], "_resolved": "https://registry.npmmirror.com/is-directory/-/is-directory-0.3.1.tgz", "_shasum": "61339b6f2475fc772fd9c9d83f5c8575dc154ae1", "_spec": "is-directory@^0.3.1", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cosmiconfig", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-directory/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Returns true if a filepath exists on the file system and it's directory.", "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-directory", "keywords": ["dir", "directories", "directory", "dirs", "file", "filepath", "files", "fp", "fs", "node", "node.js", "path", "paths", "system"], "license": "MIT", "main": "index.js", "name": "is-directory", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-directory.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-glob", "is-relative", "is-absolute"]}, "lint": {"reflinks": true}, "reflinks": ["verb"]}, "version": "0.3.1"}