{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue?vue&type=style&index=0&id=7a064a96&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue", "mtime": 1752631093713}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFwcC1jb250YWluZXIgewogIC5maWx0ZXItY2FyZCB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgLmZpbHRlci1jb250YWluZXIgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LXdyYXA6IHdyYXA7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIC5maWx0ZXItaXRlbSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICAgIH0KICAgIH0KICB9CiAgLmVsLXRhYmxlIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50OwogICAgCiAgICAuZWwtdGFibGVfX2JvZHktd3JhcHBlciB7CiAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7CiAgICB9CiAgfQogIC5wYWdpbmF0aW9uLWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAxNXB4IDA7CiAgfQp9Cg=="}, {"version": 3, "sources": ["tags.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "tags.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索标签名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加标签\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"tag_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"标签名称\" prop=\"tag_name\" min-width=\"150\" />\n      <el-table-column label=\"标签颜色\" width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :color=\"row.tag_color\" style=\"color: white;\">\n            {{ row.tag_name }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"标签描述\" prop=\"tag_desc\" min-width=\"200\" />\n      <el-table-column label=\"题目数量\" prop=\"question_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"标签名称\" prop=\"tag_name\">\n          <el-input v-model=\"temp.tag_name\" />\n        </el-form-item>\n        <el-form-item label=\"标签颜色\" prop=\"tag_color\">\n          <el-color-picker v-model=\"temp.tag_color\" />\n        </el-form-item>\n        <el-form-item label=\"标签描述\" prop=\"tag_desc\">\n          <el-input v-model=\"temp.tag_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionTags',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      temp: {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑标签',\n        create: '添加标签'\n      },\n      rules: {\n        tag_name: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟考研政治标签数据\n      setTimeout(() => {\n        this.list = [\n          // 学科标签\n          {\n            tag_id: 1,\n            tag_name: '马克思主义基本原理',\n            tag_color: '#E74C3C',\n            tag_desc: '马克思主义基本原理概论相关题目',\n            question_count: 245,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 2,\n            tag_name: '毛泽东思想',\n            tag_color: '#F39C12',\n            tag_desc: '毛泽东思想和中国特色社会主义理论体系概论',\n            question_count: 312,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 3,\n            tag_name: '中国近现代史纲要',\n            tag_color: '#27AE60',\n            tag_desc: '中国近现代史纲要相关题目',\n            question_count: 198,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 4,\n            tag_name: '思想道德与法治',\n            tag_color: '#3498DB',\n            tag_desc: '思想道德与法治相关题目',\n            question_count: 167,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 5,\n            tag_name: '形势与政策',\n            tag_color: '#9B59B6',\n            tag_desc: '形势与政策以及当代世界经济与政治',\n            question_count: 123,\n            status: 'active',\n            sort_order: 5,\n            created_at: '2024-01-01'\n          },\n          // 重要性标签\n          {\n            tag_id: 6,\n            tag_name: '重点难点',\n            tag_color: '#E67E22',\n            tag_desc: '标记为重点难点的题目',\n            question_count: 156,\n            status: 'active',\n            sort_order: 6,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 7,\n            tag_name: '高频考点',\n            tag_color: '#C0392B',\n            tag_desc: '历年考试中的高频考点',\n            question_count: 234,\n            status: 'active',\n            sort_order: 7,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 8,\n            tag_name: '易错题',\n            tag_color: '#8E44AD',\n            tag_desc: '学生容易出错的题目',\n            question_count: 189,\n            status: 'active',\n            sort_order: 8,\n            created_at: '2024-01-02'\n          },\n          // 专题标签\n          {\n            tag_id: 9,\n            tag_name: '哲学原理',\n            tag_color: '#2C3E50',\n            tag_desc: '马克思主义哲学基本原理',\n            question_count: 145,\n            status: 'active',\n            sort_order: 9,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 10,\n            tag_name: '政治经济学',\n            tag_color: '#16A085',\n            tag_desc: '马克思主义政治经济学原理',\n            question_count: 78,\n            status: 'active',\n            sort_order: 10,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 11,\n            tag_name: '科学社会主义',\n            tag_color: '#D35400',\n            tag_desc: '科学社会主义基本原理',\n            question_count: 67,\n            status: 'active',\n            sort_order: 11,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 12,\n            tag_name: '新民主主义革命',\n            tag_color: '#8B4513',\n            tag_desc: '新民主主义革命理论',\n            question_count: 89,\n            status: 'active',\n            sort_order: 12,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 13,\n            tag_name: '社会主义建设',\n            tag_color: '#FF6347',\n            tag_desc: '社会主义革命和建设',\n            question_count: 112,\n            status: 'active',\n            sort_order: 13,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 14,\n            tag_name: '改革开放',\n            tag_color: '#4169E1',\n            tag_desc: '改革开放和现代化建设',\n            question_count: 134,\n            status: 'active',\n            sort_order: 14,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 15,\n            tag_name: '新时代',\n            tag_color: '#FF1493',\n            tag_desc: '新时代中国特色社会主义',\n            question_count: 98,\n            status: 'active',\n            sort_order: 15,\n            created_at: '2024-01-03'\n          },\n          // 年份标签\n          {\n            tag_id: 16,\n            tag_name: '2023年真题',\n            tag_color: '#32CD32',\n            tag_desc: '2023年考研政治真题',\n            question_count: 45,\n            status: 'active',\n            sort_order: 16,\n            created_at: '2024-01-04'\n          },\n          {\n            tag_id: 17,\n            tag_name: '2022年真题',\n            tag_color: '#20B2AA',\n            tag_desc: '2022年考研政治真题',\n            question_count: 45,\n            status: 'active',\n            sort_order: 17,\n            created_at: '2024-01-04'\n          },\n          {\n            tag_id: 18,\n            tag_name: '模拟题',\n            tag_color: '#778899',\n            tag_desc: '模拟考试题目',\n            question_count: 267,\n            status: 'active',\n            sort_order: 18,\n            created_at: '2024-01-04'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该标签吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n    \n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}