{"_from": "is-boolean-object@^1.2.1", "_id": "is-boolean-object@1.2.2", "_inBundle": false, "_integrity": "sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==", "_location": "/is-boolean-object", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-boolean-object@^1.2.1", "name": "is-boolean-object", "escapedName": "is-boolean-object", "rawSpec": "^1.2.1", "saveSpec": null, "fetchSpec": "^1.2.1"}, "_requiredBy": ["/which-boxed-primitive"], "_resolved": "https://registry.npmmirror.com/is-boolean-object/-/is-boolean-object-1.2.2.tgz", "_shasum": "7067f47709809a393c71ff5bb3e135d8a9215d9e", "_spec": "is-boolean-object@^1.2.1", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\which-boxed-primitive", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-boolean-object/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "deprecated": false, "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/core-js": "^2.5.8", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "core-js": "^3.40.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-boolean-object#readme", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "license": "MIT", "main": "index.js", "name": "is-boolean-object", "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-boolean-object.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:harmony && npm run test:corejs", "test:corejs": "nyc tape test-corejs.js", "test:harmony": "node --harmony --es-staging test", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.2.2"}