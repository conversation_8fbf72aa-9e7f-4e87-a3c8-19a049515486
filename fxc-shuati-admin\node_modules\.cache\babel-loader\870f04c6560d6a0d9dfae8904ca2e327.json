{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue", "mtime": 1752627977905}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getPermissionList", "createPermission", "updatePermission", "deletePermission", "waves", "parseTime", "Pagination", "name", "components", "directives", "filters", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "permission_type", "status", "typeTextMap", "menu", "button", "api", "typeTagMap", "parentOptions", "temp", "permission_id", "undefined", "parent_id", "permission_name", "permission_code", "permission_url", "permission_icon", "sort_order", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "then", "response", "filter", "item", "handleFilter", "resetTemp", "handleCreate", "_this2", "$nextTick", "$refs", "clearValidate", "createData", "_this3", "validate", "valid", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this4", "Object", "assign", "updateData", "_this5", "tempData", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText"], "sources": ["src/views/users/permissions.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索权限名称、编码\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.permission_type\"\n          placeholder=\"权限类型\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"菜单\" value=\"menu\" />\n          <el-option label=\"按钮\" value=\"button\" />\n          <el-option label=\"接口\" value=\"api\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加权限\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"permission_id\"\n      default-expand-all\n    >\n      <el-table-column label=\"ID\" prop=\"permission_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"权限名称\" prop=\"permission_name\" min-width=\"150\" />\n      <el-table-column label=\"权限编码\" prop=\"permission_code\" min-width=\"180\" />\n      <el-table-column label=\"权限类型\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"typeTagMap[row.permission_type]\">\n            {{ typeTextMap[row.permission_type] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"权限URL\" prop=\"permission_url\" min-width=\"180\" />\n      <el-table-column label=\"权限图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.permission_icon\" :class=\"row.permission_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"父权限\" prop=\"parent_name\" min-width=\"150\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父权限\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父权限\" style=\"width: 100%\">\n            <el-option label=\"顶级权限\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.permission_id\"\n              :label=\"item.permission_name\"\n              :value=\"item.permission_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限名称\" prop=\"permission_name\">\n          <el-input v-model=\"temp.permission_name\" />\n        </el-form-item>\n        <el-form-item label=\"权限编码\" prop=\"permission_code\">\n          <el-input v-model=\"temp.permission_code\" />\n        </el-form-item>\n        <el-form-item label=\"权限类型\" prop=\"permission_type\">\n          <el-select v-model=\"temp.permission_type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\n            <el-option label=\"菜单\" value=\"menu\" />\n            <el-option label=\"按钮\" value=\"button\" />\n            <el-option label=\"接口\" value=\"api\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限URL\" prop=\"permission_url\">\n          <el-input v-model=\"temp.permission_url\" />\n        </el-form-item>\n        <el-form-item label=\"权限图标\" prop=\"permission_icon\">\n          <el-input v-model=\"temp.permission_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPermissionList, createPermission, updatePermission, deletePermission } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'PermissionList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        permission_type: '',\n        status: ''\n      },\n      typeTextMap: {\n        menu: '菜单',\n        button: '按钮',\n        api: '接口'\n      },\n      typeTagMap: {\n        menu: 'primary',\n        button: 'success',\n        api: 'warning'\n      },\n      parentOptions: [],\n      temp: {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑权限',\n        create: '添加权限'\n      },\n      rules: {\n        permission_name: [{ required: true, message: '权限名称不能为空', trigger: 'blur' }],\n        permission_code: [{ required: true, message: '权限编码不能为空', trigger: 'blur' }],\n        permission_type: [{ required: true, message: '权限类型不能为空', trigger: 'change' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getPermissionList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n        \n        // 更新父权限选项\n        this.parentOptions = this.list.filter(item => item.permission_type === 'menu')\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createPermission(this.temp).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          updatePermission(tempData.permission_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该权限吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deletePermission(row.permission_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoLA,SAAAA,iBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;AACA,OAAAC,KAAA;AACA,SAAAC,SAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,OAAA;IACAL,SAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,eAAA;QACAC,MAAA;MACA;MACAC,WAAA;QACAC,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACAC,UAAA;QACAH,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACAE,aAAA;MACAC,IAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,eAAA;QACAC,eAAA;QACAb,eAAA;QACAc,cAAA;QACAC,eAAA;QACAd,MAAA;QACAe,UAAA;MACA;MACAC,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAV,eAAA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAZ,eAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAzB,eAAA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAlC,WAAA;MACAf,iBAAA,MAAAgB,SAAA,EAAAkC,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAApC,IAAA,GAAAsC,QAAA,CAAAxC,IAAA,CAAAE,IAAA;QACAoC,KAAA,CAAAnC,KAAA,GAAAqC,QAAA,CAAAxC,IAAA,CAAAG,KAAA;QACAmC,KAAA,CAAAlC,WAAA;;QAEA;QACAkC,KAAA,CAAAtB,aAAA,GAAAsB,KAAA,CAAApC,IAAA,CAAAuC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAjC,eAAA;QAAA;MACA;IACA;IACAkC,YAAA,WAAAA,aAAA;MACA,KAAAtC,SAAA,CAAAC,IAAA;MACA,KAAA8B,OAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACA,KAAA3B,IAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,eAAA;QACAC,eAAA;QACAb,eAAA;QACAc,cAAA;QACAC,eAAA;QACAd,MAAA;QACAe,UAAA;MACA;IACA;IACAoB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAAjB,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA/D,gBAAA,CAAA6D,MAAA,CAAAlC,IAAA,EAAAsB,IAAA;YACAY,MAAA,CAAAzB,iBAAA;YACAyB,MAAA,CAAAG,OAAA;cACAC,KAAA;cACAtB,OAAA;cACAuB,IAAA;cACAC,QAAA;YACA;YACAN,MAAA,CAAAf,OAAA;UACA;QACA;MACA;IACA;IACAsB,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA3C,IAAA,GAAA4C,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAAhC,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAa,MAAA,CAAAZ,KAAA,aAAAC,aAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAY,QAAA,GAAAJ,MAAA,CAAAC,MAAA,KAAAE,MAAA,CAAA/C,IAAA;UACA1B,gBAAA,CAAA0E,QAAA,CAAA/C,aAAA,EAAA+C,QAAA,EAAA1B,IAAA;YACAyB,MAAA,CAAAtC,iBAAA;YACAsC,MAAA,CAAAV,OAAA;cACAC,KAAA;cACAtB,OAAA;cACAuB,IAAA;cACAC,QAAA;YACA;YACAO,MAAA,CAAA5B,OAAA;UACA;QACA;MACA;IACA;IACA8B,YAAA,WAAAA,aAAAP,GAAA;MAAA,IAAAQ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAd,IAAA;MACA,GAAAjB,IAAA;QACA/C,gBAAA,CAAAmE,GAAA,CAAAzC,aAAA,EAAAqB,IAAA;UACA4B,MAAA,CAAAb,OAAA;YACAC,KAAA;YACAtB,OAAA;YACAuB,IAAA;YACAC,QAAA;UACA;UACAU,MAAA,CAAA/B,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}