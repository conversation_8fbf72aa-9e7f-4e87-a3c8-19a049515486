{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue", "mtime": 1752566540908}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getPermissionList", "createPermission", "updatePermission", "deletePermission", "waves", "parseTime", "Pagination", "name", "components", "directives", "filters", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "permission_type", "status", "typeTextMap", "menu", "button", "api", "typeTagMap", "parentOptions", "temp", "permission_id", "undefined", "parent_id", "permission_name", "permission_code", "permission_url", "permission_icon", "sort_order", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "then", "response", "filter", "item", "handleFilter", "resetTemp", "handleCreate", "_this2", "$nextTick", "$refs", "clearValidate", "createData", "_this3", "validate", "valid", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this4", "Object", "assign", "updateData", "_this5", "tempData", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText"], "sources": ["src/views/users/permissions.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <div class=\"filter-container\">\n      <el-input\n        v-model=\"listQuery.keyword\"\n        placeholder=\"搜索权限名称、编码\"\n        style=\"width: 200px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-select\n        v-model=\"listQuery.permission_type\"\n        placeholder=\"权限类型\"\n        clearable\n        style=\"width: 120px\"\n        class=\"filter-item\"\n      >\n        <el-option label=\"菜单\" value=\"menu\" />\n        <el-option label=\"按钮\" value=\"button\" />\n        <el-option label=\"接口\" value=\"api\" />\n      </el-select>\n      <el-select\n        v-model=\"listQuery.status\"\n        placeholder=\"状态\"\n        clearable\n        style=\"width: 120px\"\n        class=\"filter-item\"\n      >\n        <el-option label=\"启用\" value=\"active\" />\n        <el-option label=\"禁用\" value=\"disabled\" />\n      </el-select>\n      <el-button\n        v-waves\n        class=\"filter-item\"\n        type=\"primary\"\n        icon=\"el-icon-search\"\n        @click=\"handleFilter\"\n      >\n        搜索\n      </el-button>\n      <el-button\n        class=\"filter-item\"\n        style=\"margin-left: 10px;\"\n        type=\"primary\"\n        icon=\"el-icon-plus\"\n        @click=\"handleCreate\"\n      >\n        添加权限\n      </el-button>\n    </div>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"permission_id\"\n      default-expand-all\n    >\n      <el-table-column label=\"ID\" prop=\"permission_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"权限名称\" prop=\"permission_name\" width=\"150\" />\n      <el-table-column label=\"权限编码\" prop=\"permission_code\" width=\"180\" />\n      <el-table-column label=\"权限类型\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"typeTagMap[row.permission_type]\">\n            {{ typeTextMap[row.permission_type] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"权限URL\" prop=\"permission_url\" width=\"180\" />\n      <el-table-column label=\"权限图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.permission_icon\" :class=\"row.permission_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"父权限\" prop=\"parent_name\" width=\"150\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父权限\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父权限\" style=\"width: 100%\">\n            <el-option label=\"顶级权限\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.permission_id\"\n              :label=\"item.permission_name\"\n              :value=\"item.permission_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限名称\" prop=\"permission_name\">\n          <el-input v-model=\"temp.permission_name\" />\n        </el-form-item>\n        <el-form-item label=\"权限编码\" prop=\"permission_code\">\n          <el-input v-model=\"temp.permission_code\" />\n        </el-form-item>\n        <el-form-item label=\"权限类型\" prop=\"permission_type\">\n          <el-select v-model=\"temp.permission_type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\n            <el-option label=\"菜单\" value=\"menu\" />\n            <el-option label=\"按钮\" value=\"button\" />\n            <el-option label=\"接口\" value=\"api\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限URL\" prop=\"permission_url\">\n          <el-input v-model=\"temp.permission_url\" />\n        </el-form-item>\n        <el-form-item label=\"权限图标\" prop=\"permission_icon\">\n          <el-input v-model=\"temp.permission_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPermissionList, createPermission, updatePermission, deletePermission } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'PermissionList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: null,\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        permission_type: '',\n        status: ''\n      },\n      typeTextMap: {\n        menu: '菜单',\n        button: '按钮',\n        api: '接口'\n      },\n      typeTagMap: {\n        menu: 'primary',\n        button: 'success',\n        api: 'warning'\n      },\n      parentOptions: [],\n      temp: {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑权限',\n        create: '添加权限'\n      },\n      rules: {\n        permission_name: [{ required: true, message: '权限名称不能为空', trigger: 'blur' }],\n        permission_code: [{ required: true, message: '权限编码不能为空', trigger: 'blur' }],\n        permission_type: [{ required: true, message: '权限类型不能为空', trigger: 'change' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getPermissionList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n        \n        // 更新父权限选项\n        this.parentOptions = this.list.filter(item => item.permission_type === 'menu')\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createPermission(this.temp).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          updatePermission(tempData.permission_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该权限吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deletePermission(row.permission_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkLA,SAAAA,iBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;AACA,OAAAC,KAAA;AACA,SAAAC,SAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,OAAA;IACAL,SAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,eAAA;QACAC,MAAA;MACA;MACAC,WAAA;QACAC,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACAC,UAAA;QACAH,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACAE,aAAA;MACAC,IAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,eAAA;QACAC,eAAA;QACAb,eAAA;QACAc,cAAA;QACAC,eAAA;QACAd,MAAA;QACAe,UAAA;MACA;MACAC,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAV,eAAA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAZ,eAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAzB,eAAA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAlC,WAAA;MACAf,iBAAA,MAAAgB,SAAA,EAAAkC,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAApC,IAAA,GAAAsC,QAAA,CAAAxC,IAAA,CAAAE,IAAA;QACAoC,KAAA,CAAAnC,KAAA,GAAAqC,QAAA,CAAAxC,IAAA,CAAAG,KAAA;QACAmC,KAAA,CAAAlC,WAAA;;QAEA;QACAkC,KAAA,CAAAtB,aAAA,GAAAsB,KAAA,CAAApC,IAAA,CAAAuC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAjC,eAAA;QAAA;MACA;IACA;IACAkC,YAAA,WAAAA,aAAA;MACA,KAAAtC,SAAA,CAAAC,IAAA;MACA,KAAA8B,OAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACA,KAAA3B,IAAA;QACAC,aAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,eAAA;QACAC,eAAA;QACAb,eAAA;QACAc,cAAA;QACAC,eAAA;QACAd,MAAA;QACAe,UAAA;MACA;IACA;IACAoB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAAjB,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA/D,gBAAA,CAAA6D,MAAA,CAAAlC,IAAA,EAAAsB,IAAA;YACAY,MAAA,CAAAzB,iBAAA;YACAyB,MAAA,CAAAG,OAAA;cACAC,KAAA;cACAtB,OAAA;cACAuB,IAAA;cACAC,QAAA;YACA;YACAN,MAAA,CAAAf,OAAA;UACA;QACA;MACA;IACA;IACAsB,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA3C,IAAA,GAAA4C,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAAhC,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAa,MAAA,CAAAZ,KAAA,aAAAC,aAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAY,QAAA,GAAAJ,MAAA,CAAAC,MAAA,KAAAE,MAAA,CAAA/C,IAAA;UACA1B,gBAAA,CAAA0E,QAAA,CAAA/C,aAAA,EAAA+C,QAAA,EAAA1B,IAAA;YACAyB,MAAA,CAAAtC,iBAAA;YACAsC,MAAA,CAAAV,OAAA;cACAC,KAAA;cACAtB,OAAA;cACAuB,IAAA;cACAC,QAAA;YACA;YACAO,MAAA,CAAA5B,OAAA;UACA;QACA;MACA;IACA;IACA8B,YAAA,WAAAA,aAAAP,GAAA;MAAA,IAAAQ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAd,IAAA;MACA,GAAAjB,IAAA;QACA/C,gBAAA,CAAAmE,GAAA,CAAAzC,aAAA,EAAAqB,IAAA;UACA4B,MAAA,CAAAb,OAAA;YACAC,KAAA;YACAtB,OAAA;YACAuB,IAAA;YACAC,QAAA;UACA;UACAU,MAAA,CAAA/B,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}