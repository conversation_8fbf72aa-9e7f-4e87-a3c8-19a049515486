{"_from": "is-path-inside@^2.1.0", "_id": "is-path-inside@2.1.0", "_inBundle": false, "_integrity": "sha512-wiyhTzfDWsvwAW53OBWF5zuvaOGlZ6PwYxAbPVDhpm+gM09xKQGjBq/8uYN12aDvMxnAnq3dxTyoSoRNmg5YFg==", "_location": "/is-path-inside", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-path-inside@^2.1.0", "name": "is-path-inside", "escapedName": "is-path-inside", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/is-path-in-cwd"], "_resolved": "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-2.1.0.tgz", "_shasum": "7c9810587d659a40d27bcdb4d5616eab059494b2", "_spec": "is-path-inside@^2.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\is-path-in-cwd", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-path-inside/issues"}, "bundleDependencies": false, "dependencies": {"path-is-inside": "^1.0.2"}, "deprecated": false, "description": "Check if a path is inside another path", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-path-inside#readme", "keywords": ["path", "inside", "folder", "directory", "dir", "file", "resolve"], "license": "MIT", "name": "is-path-inside", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-path-inside.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.1.0"}