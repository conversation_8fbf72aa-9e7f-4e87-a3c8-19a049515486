{"_from": "is-ci@^1.0.10", "_id": "is-ci@1.2.1", "_inBundle": false, "_integrity": "sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg==", "_location": "/is-ci", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-ci@^1.0.10", "name": "is-ci", "escapedName": "is-ci", "rawSpec": "^1.0.10", "saveSpec": null, "fetchSpec": "^1.0.10"}, "_requiredBy": ["/yorkie"], "_resolved": "https://registry.npmmirror.com/is-ci/-/is-ci-1.2.1.tgz", "_shasum": "e3779c8ee17fccf428488f6e281187f2e632841c", "_spec": "is-ci@^1.0.10", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\yorkie", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "bin": {"is-ci": "bin.js"}, "bugs": {"url": "https://github.com/watson/is-ci/issues"}, "bundleDependencies": false, "coordinates": [55.778255, 12.593033], "dependencies": {"ci-info": "^1.5.0"}, "deprecated": false, "description": "Detect if the current environment is a CI server", "devDependencies": {"clear-require": "^1.0.1", "standard": "^11.0.1"}, "homepage": "https://github.com/watson/is-ci", "keywords": ["ci", "continuous", "integration", "test", "detect"], "license": "MIT", "main": "index.js", "name": "is-ci", "repository": {"type": "git", "url": "git+https://github.com/watson/is-ci.git"}, "scripts": {"test": "standard && node test.js"}, "version": "1.2.1"}