const express = require('express');
const { query } = require('../config/database');
const router = express.Router();

// 获取权限列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, keyword = '', status = '', permission_type = '' } = req.query;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (keyword) {
      whereClause += ' AND (permission_name LIKE ? OR permission_code LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }
    
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    if (permission_type) {
      whereClause += ' AND permission_type = ?';
      params.push(permission_type);
    }
    
    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM permissions ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    const listSql = `
      SELECT p.permission_id, p.parent_id, p.permission_name, p.permission_code, 
             p.permission_type, p.permission_url, p.permission_icon, p.status, 
             p.sort_order, p.created_at,
             parent.permission_name as parent_name
      FROM permissions p
      LEFT JOIN permissions parent ON p.parent_id = parent.permission_id
      ${whereClause}
      ORDER BY p.sort_order ASC, p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    params.push(parseInt(pageSize), offset);
    
    const list = await query(listSql, params);
    
    res.json({
      success: true,
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取权限列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取权限列表失败'
    });
  }
});

// 获取权限树
router.get('/tree', async (req, res) => {
  try {
    const sql = `
      SELECT permission_id, parent_id, permission_name, permission_code, 
             permission_type, permission_url, permission_icon, status, sort_order
      FROM permissions
      WHERE status = 'active'
      ORDER BY sort_order ASC, permission_id ASC
    `;
    
    const permissions = await query(sql);
    
    // 构建树形结构
    const buildTree = (items, parentId = 0) => {
      return items
        .filter(item => item.parent_id === parentId)
        .map(item => ({
          ...item,
          children: buildTree(items, item.permission_id)
        }));
    };
    
    const tree = buildTree(permissions);
    
    res.json({
      success: true,
      data: tree
    });
  } catch (error) {
    console.error('获取权限树失败:', error);
    res.status(500).json({
      success: false,
      message: '获取权限树失败'
    });
  }
});

// 获取权限详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT p.permission_id, p.parent_id, p.permission_name, p.permission_code, 
             p.permission_type, p.permission_url, p.permission_icon, p.status, 
             p.sort_order, p.created_at,
             parent.permission_name as parent_name
      FROM permissions p
      LEFT JOIN permissions parent ON p.parent_id = parent.permission_id
      WHERE p.permission_id = ?
    `;
    
    const result = await query(sql, [id]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }
    
    res.json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('获取权限详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取权限详情失败'
    });
  }
});

// 创建权限
router.post('/', async (req, res) => {
  try {
    const { 
      parent_id = 0, 
      permission_name, 
      permission_code, 
      permission_type = 'menu',
      permission_url = '',
      permission_icon = '',
      status = 'active',
      sort_order = 0
    } = req.body;
    
    if (!permission_name || !permission_code) {
      return res.status(400).json({
        success: false,
        message: '权限名称和权限编码不能为空'
      });
    }
    
    // 检查权限编码是否已存在
    const existCheck = await query('SELECT permission_id FROM permissions WHERE permission_code = ?', [permission_code]);
    if (existCheck.length > 0) {
      return res.status(400).json({
        success: false,
        message: '权限编码已存在'
      });
    }
    
    // 如果有父权限，检查父权限是否存在
    if (parent_id > 0) {
      const parentCheck = await query('SELECT permission_id FROM permissions WHERE permission_id = ?', [parent_id]);
      if (parentCheck.length === 0) {
        return res.status(400).json({
          success: false,
          message: '父权限不存在'
        });
      }
    }
    
    const insertSql = `
      INSERT INTO permissions (parent_id, permission_name, permission_code, permission_type, 
                              permission_url, permission_icon, status, sort_order)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const result = await query(insertSql, [
      parent_id, permission_name, permission_code, permission_type,
      permission_url, permission_icon, status, sort_order
    ]);
    
    res.json({
      success: true,
      message: '创建权限成功',
      data: {
        permission_id: result.insertId
      }
    });
  } catch (error) {
    console.error('创建权限失败:', error);
    res.status(500).json({
      success: false,
      message: '创建权限失败'
    });
  }
});

// 更新权限
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      parent_id, 
      permission_name, 
      permission_code, 
      permission_type,
      permission_url,
      permission_icon,
      status,
      sort_order
    } = req.body;
    
    // 检查权限是否存在
    const existCheck = await query('SELECT permission_id FROM permissions WHERE permission_id = ?', [id]);
    if (existCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }
    
    // 检查权限编码是否被其他权限使用
    if (permission_code) {
      const codeCheck = await query('SELECT permission_id FROM permissions WHERE permission_code = ? AND permission_id != ?', [permission_code, id]);
      if (codeCheck.length > 0) {
        return res.status(400).json({
          success: false,
          message: '权限编码已被使用'
        });
      }
    }
    
    // 如果有父权限，检查父权限是否存在且不是自己
    if (parent_id > 0) {
      if (parent_id == id) {
        return res.status(400).json({
          success: false,
          message: '不能将自己设为父权限'
        });
      }
      
      const parentCheck = await query('SELECT permission_id FROM permissions WHERE permission_id = ?', [parent_id]);
      if (parentCheck.length === 0) {
        return res.status(400).json({
          success: false,
          message: '父权限不存在'
        });
      }
    }
    
    let updateFields = [];
    let updateValues = [];
    
    if (parent_id !== undefined) {
      updateFields.push('parent_id = ?');
      updateValues.push(parent_id);
    }
    if (permission_name !== undefined) {
      updateFields.push('permission_name = ?');
      updateValues.push(permission_name);
    }
    if (permission_code !== undefined) {
      updateFields.push('permission_code = ?');
      updateValues.push(permission_code);
    }
    if (permission_type !== undefined) {
      updateFields.push('permission_type = ?');
      updateValues.push(permission_type);
    }
    if (permission_url !== undefined) {
      updateFields.push('permission_url = ?');
      updateValues.push(permission_url);
    }
    if (permission_icon !== undefined) {
      updateFields.push('permission_icon = ?');
      updateValues.push(permission_icon);
    }
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }
    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateValues.push(sort_order);
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有要更新的字段'
      });
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    const updateSql = `UPDATE permissions SET ${updateFields.join(', ')} WHERE permission_id = ?`;
    await query(updateSql, updateValues);
    
    res.json({
      success: true,
      message: '更新权限成功'
    });
  } catch (error) {
    console.error('更新权限失败:', error);
    res.status(500).json({
      success: false,
      message: '更新权限失败'
    });
  }
});

// 删除权限
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查是否有子权限
    const childCheck = await query('SELECT permission_id FROM permissions WHERE parent_id = ?', [id]);
    if (childCheck.length > 0) {
      return res.status(400).json({
        success: false,
        message: '存在子权限，不能删除'
      });
    }
    
    const result = await query('DELETE FROM permissions WHERE permission_id = ?', [id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '权限不存在'
      });
    }
    
    res.json({
      success: true,
      message: '删除权限成功'
    });
  } catch (error) {
    console.error('删除权限失败:', error);
    res.status(500).json({
      success: false,
      message: '删除权限失败'
    });
  }
});

module.exports = router;
