{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1752572375519}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovY29kZS9mYW54aWFvY2hhbmcvZnhjLXNodWF0aS1hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMi5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBMb2dvIGZyb20gJy4vTG9nbyc7CmltcG9ydCBTaWRlYmFySXRlbSBmcm9tICcuL1NpZGViYXJJdGVtJzsKaW1wb3J0IF92YXJpYWJsZXMgZnJvbSAnQC9zdHlsZXMvdmFyaWFibGVzLnNjc3MnOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgU2lkZWJhckl0ZW06IFNpZGViYXJJdGVtLAogICAgTG9nbzogTG9nbwogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgbWFwR2V0dGVycyhbJ3NpZGViYXInXSkpLCB7fSwgewogICAgcm91dGVzOiBmdW5jdGlvbiByb3V0ZXMoKSB7CiAgICAgIHJldHVybiB0aGlzLiRyb3V0ZXIub3B0aW9ucy5yb3V0ZXM7CiAgICB9LAogICAgYWN0aXZlTWVudTogZnVuY3Rpb24gYWN0aXZlTWVudSgpIHsKICAgICAgdmFyIHJvdXRlID0gdGhpcy4kcm91dGU7CiAgICAgIHZhciBtZXRhID0gcm91dGUubWV0YSwKICAgICAgICBwYXRoID0gcm91dGUucGF0aDsKICAgICAgLy8gaWYgc2V0IHBhdGgsIHRoZSBzaWRlYmFyIHdpbGwgaGlnaGxpZ2h0IHRoZSBwYXRoIHlvdSBzZXQKICAgICAgaWYgKG1ldGEuYWN0aXZlTWVudSkgewogICAgICAgIHJldHVybiBtZXRhLmFjdGl2ZU1lbnU7CiAgICAgIH0KICAgICAgcmV0dXJuIHBhdGg7CiAgICB9LAogICAgc2hvd0xvZ286IGZ1bmN0aW9uIHNob3dMb2dvKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2lkZWJhckxvZ287CiAgICB9LAogICAgdmFyaWFibGVzOiBmdW5jdGlvbiB2YXJpYWJsZXMoKSB7CiAgICAgIHJldHVybiBfdmFyaWFibGVzOwogICAgfSwKICAgIGlzQ29sbGFwc2U6IGZ1bmN0aW9uIGlzQ29sbGFwc2UoKSB7CiAgICAgIHJldHVybiAhdGhpcy5zaWRlYmFyLm9wZW5lZDsKICAgIH0KICB9KQp9Ow=="}, {"version": 3, "names": ["mapGetters", "Logo", "SidebarItem", "variables", "components", "computed", "_objectSpread", "routes", "$router", "options", "activeMenu", "route", "$route", "meta", "path", "showLogo", "$store", "state", "settings", "sidebarLogo", "isCollapse", "sidebar", "opened"], "sources": ["src/layout/components/Sidebar/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"{'has-logo':showLogo}\">\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n    <el-scrollbar wrap-class=\"scrollbar-wrapper\">\n      <el-menu\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        :background-color=\"variables.menuBg\"\n        :text-color=\"variables.menuText\"\n        :unique-opened=\"true\"\n        :active-text-color=\"variables.menuActiveText\"\n        :collapse-transition=\"false\"\n        mode=\"vertical\"\n      >\n        <sidebar-item v-for=\"route in routes\" :key=\"route.path\" :item=\"route\" :base-path=\"route.path\" />\n      </el-menu>\n    </el-scrollbar>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Logo from './Logo'\nimport SidebarItem from './SidebarItem'\nimport variables from '@/styles/variables.scss'\n\nexport default {\n  components: { SidebarItem, Logo },\n  computed: {\n    ...mapGetters([\n      'sidebar'\n    ]),\n    routes() {\n      return this.$router.options.routes\n    },\n    activeMenu() {\n      const route = this.$route\n      const { meta, path } = route\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        return meta.activeMenu\n      }\n      return path\n    },\n    showLogo() {\n      return this.$store.state.settings.sidebarLogo\n    },\n    variables() {\n      return variables\n    },\n    isCollapse() {\n      return !this.sidebar.opened\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAqBA,SAAAA,UAAA;AACA,OAAAC,IAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA;EACAC,UAAA;IAAAF,WAAA,EAAAA,WAAA;IAAAD,IAAA,EAAAA;EAAA;EACAI,QAAA,EAAAC,aAAA,CAAAA,aAAA,KACAN,UAAA,EACA,UACA;IACAO,MAAA,WAAAA,OAAA;MACA,YAAAC,OAAA,CAAAC,OAAA,CAAAF,MAAA;IACA;IACAG,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAE,IAAA;QAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA;MACA;MACA,IAAAD,IAAA,CAAAH,UAAA;QACA,OAAAG,IAAA,CAAAH,UAAA;MACA;MACA,OAAAI,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,WAAA;IACA;IACAhB,SAAA,WAAAA,UAAA;MACA,OAAAA,UAAA;IACA;IACAiB,UAAA,WAAAA,WAAA;MACA,aAAAC,OAAA,CAAAC,MAAA;IACA;EAAA;AAEA", "ignoreList": []}]}