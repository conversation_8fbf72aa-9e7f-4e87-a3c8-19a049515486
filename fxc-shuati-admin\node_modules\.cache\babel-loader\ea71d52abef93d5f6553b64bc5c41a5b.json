{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\index.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\index.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IHsgZGVmYXVsdCBhcyBOYXZiYXIgfSBmcm9tICcuL05hdmJhcic7CmV4cG9ydCB7IGRlZmF1bHQgYXMgU2lkZWJhciB9IGZyb20gJy4vU2lkZWJhcic7CmV4cG9ydCB7IGRlZmF1bHQgYXMgQXBwTWFpbiB9IGZyb20gJy4vQXBwTWFpbic7"}, {"version": 3, "names": ["default", "<PERSON><PERSON><PERSON>", "Sidebar", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/layout/components/index.js"], "sourcesContent": ["export { default as Navbar } from './Navbar'\nexport { default as Sidebar } from './Sidebar'\nexport { default as AppMain } from './AppMain'\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,MAAM,QAAQ,UAAU;AAC5C,SAASD,OAAO,IAAIE,OAAO,QAAQ,WAAW;AAC9C,SAASF,OAAO,IAAIG,OAAO,QAAQ,WAAW", "ignoreList": []}]}