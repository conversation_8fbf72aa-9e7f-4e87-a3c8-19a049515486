<!--question.wxml-->
<view class="container">
  <!-- 顶部进度条 -->
  <view class="progress-bar">
    <view class="progress-info">
      <text class="progress-text">{{currentIndex + 1}}/{{questions.length}}</text>
      <text class="time-text" wx:if="{{showTimer}}">{{timeText}}</text>
    </view>
    <progress 
      percent="{{progressPercent}}" 
      stroke-width="6" 
      activeColor="#409EFF" 
      backgroundColor="#f0f0f0" 
    />
  </view>

  <!-- 题目内容 -->
  <view class="question-content" wx:if="{{currentQuestion}}">
    <!-- 组合题材料 -->
    <view wx:if="{{currentQuestion.composite_content}}" class="composite-content card">
      <view class="composite-title">阅读材料</view>
      <rich-text nodes="{{currentQuestion.composite_content}}"></rich-text>
    </view>

    <!-- 题目 -->
    <view class="question-main card">
      <view class="question-title">
        <text class="question-number">第{{currentIndex + 1}}题</text>
        <text class="question-type">[{{currentQuestion.typeText}}]</text>
      </view>
      <view class="question-text">
        <rich-text nodes="{{currentQuestion.question_content}}"></rich-text>
      </view>
    </view>

    <!-- 选项 -->
    <view class="options-container" wx:if="{{currentQuestion.options && currentQuestion.options.length > 0}}">
      <view 
        wx:for="{{currentQuestion.options}}" 
        wx:key="index"
        class="option-item {{item.selected ? 'selected' : ''}} {{showAnswer && item.is_correct ? 'correct' : ''}} {{showAnswer && item.selected && !item.is_correct ? 'wrong' : ''}}"
        bindtap="selectOption"
        data-index="{{index}}"
      >
        <view class="option-label">{{item.label}}</view>
        <view class="option-content">
          <rich-text nodes="{{item.option_content}}"></rich-text>
        </view>
        <view class="option-check" wx:if="{{item.selected}}">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>

    <!-- 填空题输入框 -->
    <view class="input-container card" wx:if="{{currentQuestion.question_type === 'fill'}}">
      <textarea 
        value="{{fillAnswer}}"
        placeholder="请输入答案"
        class="fill-input"
        disabled="{{showAnswer}}"
        bindinput="onFillInput"
      ></textarea>
    </view>

    <!-- 简答题输入框 -->
    <view class="input-container card" wx:if="{{currentQuestion.question_type === 'essay'}}">
      <textarea 
        value="{{essayAnswer}}"
        placeholder="请输入答案"
        class="essay-input"
        disabled="{{showAnswer}}"
        bindinput="onEssayInput"
      ></textarea>
    </view>

    <!-- 答案解析 -->
    <view class="explanation-container card" wx:if="{{showAnswer && currentQuestion.explanation}}">
      <view class="explanation-title">答案解析</view>
      <view class="explanation-content">
        <rich-text nodes="{{currentQuestion.explanation.explanation_content}}"></rich-text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button 
      class="action-btn btn-secondary" 
      bindtap="prevQuestion"
      disabled="{{currentIndex === 0}}"
    >
      上一题
    </button>
    
    <button 
      class="action-btn btn-favorite {{isFavorited ? 'active' : ''}}"
      bindtap="toggleFavorite"
    >
      {{isFavorited ? '已收藏' : '收藏'}}
    </button>
    
    <button 
      wx:if="{{!showAnswer}}"
      class="action-btn btn-primary" 
      bindtap="submitAnswer"
      disabled="{{!hasAnswer}}"
    >
      提交答案
    </button>
    
    <button 
      wx:else
      class="action-btn btn-primary" 
      bindtap="nextQuestion"
    >
      {{currentIndex === questions.length - 1 ? '完成' : '下一题'}}
    </button>
  </view>

  <!-- 退出确认弹窗 -->
  <view class="modal-mask" wx:if="{{showExitModal}}" bindtap="hideExitModal">
    <view class="modal-content" catchtap="">
      <view class="modal-title">确认退出</view>
      <view class="modal-text">确定要退出答题吗？当前进度将会丢失。</view>
      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="hideExitModal">取消</button>
        <button class="modal-btn confirm" bindtap="confirmExit">确定</button>
      </view>
    </view>
  </view>
</view>
