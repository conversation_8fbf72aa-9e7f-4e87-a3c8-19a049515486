{"_from": "ini@^1.3.4", "_id": "ini@1.3.8", "_inBundle": false, "_integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==", "_location": "/ini", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ini@^1.3.4", "name": "ini", "escapedName": "ini", "rawSpec": "^1.3.4", "saveSpec": null, "fetchSpec": "^1.3.4"}, "_requiredBy": ["/config-chain"], "_resolved": "https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz", "_shasum": "a29da425b48806f34767a4efce397269af28432c", "_spec": "ini@^1.3.4", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\config-chain", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "bundleDependencies": false, "deprecated": false, "description": "An ini encoder/decoder for node", "devDependencies": {"eslint": "^7.9.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "tap": "14"}, "files": ["ini.js"], "homepage": "https://github.com/isaacs/ini#readme", "license": "ISC", "main": "ini.js", "name": "ini", "repository": {"type": "git", "url": "git://github.com/isaacs/ini.git"}, "scripts": {"eslint": "eslint", "lint": "npm run eslint -- ini.js test/*.js", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "test": "tap"}, "version": "1.3.8"}