// question.js
const { api } = require('../../utils/api.js')
const { getQuestionTypeText, getOptionLabel, formatCountdown, showSuccess, showError } = require('../../utils/util.js')

Page({
  data: {
    questions: [],
    currentIndex: 0,
    fillAnswer: '',
    essayAnswer: '',
    showAnswer: false,
    isFavorited: false,
    showTimer: true,
    timeLeft: 1800, // 30分钟
    timeText: '30:00',
    timer: null,
    showExitModal: false,
    practiceStartTime: null,
    answerRecords: [] // 答题记录
  },

  onLoad(options) {
    console.log('答题页面加载', options)
    this.setData({
      practiceStartTime: Date.now()
    })
    
    // 设置页面标题
    if (options.title) {
      wx.setNavigationBarTitle({
        title: options.title
      })
    }
    
    this.loadQuestions(options)
    this.startTimer()
  },

  onUnload() {
    this.clearTimer()
  },

  onShow() {
    // 监听返回按钮
    wx.onBackPress && wx.onBackPress(() => {
      this.showExitConfirm()
      return true
    })
  },

  // 加载题目数据
  loadQuestions(options) {
    // 使用mock数据
    const mockQuestions = [
      {
        question_id: 1,
        question_type: 'single',
        question_content: 'What is the capital of France?',
        options: [
          { option_content: 'Paris', is_correct: 1 },
          { option_content: 'London', is_correct: 0 },
          { option_content: 'Berlin', is_correct: 0 },
          { option_content: 'Madrid', is_correct: 0 }
        ],
        explanation: {
          explanation_content: 'Paris is the capital and most populous city of France.'
        }
      },
      {
        question_id: 2,
        question_type: 'multiple',
        question_content: 'Which of the following cities are in France?',
        options: [
          { option_content: 'Paris', is_correct: 1 },
          { option_content: 'Lyon', is_correct: 1 },
          { option_content: 'Berlin', is_correct: 0 },
          { option_content: 'Marseille', is_correct: 1 }
        ],
        explanation: {
          explanation_content: 'Paris, Lyon, and Marseille are all cities in France. Berlin is in Germany.'
        }
      }
    ]

    // 处理题目数据
    const questions = mockQuestions.map(q => ({
      ...q,
      typeText: getQuestionTypeText(q.question_type),
      options: q.options ? q.options.map((opt, index) => ({
        ...opt,
        label: getOptionLabel(index),
        selected: false
      })) : null
    }))

    this.setData({
      questions,
      currentQuestion: questions[0] || null,
      progressPercent: questions.length > 0 ? (1 / questions.length) * 100 : 0
    })

    this.checkFavoriteStatus()
  },

  // 开始计时
  startTimer() {
    if (this.data.showTimer) {
      this.data.timer = setInterval(() => {
        const timeLeft = this.data.timeLeft - 1
        if (timeLeft >= 0) {
          this.setData({
            timeLeft,
            timeText: formatCountdown(timeLeft)
          })
        } else {
          this.clearTimer()
          this.timeUp()
        }
      }, 1000)
    }
  },

  // 清除计时器
  clearTimer() {
    if (this.data.timer) {
      clearInterval(this.data.timer)
      this.setData({
        timer: null
      })
    }
  },

  // 时间到
  timeUp() {
    wx.showModal({
      title: '时间到',
      content: '答题时间已结束',
      showCancel: false,
      success: () => {
        this.finishPractice()
      }
    })
  },

  // 选择选项
  selectOption(e) {
    if (this.data.showAnswer) return
    
    const index = e.currentTarget.dataset.index
    const { currentQuestion, currentIndex, questions } = this.data
    
    if (!currentQuestion || !currentQuestion.options) return
    
    const newQuestions = [...questions]
    const question = newQuestions[currentIndex]
    
    if (question.question_type === 'single' || question.question_type === 'judge') {
      // 单选题：取消其他选项，选中当前选项
      question.options.forEach((opt, i) => {
        opt.selected = i === index
      })
    } else if (question.question_type === 'multiple') {
      // 多选题：切换当前选项状态
      question.options[index].selected = !question.options[index].selected
    }
    
    this.setData({
      questions: newQuestions,
      currentQuestion: question,
      hasAnswer: this.checkHasAnswer(question)
    })
  },

  // 检查是否有答案
  checkHasAnswer(question) {
    if (question.question_type === 'fill') {
      return this.data.fillAnswer.trim() !== ''
    } else if (question.question_type === 'essay') {
      return this.data.essayAnswer.trim() !== ''
    } else {
      return question.options && question.options.some(opt => opt.selected)
    }
  },

  // 填空题输入
  onFillInput(e) {
    this.setData({
      fillAnswer: e.detail.value,
      hasAnswer: e.detail.value.trim() !== ''
    })
  },

  // 简答题输入
  onEssayInput(e) {
    this.setData({
      essayAnswer: e.detail.value,
      hasAnswer: e.detail.value.trim() !== ''
    })
  },

  // 提交答案
  submitAnswer() {
    const { currentQuestion } = this.data
    if (!currentQuestion) return
    
    this.setData({
      showAnswer: true
    })
    
    // 判断答案是否正确
    let isCorrect = false
    if (currentQuestion.question_type === 'single' || currentQuestion.question_type === 'judge') {
      const selectedOption = currentQuestion.options.find(opt => opt.selected)
      isCorrect = selectedOption && selectedOption.is_correct === 1
    } else if (currentQuestion.question_type === 'multiple') {
      const selectedOptions = currentQuestion.options.filter(opt => opt.selected)
      const correctOptions = currentQuestion.options.filter(opt => opt.is_correct === 1)
      isCorrect = selectedOptions.length === correctOptions.length &&
        selectedOptions.every(opt => opt.is_correct === 1)
    } else {
      // 填空题和简答题暂时标记为正确
      isCorrect = true
    }
    
    // 记录答题结果
    this.recordAnswer(isCorrect)
    
    // 显示结果提示
    wx.showToast({
      title: isCorrect ? '回答正确!' : '回答错误!',
      icon: isCorrect ? 'success' : 'error'
    })
  },

  // 记录答案
  recordAnswer(isCorrect) {
    const { currentQuestion, currentIndex, fillAnswer, essayAnswer } = this.data
    const record = {
      questionId: currentQuestion.question_id,
      questionIndex: currentIndex,
      isCorrect,
      userAnswer: this.getUserAnswer(currentQuestion),
      answerTime: Date.now() - this.data.practiceStartTime
    }
    
    this.data.answerRecords.push(record)
    
    // 如果答错了，添加到错题本
    if (!isCorrect) {
      this.addToErrors(currentQuestion, record.userAnswer)
    }
  },

  // 获取用户答案
  getUserAnswer(question) {
    if (question.question_type === 'fill') {
      return this.data.fillAnswer
    } else if (question.question_type === 'essay') {
      return this.data.essayAnswer
    } else {
      const selectedOptions = question.options.filter(opt => opt.selected)
      return selectedOptions.map(opt => opt.option_content).join(', ')
    }
  },

  // 添加到错题本
  addToErrors(question, userAnswer) {
    // 这里应该调用API添加到错题本
    console.log('添加到错题本:', question.question_id, userAnswer)
  },

  // 下一题
  nextQuestion() {
    const { currentIndex, questions } = this.data
    if (currentIndex < questions.length - 1) {
      this.setData({
        currentIndex: currentIndex + 1,
        currentQuestion: questions[currentIndex + 1],
        progressPercent: ((currentIndex + 2) / questions.length) * 100
      })
      this.resetQuestion()
    } else {
      this.finishPractice()
    }
  },

  // 上一题
  prevQuestion() {
    const { currentIndex, questions } = this.data
    if (currentIndex > 0) {
      this.setData({
        currentIndex: currentIndex - 1,
        currentQuestion: questions[currentIndex - 1],
        progressPercent: (currentIndex / questions.length) * 100
      })
      this.resetQuestion()
    }
  },

  // 重置题目状态
  resetQuestion() {
    this.setData({
      showAnswer: false,
      fillAnswer: '',
      essayAnswer: '',
      hasAnswer: false
    })
    this.checkFavoriteStatus()
  },

  // 切换收藏状态
  toggleFavorite() {
    const isFavorited = !this.data.isFavorited
    this.setData({
      isFavorited
    })
    
    // 这里应该调用API
    wx.showToast({
      title: isFavorited ? '已收藏' : '已取消收藏',
      icon: 'success'
    })
  },

  // 检查收藏状态
  checkFavoriteStatus() {
    // 这里应该调用API检查收藏状态
    this.setData({
      isFavorited: false
    })
  },

  // 显示退出确认
  showExitConfirm() {
    this.setData({
      showExitModal: true
    })
  },

  // 隐藏退出确认
  hideExitModal() {
    this.setData({
      showExitModal: false
    })
  },

  // 确认退出
  confirmExit() {
    this.clearTimer()
    wx.navigateBack()
  },

  // 完成练习
  finishPractice() {
    this.clearTimer()
    
    // 计算统计数据
    const { answerRecords, questions } = this.data
    const correctCount = answerRecords.filter(r => r.isCorrect).length
    const totalCount = questions.length
    const accuracy = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0
    
    // 跳转到结果页面
    wx.redirectTo({
      url: `/pages/result/result?correctCount=${correctCount}&totalCount=${totalCount}&accuracy=${accuracy}`
    })
  }
})
