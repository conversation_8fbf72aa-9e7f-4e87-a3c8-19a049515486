{"_from": "is-data-descriptor@^1.0.1", "_id": "is-data-descriptor@1.0.1", "_inBundle": false, "_integrity": "sha512-bc4NlCDiCr28U4aEsQ3Qs2491gVq4V8G7MQyws968ImqjKuYtTJXrl7Vq7jsN7Ly/C3xj5KWFrY7sHNeDkAzXw==", "_location": "/is-data-descriptor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-data-descriptor@^1.0.1", "name": "is-data-descriptor", "escapedName": "is-data-descriptor", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/base/is-descriptor", "/define-property/is-descriptor", "/extglob/is-descriptor", "/is-descriptor", "/snapdragon-node/is-descriptor", "/svg-baker/is-descriptor"], "_resolved": "https://registry.npmmirror.com/is-data-descriptor/-/is-data-descriptor-1.0.1.tgz", "_shasum": "2109164426166d32ea38c405c1e0945d9e6a4eeb", "_spec": "is-data-descriptor@^1.0.1", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\is-descriptor", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-data-descriptor/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}, {"name": "<PERSON>", "url": "https://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "dependencies": {"hasown": "^2.0.0"}, "deprecated": false, "description": "Returns true if a value has the characteristics of a valid JavaScript data descriptor.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.2"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/inspect-js/is-data-descriptor", "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "license": "MIT", "main": "index.js", "name": "is-data-descriptor", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-data-descriptor.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.0.1"}