{"_from": "vue-router@3.0.6", "_id": "vue-router@3.0.6", "_inBundle": false, "_integrity": "sha512-Ox0ciFLswtSGRTHYhGvx2L44sVbTPNS+uD2kRISuo8B39Y79rOo0Kw0hzupTmiVtftQYCZl87mwldhh2L9Aquw==", "_location": "/vue-router", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-router@3.0.6", "name": "vue-router", "escapedName": "vue-router", "rawSpec": "3.0.6", "saveSpec": null, "fetchSpec": "3.0.6"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/vue-router/-/vue-router-3.0.6.tgz", "_shasum": "2e4f0f9cbb0b96d0205ab2690cfe588935136ac3", "_spec": "vue-router@3.0.6", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/vue-router/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Official router for Vue.js 2", "devDependencies": {"babel-core": "^6.24.1", "babel-eslint": "^10.0.1", "babel-loader": "^7.1.3", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-preset-env": "^1.6.1", "babel-preset-flow-vue": "^1.0.0", "buble": "^0.19.3", "chromedriver": "^2.35.0", "conventional-changelog-cli": "^2.0.11", "cross-spawn": "^5.0.1", "css-loader": "^0.28.10", "es6-promise": "^4.2.4", "eslint": "^4.19.1", "eslint-plugin-flowtype": "^2.46.1", "eslint-plugin-vue-libs": "^2.1.0", "express": "^4.16.2", "express-urlrewrite": "^1.2.0", "flow-bin": "^0.66.0", "jasmine": "2.8.0", "lint-staged": "^8.1.0", "nightwatch": "^0.9.20", "nightwatch-helpers": "^1.0.0", "path-to-regexp": "^1.7.0", "phantomjs-prebuilt": "^2.1.16", "rollup": "^0.56.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-flow-no-whitespace": "^1.0.0", "rollup-plugin-node-resolve": "^3.0.3", "rollup-plugin-replace": "^2.0.0", "rollup-watch": "^4.0.0", "selenium-server": "^2.53.1", "terser": "^3.17.0", "typescript": "^3.3.1", "vue": "^2.5.16", "vue-loader": "^15.2.1", "vue-template-compiler": "^2.5.16", "vuepress": "^0.14.1", "vuepress-theme-vue": "^1.1.0", "webpack": "^4.9.0", "webpack-dev-middleware": "^3.1.3", "yorkie": "^2.0.0"}, "files": ["src/*.js", "dist/*.js", "types/*.d.ts"], "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommitMsg.js"}, "homepage": "https://github.com/vuejs/vue-router#readme", "jsdelivr": "dist/vue-router.js", "keywords": ["vue", "router", "routing"], "license": "MIT", "lint-staged": {"*.{js,vue}": ["eslint --fix", "git add"]}, "main": "dist/vue-router.common.js", "module": "dist/vue-router.esm.js", "name": "vue-router", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-router.git"}, "scripts": {"build": "node build/build.js", "changelog": "conventional-changelog -p angular -r 1 -i CHANGELOG.md -s", "dev": "node examples/server.js", "dev:dist": "rollup -wm -c build/rollup.dev.config.js", "docs": "vuepress dev docs", "docs:build": "vuepress build docs", "flow": "flow check", "lint": "eslint src test examples", "release": "bash scripts/release.sh", "test": "npm run lint && npm run flow && npm run test:unit && npm run test:e2e && npm run test:types", "test:e2e": "node test/e2e/runner.js", "test:types": "tsc -p types/test", "test:unit": "jasmine JASMINE_CONFIG_PATH=test/unit/jasmine.json"}, "sideEffects": false, "typings": "types/index.d.ts", "unpkg": "dist/vue-router.js", "version": "3.0.6"}