{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\list.vue", "mtime": 1752570827537}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEFkbWluVXNlckxpc3QsIGNyZWF0ZUFkbWluVXNlciwgdXBkYXRlQWRtaW5Vc2VyLCBkZWxldGVBZG1pblVzZXIgfSBmcm9tICdAL2FwaS91c2VycycKaW1wb3J0IHsgZ2V0Um9sZUxpc3QgfSBmcm9tICdAL2FwaS91c2VycycKaW1wb3J0IHdhdmVzIGZyb20gJ0AvZGlyZWN0aXZlL3dhdmVzJwppbXBvcnQgeyBwYXJzZVRpbWUgfSBmcm9tICdAL3V0aWxzJwppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnVXNlckxpc3QnLAogIGNvbXBvbmVudHM6IHsgUGFnaW5hdGlvbiB9LAogIGRpcmVjdGl2ZXM6IHsgd2F2ZXMgfSwKICBmaWx0ZXJzOiB7CiAgICBwYXJzZVRpbWUKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJsZUtleTogMCwKICAgICAgbGlzdDogW10sCiAgICAgIHRvdGFsOiAwLAogICAgICBsaXN0TG9hZGluZzogdHJ1ZSwKICAgICAgbGlzdFF1ZXJ5OiB7CiAgICAgICAgcGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAga2V5d29yZDogJycsCiAgICAgICAgc3RhdHVzOiAnJwogICAgICB9LAogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIHRlbXA6IHsKICAgICAgICBhZG1pbl9pZDogdW5kZWZpbmVkLAogICAgICAgIHVzZXJuYW1lOiAnJywKICAgICAgICBwYXNzd29yZDogJycsCiAgICAgICAgcmVhbF9uYW1lOiAnJywKICAgICAgICBlbWFpbDogJycsCiAgICAgICAgcGhvbmU6ICcnLAogICAgICAgIHJvbGVfaWRzOiBbXSwKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnCiAgICAgIH0sCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nU3RhdHVzOiAnJywKICAgICAgdGV4dE1hcDogewogICAgICAgIHVwZGF0ZTogJ+e8lui+keeuoeeQhuWRmCcsCiAgICAgICAgY3JlYXRlOiAn5re75Yqg566h55CG5ZGYJwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIHVzZXJuYW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+eUqOaIt+WQjeS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9XSwKICAgICAgICBwYXNzd29yZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICflr4bnoIHkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0KICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB0aGlzLmdldFJvbGVPcHRpb25zKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlCiAgICAgIGdldEFkbWluVXNlckxpc3QodGhpcy5saXN0UXVlcnkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdAogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlCiAgICAgIH0pCiAgICB9LAogICAgZ2V0Um9sZU9wdGlvbnMoKSB7CiAgICAgIGdldFJvbGVMaXN0KHsgcGFnZVNpemU6IDEwMCB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YS5saXN0CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRmlsdGVyKCkgewogICAgICB0aGlzLmxpc3RRdWVyeS5wYWdlID0gMQogICAgICB0aGlzLmdldExpc3QoKQogICAgfSwKICAgIHJlc2V0VGVtcCgpIHsKICAgICAgdGhpcy50ZW1wID0gewogICAgICAgIGFkbWluX2lkOiB1bmRlZmluZWQsCiAgICAgICAgdXNlcm5hbWU6ICcnLAogICAgICAgIHBhc3N3b3JkOiAnJywKICAgICAgICByZWFsX25hbWU6ICcnLAogICAgICAgIGVtYWlsOiAnJywKICAgICAgICBwaG9uZTogJycsCiAgICAgICAgcm9sZV9pZHM6IFtdLAogICAgICAgIHN0YXR1czogJ2FjdGl2ZScKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUNyZWF0ZSgpIHsKICAgICAgdGhpcy5yZXNldFRlbXAoKQogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICdjcmVhdGUnCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICB9KQogICAgfSwKICAgIGNyZWF0ZURhdGEoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBjcmVhdGVBZG1pblVzZXIodGhpcy50ZW1wKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5saXN0LnVuc2hpZnQodGhpcy50ZW1wKQogICAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIm+W7uuaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMudGVtcCA9IE9iamVjdC5hc3NpZ24oe30sIHJvdykKICAgICAgdGhpcy50ZW1wLnBhc3N3b3JkID0gJycKICAgICAgdGhpcy5kaWFsb2dTdGF0dXMgPSAndXBkYXRlJwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS5jbGVhclZhbGlkYXRlKCkKICAgICAgfSkKICAgIH0sCiAgICB1cGRhdGVEYXRhKCkgewogICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgY29uc3QgdGVtcERhdGEgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnRlbXApCiAgICAgICAgICBpZiAoIXRlbXBEYXRhLnBhc3N3b3JkKSB7CiAgICAgICAgICAgIGRlbGV0ZSB0ZW1wRGF0YS5wYXNzd29yZAogICAgICAgICAgfQogICAgICAgICAgdXBkYXRlQWRtaW5Vc2VyKHRlbXBEYXRhLmFkbWluX2lkLCB0ZW1wRGF0YSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZQogICAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5pu05paw5oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6K+l566h55CG5ZGY5ZCX77yfJywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGRlbGV0ZUFkbWluVXNlcihyb3cuYWRtaW5faWQpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfJywKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/users", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <div class=\"filter-container\">\n      <el-input\n        v-model=\"listQuery.keyword\"\n        placeholder=\"搜索用户名、姓名、邮箱\"\n        style=\"width: 200px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-select\n        v-model=\"listQuery.status\"\n        placeholder=\"状态\"\n        clearable\n        style=\"width: 120px\"\n        class=\"filter-item\"\n      >\n        <el-option label=\"正常\" value=\"active\" />\n        <el-option label=\"禁用\" value=\"banned\" />\n      </el-select>\n      <el-button\n        v-waves\n        class=\"filter-item\"\n        type=\"primary\"\n        icon=\"el-icon-search\"\n        @click=\"handleFilter\"\n      >\n        搜索\n      </el-button>\n      <el-button\n        class=\"filter-item\"\n        style=\"margin-left: 10px;\"\n        type=\"primary\"\n        icon=\"el-icon-plus\"\n        @click=\"handleCreate\"\n      >\n        添加管理员\n      </el-button>\n    </div>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"admin_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"用户名\" prop=\"username\" width=\"120\" />\n      <el-table-column label=\"真实姓名\" prop=\"real_name\" width=\"120\" />\n      <el-table-column label=\"邮箱\" prop=\"email\" width=\"200\" />\n      <el-table-column label=\"手机号\" prop=\"phone\" width=\"120\" />\n      <el-table-column label=\"角色\" width=\"150\">\n        <template slot-scope=\"{row}\">\n          <el-tag\n            v-for=\"role in (row.roles || '').split(',')\"\n            :key=\"role\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n          >\n            {{ role }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'danger'\">\n            {{ row.status === 'active' ? '正常' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"最后登录\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.last_login_time | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            v-if=\"row.admin_id !== 1\"\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"temp.username\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"temp.password\" type=\"password\" show-password />\n        </el-form-item>\n        <el-form-item label=\"真实姓名\" prop=\"real_name\">\n          <el-input v-model=\"temp.real_name\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"temp.email\" />\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"temp.phone\" />\n        </el-form-item>\n        <el-form-item label=\"角色\" prop=\"role_ids\">\n          <el-select v-model=\"temp.role_ids\" multiple placeholder=\"请选择角色\">\n            <el-option\n              v-for=\"role in roleOptions\"\n              :key=\"role.role_id\"\n              :label=\"role.role_name\"\n              :value=\"role.role_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"正常\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"banned\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getAdminUserList, createAdminUser, updateAdminUser, deleteAdminUser } from '@/api/users'\nimport { getRoleList } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'UserList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      roleOptions: [],\n      temp: {\n        admin_id: undefined,\n        username: '',\n        password: '',\n        real_name: '',\n        email: '',\n        phone: '',\n        role_ids: [],\n        status: 'active'\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑管理员',\n        create: '添加管理员'\n      },\n      rules: {\n        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],\n        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getRoleOptions()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getAdminUserList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n      })\n    },\n    getRoleOptions() {\n      getRoleList({ pageSize: 100 }).then(response => {\n        this.roleOptions = response.data.list\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        admin_id: undefined,\n        username: '',\n        password: '',\n        real_name: '',\n        email: '',\n        phone: '',\n        role_ids: [],\n        status: 'active'\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createAdminUser(this.temp).then(() => {\n            this.list.unshift(this.temp)\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.temp.password = ''\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          if (!tempData.password) {\n            delete tempData.password\n          }\n          updateAdminUser(tempData.admin_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该管理员吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteAdminUser(row.admin_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    }\n  }\n}\n</script>\n"]}]}