{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SidebarItem.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SidebarItem.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\n  <div v-if=\"!item.hidden\">\n    <template v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path)\">\n        <el-menu-item :index=\"resolvePath(onlyOneChild.path)\" :class=\"{'submenu-title-noDropdown':!isNest}\">\n          <item :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\" :title=\"onlyOneChild.meta.title\" />\n        </el-menu-item>\n      </app-link>\n    </template>\n\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\n      <template slot=\"title\">\n        <item v-if=\"item.meta\" :icon=\"item.meta && item.meta.icon\" :title=\"item.meta.title\" />\n      </template>\n      <sidebar-item\n        v-for=\"child in item.children\"\n        :key=\"child.path\"\n        :is-nest=\"true\"\n        :item=\"child\"\n        :base-path=\"resolvePath(child.path)\"\n        class=\"nest-menu\"\n      />\n    </el-submenu>\n  </div>\n</template>\n\n<script>\nimport path from 'path'\nimport { isExternal } from '@/utils/validate'\nimport Item from './Item'\nimport AppLink from './Link'\nimport FixiOSBug from './FixiOSBug'\n\nexport default {\n  name: 'SidebarItem',\n  components: { Item, AppLink },\n  mixins: [FixiOSBug],\n  props: {\n    // route object\n    item: {\n      type: Object,\n      required: true\n    },\n    isNest: {\n      type: Boolean,\n      default: false\n    },\n    basePath: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237\n    // TODO: refactor with render function\n    this.onlyOneChild = null\n    return {}\n  },\n  methods: {\n    hasOneShowingChild(children = [], parent) {\n      const showingChildren = children.filter(item => {\n        if (item.hidden) {\n          return false\n        } else {\n          // Temp set(will be used if only has one showing child)\n          this.onlyOneChild = item\n          return true\n        }\n      })\n\n      // When there is only one child router, the child router is displayed by default\n      if (showingChildren.length === 1) {\n        return true\n      }\n\n      // Show parent if there are no child router to display\n      if (showingChildren.length === 0) {\n        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }\n        return true\n      }\n\n      return false\n    },\n    resolvePath(routePath) {\n      if (isExternal(routePath)) {\n        return routePath\n      }\n      if (isExternal(this.basePath)) {\n        return this.basePath\n      }\n      return path.resolve(this.basePath, routePath)\n    }\n  }\n}\n</script>\n"]}]}