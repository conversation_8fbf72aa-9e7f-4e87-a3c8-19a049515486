{"_from": "cli-cursor@^3.1.0", "_id": "cli-cursor@3.1.0", "_inBundle": false, "_integrity": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==", "_location": "/inquirer/cli-cursor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cli-cursor@^3.1.0", "name": "cli-cursor", "escapedName": "cli-cursor", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/inquirer"], "_resolved": "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz", "_shasum": "264305a7ae490d1d03bf0c9ba7c925d1753af307", "_spec": "cli-cursor@^3.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\inquirer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/cli-cursor/issues"}, "bundleDependencies": false, "dependencies": {"restore-cursor": "^3.1.0"}, "deprecated": false, "description": "Toggle the CLI cursor", "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/cli-cursor#readme", "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "license": "MIT", "name": "cli-cursor", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/cli-cursor.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.1.0"}