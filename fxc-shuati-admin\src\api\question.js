import request from '@/utils/request'

// 题目分类相关API
export function getCategoryTree() {
  return request({
    url: '/api/categories/tree',
    method: 'get'
  })
}

export function getCategoryList(params) {
  return request({
    url: '/api/categories',
    method: 'get',
    params
  })
}

export function createCategory(data) {
  return request({
    url: '/api/categories',
    method: 'post',
    data
  })
}

export function updateCategory(id, data) {
  return request({
    url: `/api/categories/${id}`,
    method: 'put',
    data
  })
}

export function deleteCategory(id) {
  return request({
    url: `/api/categories/${id}`,
    method: 'delete'
  })
}

// 题目相关API
export function getQuestionList(params) {
  return request({
    url: '/api/questions',
    method: 'get',
    params
  })
}

export function getQuestionDetail(id) {
  return request({
    url: `/api/questions/${id}`,
    method: 'get'
  })
}

export function createQuestion(data) {
  return request({
    url: '/api/questions',
    method: 'post',
    data
  })
}

export function updateQuestion(id, data) {
  return request({
    url: `/api/questions/${id}`,
    method: 'put',
    data
  })
}

export function deleteQuestion(id) {
  return request({
    url: `/api/questions/${id}`,
    method: 'delete'
  })
}

export function batchDeleteQuestions(ids) {
  return request({
    url: '/api/questions/batch',
    method: 'delete',
    data: { ids }
  })
}

// 题目导入导出
export function uploadQuestions(data) {
  return request({
    url: '/api/questions/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function exportQuestions(params) {
  return request({
    url: '/api/questions/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 题目统计
export function getQuestionStats() {
  return request({
    url: '/api/questions/stats',
    method: 'get'
  })
}

// 组合题相关API
export function getCompositeList(params) {
  return request({
    url: '/api/composite',
    method: 'get',
    params
  })
}

export function createComposite(data) {
  return request({
    url: '/api/composite',
    method: 'post',
    data
  })
}

export function updateComposite(id, data) {
  return request({
    url: `/api/composite/${id}`,
    method: 'put',
    data
  })
}

export function deleteComposite(id) {
  return request({
    url: `/api/composite/${id}`,
    method: 'delete'
  })
}
