const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  user: process.env.DB_USER || 'fxc-shuati',
  password: process.env.DB_PASSWORD || 'haf6LeMjHLKefKs7',
  database: process.env.DB_NAME || 'fxc-shuati',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4',
  timezone: '+08:00',
  connectTimeout: 30000,        // 连接超时时间 30秒
  waitForConnections: true,
  connectionLimit: 5,           // 减少连接数
  queueLimit: 0,
  ssl: false,                   // 禁用SSL
  multipleStatements: false     // 禁用多语句查询
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 监听连接池事件
pool.on('connection', function (connection) {
  console.log('新的数据库连接建立: ' + connection.threadId);
});

pool.on('error', function(err) {
  console.error('数据库连接池错误:', err);
  if(err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('数据库连接丢失，尝试重新连接...');
  }
});

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('数据库连接成功');

    // 执行一个简单的查询来确保连接正常
    await connection.query('SELECT 1');
    console.log('数据库查询测试成功');

    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error.message);
    return false;
  }
}

// 执行查询（带重试机制）
async function query(sql, params = [], retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const [rows] = await pool.query(sql, params);
      return rows;
    } catch (error) {
      console.error(`数据库查询错误 (尝试 ${i + 1}/${retries}):`, error.message);

      // 如果是连接相关错误且还有重试次数，则等待后重试
      if ((error.code === 'ECONNRESET' ||
           error.code === 'PROTOCOL_CONNECTION_LOST' ||
           error.code === 'ENOTFOUND' ||
           error.code === 'ETIMEDOUT') && i < retries - 1) {
        console.log(`等待 ${(i + 1) * 1000}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, (i + 1) * 1000));
        continue;
      }

      // 如果不是连接错误或已达到最大重试次数，直接抛出错误
      throw error;
    }
  }
}

// 执行事务
async function transaction(callback) {
  const connection = await pool.getConnection();
  await connection.beginTransaction();
  
  try {
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

module.exports = {
  pool,
  query,
  transaction,
  testConnection
};
