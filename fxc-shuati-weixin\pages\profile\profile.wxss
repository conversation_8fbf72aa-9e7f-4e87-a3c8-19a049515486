/**profile.wxss**/

/* 用户信息卡片 */
.user-card {
  margin: 20rpx;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-radius: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  padding: 0;
  background: none;
  border: none;
  margin-right: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  background: none;
  border: none;
  margin-bottom: 10rpx;
  padding: 0;
}

.user-signature {
  font-size: 24rpx;
  opacity: 0.8;
  line-height: 1.4;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.edit-icon {
  font-size: 24rpx;
}

/* 学习统计 */
.study-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 22rpx;
  opacity: 0.8;
}

/* 菜单部分 */
.menu-section {
  margin: 20rpx;
  padding: 0;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-badge {
  background-color: #ff6b6b;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
  min-width: 32rpx;
  text-align: center;
}

.menu-arrow {
  margin-left: 10rpx;
}

.arrow-text {
  color: #ccc;
  font-size: 24rpx;
}

/* 图表部分 */
.chart-section {
  margin: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.chart-container {
  height: 200rpx;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  height: 150rpx;
  margin-bottom: 20rpx;
  gap: 10rpx;
}

.chart-bar {
  flex: 1;
  background-color: #409EFF;
  border-radius: 4rpx 4rpx 0 0;
  position: relative;
  min-height: 10rpx;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 5rpx;
}

.bar-value {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.chart-labels {
  display: flex;
  gap: 10rpx;
}

.chart-label {
  flex: 1;
  text-align: center;
  font-size: 20rpx;
  color: #666;
}

/* 编辑弹窗 */
.edit-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-popup {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin: 40rpx;
  width: calc(100% - 80rpx);
  max-width: 600rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  line-height: 1.5;
  background-color: #fff;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #409EFF;
  color: white;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .study-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 15rpx;
  }
  
  .user-info {
    flex-direction: column;
    text-align: center;
  }
  
  .user-details {
    margin-top: 20rpx;
  }
  
  .edit-btn {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
  }
}

/* 动画效果 */
.menu-item {
  animation: fadeInUp 0.3s ease-out;
}

.chart-bar {
  animation: growUp 0.8s ease-out;
  animation-fill-mode: both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes growUp {
  from {
    height: 0;
  }
  to {
    height: var(--bar-height, 10rpx);
  }
}
