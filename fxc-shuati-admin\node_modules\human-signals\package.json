{"_from": "human-signals@^1.1.1", "_id": "human-signals@1.1.1", "_inBundle": false, "_integrity": "sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==", "_location": "/human-signals", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "human-signals@^1.1.1", "name": "human-signals", "escapedName": "human-signals", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/default-gateway/execa"], "_resolved": "https://registry.npmmirror.com/human-signals/-/human-signals-1.1.1.tgz", "_shasum": "c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3", "_spec": "human-signals@^1.1.1", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\default-gateway\\node_modules\\execa", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ehmicky"}, "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Human-friendly process signals", "devDependencies": {"@ehmicky/dev-tasks": "^0.30.48", "ajv": "^6.10.2", "ava": "^2.4.0", "fast-deep-equal": "^2.0.1", "gulp": "^4.0.2", "husky": "^3.0.9", "test-each": "^1.7.2"}, "directories": {"lib": "src", "test": "test"}, "engines": {"node": ">=8.12.0"}, "files": ["build/src", "!~"], "homepage": "https://git.io/JeluP", "husky": {"hooks": {"pre-push": "gulp check --full"}}, "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "license": "Apache-2.0", "main": "build/src/main.js", "name": "human-signals", "repository": {"type": "git", "url": "git+https://github.com/ehmicky/human-signals.git"}, "scripts": {"test": "gulp test"}, "version": "1.1.1"}