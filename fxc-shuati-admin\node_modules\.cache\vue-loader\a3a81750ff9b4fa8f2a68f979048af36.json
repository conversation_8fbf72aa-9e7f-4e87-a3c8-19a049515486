{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\list.vue", "mtime": 1752630716965}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcycKaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdRdWVzdGlvbkxpc3QnLAogIGNvbXBvbmVudHM6IHsgUGFnaW5hdGlvbiB9LAogIGRpcmVjdGl2ZXM6IHsgd2F2ZXMgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHF1ZXN0aW9uX3R5cGU6ICcnLAogICAgICAgIGRpZmZpY3VsdHk6ICcnLAogICAgICAgIGNhdGVnb3J5X2lkOiAnJywKICAgICAgICBzdGF0dXM6ICcnCiAgICAgIH0sCiAgICAgIGNhdGVnb3J5T3B0aW9uczogW10sCiAgICAgIHZpZXdEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudFF1ZXN0aW9uOiBudWxsLAogICAgICB0eXBlVGFnTWFwOiB7CiAgICAgICAgc2luZ2xlX2Nob2ljZTogJ3ByaW1hcnknLAogICAgICAgIG11bHRpcGxlX2Nob2ljZTogJ3N1Y2Nlc3MnLAogICAgICAgIHRydWVfZmFsc2U6ICd3YXJuaW5nJywKICAgICAgICBmaWxsX2JsYW5rOiAnaW5mbycsCiAgICAgICAgc2hvcnRfYW5zd2VyOiAnZGFuZ2VyJywKICAgICAgICBlc3NheTogJ2RhbmdlcicKICAgICAgfSwKICAgICAgdHlwZVRleHRNYXA6IHsKICAgICAgICBzaW5nbGVfY2hvaWNlOiAn5Y2V6YCJ6aKYJywKICAgICAgICBtdWx0aXBsZV9jaG9pY2U6ICflpJrpgInpopgnLAogICAgICAgIHRydWVfZmFsc2U6ICfliKTmlq3popgnLAogICAgICAgIGZpbGxfYmxhbms6ICfloavnqbrpopgnLAogICAgICAgIHNob3J0X2Fuc3dlcjogJ+eugOetlOmimCcsCiAgICAgICAgZXNzYXk6ICforrrov7DpopgnCiAgICAgIH0sCiAgICAgIGRpZmZpY3VsdHlUYWdNYXA6IHsKICAgICAgICBlYXN5OiAnc3VjY2VzcycsCiAgICAgICAgbWVkaXVtOiAnd2FybmluZycsCiAgICAgICAgaGFyZDogJ2RhbmdlcicKICAgICAgfSwKICAgICAgZGlmZmljdWx0eVRleHRNYXA6IHsKICAgICAgICBlYXN5OiAn566A5Y2VJywKICAgICAgICBtZWRpdW06ICfkuK3nrYknLAogICAgICAgIGhhcmQ6ICflm7Dpmr4nCiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogICAgdGhpcy5sb2FkQ2F0ZWdvcnlPcHRpb25zKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlCgogICAgICAvLyDmqKHmi5/popjnm67mlbDmja4KICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gWwogICAgICAgICAgLy8g6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGIC0g5ZOy5a2m6YOo5YiGCiAgICAgICAgICB7CiAgICAgICAgICAgIHF1ZXN0aW9uX2lkOiAxLAogICAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn6ams5YWL5oCd5Li75LmJ5ZOy5a2m55qE5Z+65pys6Zeu6aKY5piv77yI77yJJywKICAgICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgICBkaWZmaWN1bHR5OiAnbWVkaXVtJywKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDExMTEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfnianotKjmpoLlv7UnLAogICAgICAgICAgICB0YWdzOiBbCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDEsIHRhZ19uYW1lOiAn6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGJyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA5LCB0YWdfbmFtZTogJ+WTsuWtpuWOn+eQhicgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogNywgdGFnX25hbWU6ICfpq5jpopHogIPngrknIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsgY29udGVudDogJ+eJqei0qOWSjOaEj+ivhueahOWFs+ezu+mXrumimCcgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICfnkIborrrlkozlrp7ot7XnmoTlhbPns7vpl67popgnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5Liq5Lq65ZKM56S+5Lya55qE5YWz57O76Zeu6aKYJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+iHqueUseWSjOW/heeEtueahOWFs+ezu+mXrumimCcgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBjb3JyZWN0X2Fuc3dlcjogJ0EnLAogICAgICAgICAgICBleHBsYW5hdGlvbjogJ+mprOWFi+aAneS4u+S5ieWTsuWtpueahOWfuuacrOmXrumimOaYr+eJqei0qOWSjOaEj+ivhueahOWFs+ezu+mXrumimO+8jOi/meaYr+WTsuWtpueahOagueacrOmXrumimOOAguWug+WMheaLrOS4pOS4quaWuemdou+8muesrOS4gO+8jOeJqei0qOWSjOaEj+ivhuS9leiAheS4uuesrOS4gOaAp++8m+esrOS6jO+8jOeJqei0qOWSjOaEj+ivhuaYr+WQpuWFt+acieWQjOS4gOaAp+OAgicsCiAgICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNScKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHF1ZXN0aW9uX2lkOiAyLAogICAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn54mp6LSo55qE5ZSv5LiA54m55oCn5piv77yI77yJJywKICAgICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgICBkaWZmaWN1bHR5OiAnZWFzeScsCiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTExLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn54mp6LSo5qaC5b+1JywKICAgICAgICAgICAgdGFnczogWwogICAgICAgICAgICAgIHsgdGFnX2lkOiAxLCB0YWdfbmFtZTogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhicgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogOSwgdGFnX25hbWU6ICflk7Llrabljp/nkIYnIH0sCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDE2LCB0YWdfbmFtZTogJzIwMjPlubTnnJ/popgnIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsgY29udGVudDogJ+i/kOWKqOaApycgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICflrqLop4Llrp7lnKjmgKcnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5Y+v55+l5oCnJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+e7neWvueaApycgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBjb3JyZWN0X2Fuc3dlcjogJ0InLAogICAgICAgICAgICBleHBsYW5hdGlvbjogJ+eJqei0qOeahOWUr+S4gOeJueaAp+aYr+WuouinguWunuWcqOaAp+OAgui/meaYr+eJqei0qOamguW/teeahOaguOW/g++8jOaMh+eJqei0qOS4jeS+nei1luS6juS6uueahOaEj+ivhuiAjOWtmOWcqO+8jOW5tuiDveS4uuS6uueahOaEj+ivhuaJgOWPjeaYoOOAgicsCiAgICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNScKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHF1ZXN0aW9uX2lkOiAzLAogICAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn5oSP6K+G55qE5pys6LSo5piv77yI77yJJywKICAgICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ211bHRpcGxlX2Nob2ljZScsCiAgICAgICAgICAgIGRpZmZpY3VsdHk6ICdtZWRpdW0nLAogICAgICAgICAgICBjYXRlZ29yeV9pZDogMTExMiwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+aEj+ivhuacrOi0qCcsCiAgICAgICAgICAgIHRhZ3M6IFsKICAgICAgICAgICAgICB7IHRhZ19pZDogMSwgdGFnX25hbWU6ICfpqazlhYvmgJ3kuLvkuYnln7rmnKzljp/nkIYnIH0sCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDksIHRhZ19uYW1lOiAn5ZOy5a2m5Y6f55CGJyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA3LCB0YWdfbmFtZTogJ+mrmOmikeiAg+eCuScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5oSP6K+G5piv5Lq66ISR55qE5py66IO9JyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+aEj+ivhuaYr+WuouinguWtmOWcqOeahOWPjeaYoCcgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICfmhI/or4bmmK/npL7kvJrnmoTkuqfniaknIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5oSP6K+G5YW35pyJ5Li76KeC6IO95Yqo5oCnJyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQSxCLEMsRCcsCiAgICAgICAgICAgIGV4cGxhbmF0aW9uOiAn5oSP6K+G55qE5pys6LSo5YyF5ous77ya77yIMe+8ieaEj+ivhuaYr+S6uuiEkeeahOacuuiDve+8m++8iDLvvInmhI/or4bmmK/lrqLop4LlrZjlnKjnmoTlj43mmKDvvJvvvIgz77yJ5oSP6K+G5piv56S+5Lya55qE5Lqn54mp77yb77yINO+8ieaEj+ivhuWFt+acieS4u+inguiDveWKqOaAp+OAgui/meWbm+S4quaWuemdouaehOaIkOS6huaEj+ivhuacrOi0qOeahOWujOaVtOWGheWuueOAgicsCiAgICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNScKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHF1ZXN0aW9uX2lkOiA0LAogICAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn6ams5YWL5oCd5Li75LmJ5ZOy5a2m6K6k5Li677yM5LiW55WM55qE55yf5q2j57uf5LiA5oCn5Zyo5LqO5a6D55qE54mp6LSo5oCn44CCJywKICAgICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3RydWVfZmFsc2UnLAogICAgICAgICAgICBkaWZmaWN1bHR5OiAnZWFzeScsCiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTExLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn54mp6LSo5qaC5b+1JywKICAgICAgICAgICAgdGFnczogWwogICAgICAgICAgICAgIHsgdGFnX2lkOiAxLCB0YWdfbmFtZTogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhicgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogOSwgdGFnX25hbWU6ICflk7Llrabljp/nkIYnIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAndHJ1ZScsCiAgICAgICAgICAgIGV4cGxhbmF0aW9uOiAn5q2j56Gu44CC6ams5YWL5oCd5Li75LmJ5ZOy5a2m6K6k5Li677yM5LiW55WM55qE55yf5q2j57uf5LiA5oCn5Zyo5LqO5a6D55qE54mp6LSo5oCn44CC6L+Z5piv6ams5YWL5oCd5Li75LmJ5LiA5YWD6K6655qE5Z+65pys6KeC54K577yM5by66LCD5LiW55WM5LiH54mp6YO95piv54mp6LSo55qE5LiN5ZCM6KGo546w5b2i5byP44CCJywKICAgICAgICAgICAgc2NvcmU6IDIsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1JwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcXVlc3Rpb25faWQ6IDUsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfogZTns7vnmoTnibnngrnljIXmi6zvvIjvvIknLAogICAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnbXVsdGlwbGVfY2hvaWNlJywKICAgICAgICAgICAgZGlmZmljdWx0eTogJ21lZGl1bScsCiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTIxLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn6IGU57O76KeCJywKICAgICAgICAgICAgdGFnczogWwogICAgICAgICAgICAgIHsgdGFnX2lkOiAxLCB0YWdfbmFtZTogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhicgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogOSwgdGFnX25hbWU6ICflk7Llrabljp/nkIYnIH0sCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDcsIHRhZ19uYW1lOiAn6auY6aKR6ICD54K5JyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICflrqLop4LmgKcnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5pmu6YGN5oCnJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+Wkmuagt+aApycgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICfmnaHku7bmgKcnIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgY29ycmVjdF9hbnN3ZXI6ICdBLEIsQyxEJywKICAgICAgICAgICAgZXhwbGFuYXRpb246ICfogZTns7vlhbfmnInlrqLop4LmgKfjgIHmma7pgY3mgKfjgIHlpJrmoLfmgKflkozmnaHku7bmgKfnrYnnibnngrnjgILlrqLop4LmgKfmjIfogZTns7vmmK/kuovnianmnKzouqvmiYDlm7rmnInnmoTvvJvmma7pgY3mgKfmjIfku7vkvZXkuovnianpg73lpITlnKjogZTns7vkuYvkuK3vvJvlpJrmoLfmgKfmjIfogZTns7vnmoTlvaLlvI/lpJrnp43lpJrmoLfvvJvmnaHku7bmgKfmjIfogZTns7vmmK/mnInmnaHku7bnmoTjgIInLAogICAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTUnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBxdWVzdGlvbl9pZDogNiwKICAgICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+efm+ebvueahOWfuuacrOWxnuaAp+aYr19fX19f5ZKMX19fX1/jgIInLAogICAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnZmlsbF9ibGFuaycsCiAgICAgICAgICAgIGRpZmZpY3VsdHk6ICdlYXN5JywKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDExMjMsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfnn5vnm77op4TlvosnLAogICAgICAgICAgICB0YWdzOiBbCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDEsIHRhZ19uYW1lOiAn6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGJyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA5LCB0YWdfbmFtZTogJ+WTsuWtpuWOn+eQhicgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogNywgdGFnX25hbWU6ICfpq5jpopHogIPngrknIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAn5ZCM5LiA5oCn77yb5paX5LqJ5oCnJywKICAgICAgICAgICAgZXhwbGFuYXRpb246ICfnn5vnm77nmoTln7rmnKzlsZ7mgKfmmK/lkIzkuIDmgKflkozmlpfkuonmgKfjgILlkIzkuIDmgKfmmK/mjIfnn5vnm77lj4zmlrnnm7jkupLkvp3lrZjjgIHnm7jkupLotK/pgJrnmoTmgKfotKjlkozotovlir/vvJvmlpfkuonmgKfmmK/mjIfnn5vnm77lj4zmlrnnm7jkupLmjpLmlqXjgIHnm7jkupLlr7nnq4vnmoTmgKfotKjlkozotovlir/jgIInLAogICAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTUnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBxdWVzdGlvbl9pZDogNywKICAgICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+Wunui3teaYr+iupOivhueahOWfuuehgO+8jOS4u+imgeihqOeOsOWcqO+8iO+8iScsCiAgICAgICAgICAgIHF1ZXN0aW9uX3R5cGU6ICdtdWx0aXBsZV9jaG9pY2UnLAogICAgICAgICAgICBkaWZmaWN1bHR5OiAnbWVkaXVtJywKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDExMywKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+iupOivhuiuuicsCiAgICAgICAgICAgIHRhZ3M6IFsKICAgICAgICAgICAgICB7IHRhZ19pZDogMSwgdGFnX25hbWU6ICfpqazlhYvmgJ3kuLvkuYnln7rmnKzljp/nkIYnIH0sCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDksIHRhZ19uYW1lOiAn5ZOy5a2m5Y6f55CGJyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA2LCB0YWdfbmFtZTogJ+mHjeeCuemavueCuScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5a6e6Le15piv6K6k6K+G55qE5p2l5rqQJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+Wunui3teaYr+iupOivhuWPkeWxleeahOWKqOWKmycgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICflrp7ot7XmmK/mo4DpqozorqTor4bnnJ/nkIbmgKfnmoTllK/kuIDmoIflh4YnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5a6e6Le15piv6K6k6K+G55qE55uu55qEJyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQSxCLEMsRCcsCiAgICAgICAgICAgIGV4cGxhbmF0aW9uOiAn5a6e6Le15piv6K6k6K+G55qE5Z+656GA77yM5Li76KaB6KGo546w5Zyo5Zub5Liq5pa56Z2i77ya77yIMe+8ieWunui3teaYr+iupOivhueahOadpea6kO+8m++8iDLvvInlrp7ot7XmmK/orqTor4blj5HlsZXnmoTliqjlipvvvJvvvIgz77yJ5a6e6Le15piv5qOA6aqM6K6k6K+G55yf55CG5oCn55qE5ZSv5LiA5qCH5YeG77yb77yINO+8ieWunui3teaYr+iupOivhueahOebrueahOOAgicsCiAgICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNScKICAgICAgICAgIH0sCiAgICAgICAgICAvLyDmr5vms73kuJzmgJ3mg7Ppg6jliIYKICAgICAgICAgIHsKICAgICAgICAgICAgcXVlc3Rpb25faWQ6IDgsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfmr5vms73kuJzmgJ3mg7PlvaLmiJDnmoTml7bku6Pog4zmma/mmK/vvIjvvIknLAogICAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnc2luZ2xlX2Nob2ljZScsCiAgICAgICAgICAgIGRpZmZpY3VsdHk6ICdtZWRpdW0nLAogICAgICAgICAgICBjYXRlZ29yeV9pZDogMjEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfmr5vms73kuJzmgJ3mg7MnLAogICAgICAgICAgICB0YWdzOiBbCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDIsIHRhZ19uYW1lOiAn5q+b5rO95Lic5oCd5oOzJyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA3LCB0YWdfbmFtZTogJ+mrmOmikeiAg+eCuScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5bid5Zu95Li75LmJ5ZKM5peg5Lqn6Zi257qn6Z2p5ZG955qE5pe25LujJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+i1hOacrOS4u+S5ieWQkeekvuS8muS4u+S5iei/h+a4oeeahOaXtuS7oycgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICflkozlubPkuI7lj5HlsZXnmoTml7bku6MnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5YWo55CD5YyW55qE5pe25LujJyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQScsCiAgICAgICAgICAgIGV4cGxhbmF0aW9uOiAn5q+b5rO95Lic5oCd5oOz5b2i5oiQ55qE5pe25Luj6IOM5pmv5piv5bid5Zu95Li75LmJ5ZKM5peg5Lqn6Zi257qn6Z2p5ZG955qE5pe25Luj44CC6L+Z5LiA5pe25Luj54m55b6B5Li65q+b5rO95Lic5oCd5oOz55qE5b2i5oiQ5ZKM5Y+R5bGV5o+Q5L6b5LqG6YeN6KaB55qE5Y6G5Y+y5p2h5Lu244CCJywKICAgICAgICAgICAgc2NvcmU6IDIsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE2JwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcXVlc3Rpb25faWQ6IDksCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfmlrDmsJHkuLvkuLvkuYnpnanlkb3nmoTmgLvot6/nur/mmK/vvIjvvIknLAogICAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnc2luZ2xlX2Nob2ljZScsCiAgICAgICAgICAgIGRpZmZpY3VsdHk6ICdoYXJkJywKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDIxLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5q+b5rO95Lic5oCd5oOzJywKICAgICAgICAgICAgdGFnczogWwogICAgICAgICAgICAgIHsgdGFnX2lkOiAyLCB0YWdfbmFtZTogJ+avm+azveS4nOaAneaDsycgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogMTIsIHRhZ19uYW1lOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG9JyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA2LCB0YWdfbmFtZTogJ+mHjeeCuemavueCuScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5peg5Lqn6Zi257qn6aKG5a+855qE77yM5Lq65rCR5aSn5LyX55qE77yM5Y+N5a+55bid5Zu95Li75LmJ44CB5bCB5bu65Li75LmJ5ZKM5a6Y5YOa6LWE5pys5Li75LmJ55qE6Z2p5ZG9JyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+W3peS6uumYtue6p+mihuWvvOeahO+8jOS7peW3peWGnOiBlOebn+S4uuWfuuehgOeahOS6uuawkeawkeS4u+S4k+aUvycgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICfkuK3lm73lhbHkuqflhZrpooblr7znmoTlpJrlhZrlkIjkvZzlkozmlL/msrvljY/llYbliLbluqYnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5Lq65rCR5b2T5a625L2c5Li755qE56S+5Lya5Li75LmJ5rCR5Li75pS/5rK7JyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQScsCiAgICAgICAgICAgIGV4cGxhbmF0aW9uOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG955qE5oC76Lev57q/5piv77ya5peg5Lqn6Zi257qn6aKG5a+855qE77yM5Lq65rCR5aSn5LyX55qE77yM5Y+N5a+55bid5Zu95Li75LmJ44CB5bCB5bu65Li75LmJ5ZKM5a6Y5YOa6LWE5pys5Li75LmJ55qE6Z2p5ZG944CC6L+Z5piv5q+b5rO95Lic5a+55paw5rCR5Li75Li75LmJ6Z2p5ZG95pys6LSo55qE56eR5a2m5qaC5ous44CCJywKICAgICAgICAgICAgc2NvcmU6IDIsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE2JwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcXVlc3Rpb25faWQ6IDEwLAogICAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn5Lit5Zu96Z2p5ZG955qE5Z+65pys6Zeu6aKY5piv77yI77yJJywKICAgICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgICBkaWZmaWN1bHR5OiAnbWVkaXVtJywKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDIxLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5q+b5rO95Lic5oCd5oOzJywKICAgICAgICAgICAgdGFnczogWwogICAgICAgICAgICAgIHsgdGFnX2lkOiAyLCB0YWdfbmFtZTogJ+avm+azveS4nOaAneaDsycgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogMTIsIHRhZ19uYW1lOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG9JyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiAxNywgdGFnX25hbWU6ICcyMDIy5bm055yf6aKYJyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICflhpzmsJHpl67popgnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5q2m6KOF5paX5LqJ6Zeu6aKYJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+e7n+S4gOaImOe6v+mXrumimCcgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICflhZrnmoTlu7rorr7pl67popgnIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgY29ycmVjdF9hbnN3ZXI6ICdBJywKICAgICAgICAgICAgZXhwbGFuYXRpb246ICfkuK3lm73pnanlkb3nmoTln7rmnKzpl67popjmmK/lhpzmsJHpl67popjjgILmr5vms73kuJzmjIflh7rvvIzlhpzmsJHpl67popjkuYPlm73msJHpnanlkb3nmoTkuK3lv4Ppl67popjvvIzlhpzmsJHmmK/kuK3lm73pnanlkb3nmoTkuLvlipvlhpvvvIzlhpzmsJHpl67popjmmK/kuK3lm73pnanlkb3nmoTln7rmnKzpl67popjjgIInLAogICAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTYnCiAgICAgICAgICB9LAogICAgICAgICAgLy8g5Lit5Zu96L+R546w5Luj5Y+y57qy6KaB6YOo5YiGCiAgICAgICAgICB7CiAgICAgICAgICAgIHF1ZXN0aW9uX2lkOiAxMSwKICAgICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+S4reWbvei/keS7o+WPsueahOi1t+eCueaYr++8iO+8iScsCiAgICAgICAgICAgIHF1ZXN0aW9uX3R5cGU6ICdzaW5nbGVfY2hvaWNlJywKICAgICAgICAgICAgZGlmZmljdWx0eTogJ2Vhc3knLAogICAgICAgICAgICBjYXRlZ29yeV9pZDogMzEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfml6fmsJHkuLvkuLvkuYnpnanlkb3ml7bmnJ8nLAogICAgICAgICAgICB0YWdzOiBbCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDMsIHRhZ19uYW1lOiAn5Lit5Zu96L+R546w5Luj5Y+y57qy6KaBJyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA3LCB0YWdfbmFtZTogJ+mrmOmikeiAg+eCuScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBjb250ZW50OiAnMTg0MOW5tOm4pueJh+aImOS6iScgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICcxODQy5bm044CK5Y2X5Lqs5p2h57qm44CL562+6K6iJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJzE4NTHlubTlpKrlubPlpKnlm73ov5DliqgnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAnMTg5NOW5tOeUsuWNiOS4reaXpeaImOS6iScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBjb3JyZWN0X2Fuc3dlcjogJ0EnLAogICAgICAgICAgICBleHBsYW5hdGlvbjogJ+S4reWbvei/keS7o+WPsueahOi1t+eCueaYrzE4NDDlubTpuKbniYfmiJjkuonjgILpuKbniYfmiJjkuonmmK/kuK3lm73ljoblj7LnmoTovazmipjngrnvvIzmoIflv5fnnYDkuK3lm73lvIDlp4vmsqbkuLrljYrmrpbmsJHlnLDljYrlsIHlu7rnpL7kvJrvvIzkuK3lm73ov5Hku6Plj7LnlLHmraTlvIDlp4vjgIInLAogICAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTcnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBxdWVzdGlvbl9pZDogMTIsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfkupTlm5vov5DliqjnmoTljoblj7LmhI/kuYnljIXmi6zvvIjvvIknLAogICAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnbXVsdGlwbGVfY2hvaWNlJywKICAgICAgICAgICAgZGlmZmljdWx0eTogJ21lZGl1bScsCiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAzMiwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+aWsOawkeS4u+S4u+S5iemdqeWRveaXtuacnycsCiAgICAgICAgICAgIHRhZ3M6IFsKICAgICAgICAgICAgICB7IHRhZ19pZDogMywgdGFnX25hbWU6ICfkuK3lm73ov5HnjrDku6Plj7LnurLopoEnIH0sCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDEyLCB0YWdfbmFtZTogJ+aWsOawkeS4u+S4u+S5iemdqeWRvScgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogNywgdGFnX25hbWU6ICfpq5jpopHogIPngrknIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsgY29udGVudDogJ+aYr+S4reWbveaWsOawkeS4u+S4u+S5iemdqeWRveeahOW8gOerrycgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICfkv4Pov5vkuobpqazlhYvmgJ3kuLvkuYnlnKjkuK3lm73nmoTkvKDmkq0nIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5L+D6L+b5LqG6ams5YWL5oCd5Li75LmJ5LiO5Lit5Zu95bel5Lq66L+Q5Yqo55qE57uT5ZCIJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+S4uuS4reWbveWFseS6p+WFmueahOaIkOeri+S9nOS6huaAneaDs+WSjOW5sumDqOWHhuWkhycgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBjb3JyZWN0X2Fuc3dlcjogJ0EsQixDLEQnLAogICAgICAgICAgICBleHBsYW5hdGlvbjogJ+S6lOWbm+i/kOWKqOeahOWOhuWPsuaEj+S5iemHjeWkp++8mu+8iDHvvInmmK/kuK3lm73mlrDmsJHkuLvkuLvkuYnpnanlkb3nmoTlvIDnq6/vvJvvvIgy77yJ5L+D6L+b5LqG6ams5YWL5oCd5Li75LmJ5Zyo5Lit5Zu955qE5Lyg5pKt77yb77yIM++8ieS/g+i/m+S6humprOWFi+aAneS4u+S5ieS4juS4reWbveW3peS6uui/kOWKqOeahOe7k+WQiO+8m++8iDTvvInkuLrkuK3lm73lhbHkuqflhZrnmoTmiJDnq4vkvZzkuobmgJ3mg7PlkozlubLpg6jlh4blpIfjgIInLAogICAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTcnCiAgICAgICAgICB9LAogICAgICAgICAgLy8g5oCd5oOz6YGT5b635LiO5rOV5rK76YOo5YiGCiAgICAgICAgICB7CiAgICAgICAgICAgIHF1ZXN0aW9uX2lkOiAxMywKICAgICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+eQhuaDs+S/oeW/teeahOS9nOeUqOS4u+imgeihqOeOsOWcqO+8iO+8iScsCiAgICAgICAgICAgIHF1ZXN0aW9uX3R5cGU6ICdtdWx0aXBsZV9jaG9pY2UnLAogICAgICAgICAgICBkaWZmaWN1bHR5OiAnbWVkaXVtJywKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDQsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfmgJ3mg7PpgZPlvrfkuI7ms5XmsrsnLAogICAgICAgICAgICB0YWdzOiBbCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDQsIHRhZ19uYW1lOiAn5oCd5oOz6YGT5b635LiO5rOV5rK7JyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA3LCB0YWdfbmFtZTogJ+mrmOmikeiAg+eCuScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5oyH5byV5Lq655Sf55qE5aWL5paX55uu5qCHJyB9LAogICAgICAgICAgICAgIHsgY29udGVudDogJ+aPkOS+m+S6uueUn+eahOWJjei/m+WKqOWKmycgfSwKICAgICAgICAgICAgICB7IGNvbnRlbnQ6ICfmj5Dpq5jkurrnlJ/nmoTnsr7npZ7looPnlYwnIH0sCiAgICAgICAgICAgICAgeyBjb250ZW50OiAn5aKe5by65Lq655Sf55qE5L2/5ZG95oSfJyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQSxCLEMnLAogICAgICAgICAgICBleHBsYW5hdGlvbjogJ+eQhuaDs+S/oeW/teeahOS9nOeUqOS4u+imgeihqOeOsOWcqOS4ieS4quaWuemdou+8mu+8iDHvvInmjIflvJXkurrnlJ/nmoTlpYvmlpfnm67moIfvvJvvvIgy77yJ5o+Q5L6b5Lq655Sf55qE5YmN6L+b5Yqo5Yqb77yb77yIM++8ieaPkOmrmOS6uueUn+eahOeyvuelnuWig+eVjOOAgueQhuaDs+S/oeW/teaYr+S6uueUn+eahOeyvuelnuaUr+afseWSjOWJjei/m+WKqOWKm+OAgicsCiAgICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xOCcKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHF1ZXN0aW9uX2lkOiAxNCwKICAgICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+ekvuS8muS4u+S5ieaguOW/g+S7t+WAvOingueahOWfuuacrOWGheWuueaYr19fX19f44CBX19fX1/jgIFfX19fX+OAgicsCiAgICAgICAgICAgIHF1ZXN0aW9uX3R5cGU6ICdmaWxsX2JsYW5rJywKICAgICAgICAgICAgZGlmZmljdWx0eTogJ2Vhc3knLAogICAgICAgICAgICBjYXRlZ29yeV9pZDogNCwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+aAneaDs+mBk+W+t+S4juazleayuycsCiAgICAgICAgICAgIHRhZ3M6IFsKICAgICAgICAgICAgICB7IHRhZ19pZDogNCwgdGFnX25hbWU6ICfmgJ3mg7PpgZPlvrfkuI7ms5XmsrsnIH0sCiAgICAgICAgICAgICAgeyB0YWdfaWQ6IDE1LCB0YWdfbmFtZTogJ+aWsOaXtuS7oycgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogNywgdGFnX25hbWU6ICfpq5jpopHogIPngrknIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAn5a+M5by644CB5rCR5Li744CB5paH5piO44CB5ZKM6LCQ77yb6Ieq55Sx44CB5bmz562J44CB5YWs5q2j44CB5rOV5rK777yb54ix5Zu944CB5pWs5Lia44CB6K+a5L+h44CB5Y+L5ZaEJywKICAgICAgICAgICAgZXhwbGFuYXRpb246ICfnpL7kvJrkuLvkuYnmoLjlv4Pku7flgLzop4LnmoTln7rmnKzlhoXlrrnmmK/vvJrlm73lrrblsYLpnaLnmoTku7flgLzopoHmsYLmmK/lr4zlvLrjgIHmsJHkuLvjgIHmlofmmI7jgIHlkozosJDvvJvnpL7kvJrlsYLpnaLnmoTku7flgLzopoHmsYLmmK/oh6rnlLHjgIHlubPnrYnjgIHlhazmraPjgIHms5XmsrvvvJvkuKrkurrlsYLpnaLnmoTku7flgLzopoHmsYLmmK/niLHlm73jgIHmlazkuJrjgIHor5rkv6HjgIHlj4vlloTjgIInLAogICAgICAgICAgICBzY29yZTogMywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTgnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBxdWVzdGlvbl9pZDogMTUsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfor7forrrov7DmlrDml7bku6PniLHlm73kuLvkuYnnmoTln7rmnKzopoHmsYLjgIInLAogICAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnZXNzYXknLAogICAgICAgICAgICBkaWZmaWN1bHR5OiAnaGFyZCcsCiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiA0LAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5oCd5oOz6YGT5b635LiO5rOV5rK7JywKICAgICAgICAgICAgdGFnczogWwogICAgICAgICAgICAgIHsgdGFnX2lkOiA0LCB0YWdfbmFtZTogJ+aAneaDs+mBk+W+t+S4juazleayuycgfSwKICAgICAgICAgICAgICB7IHRhZ19pZDogMTUsIHRhZ19uYW1lOiAn5paw5pe25LujJyB9LAogICAgICAgICAgICAgIHsgdGFnX2lkOiA2LCB0YWdfbmFtZTogJ+mHjeeCuemavueCuScgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgICAgY29ycmVjdF9hbnN3ZXI6ICfmlrDml7bku6PniLHlm73kuLvkuYnnmoTln7rmnKzopoHmsYLljIXmi6zvvJrvvIgx77yJ5Z2a5oyB54ix5Zu95ZKM54ix5YWa44CB54ix56S+5Lya5Li75LmJ55u457uf5LiA77yb77yIMu+8iee7tOaKpOelluWbvee7n+S4gOWSjOawkeaXj+Wboue7k++8m++8iDPvvInlsIrph43lkozkvKDmib/kuK3ljY7msJHml4/ljoblj7LlkozmlofljJbvvJvvvIg077yJ5Z2a5oyB56uL6Laz5rCR5peP5Y+I6Z2i5ZCR5LiW55WM44CC5paw5pe25Luj55qE54ix5Zu95Li75LmJ5b+F6aG75Z2a5oyB54ix5Zu95ZKM54ix5YWa44CB54ix56S+5Lya5Li75LmJ55u457uf5LiA77yM6L+Z5piv5b2T5Luj5Lit5Zu954ix5Zu95Li75LmJ57K+56We5pyA6YeN6KaB55qE5L2T546w44CCJywKICAgICAgICAgICAgZXhwbGFuYXRpb246ICfov5npgZPpopjogIPmn6XmlrDml7bku6PniLHlm73kuLvkuYnnmoTln7rmnKzopoHmsYLjgILnrZTpopjopoHngrnvvJrvvIgx77yJ54ix5Zu95ZKM54ix5YWa44CB54ix56S+5Lya5Li75LmJ55u457uf5LiA77yb77yIMu+8iee7tOaKpOelluWbvee7n+S4gOWSjOawkeaXj+Wboue7k++8m++8iDPvvInlsIrph43lkozkvKDmib/kuK3ljY7msJHml4/ljoblj7LlkozmlofljJbvvJvvvIg077yJ56uL6Laz5rCR5peP5Y+I6Z2i5ZCR5LiW55WM44CC6KaB57uT5ZCI5paw5pe25Luj54m554K56L+b6KGM6K666L+w44CCJywKICAgICAgICAgICAgc2NvcmU6IDEwLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xOCcKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMubGlzdC5sZW5ndGgKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2UKICAgICAgfSwgNTAwKQogICAgfSwKCiAgICBsb2FkQ2F0ZWdvcnlPcHRpb25zKCkgewogICAgICAvLyDmqKHmi5/liIbnsbvpgInpobnvvIjmiYHlubPljJbvvIkKICAgICAgdGhpcy5jYXRlZ29yeU9wdGlvbnMgPSBbCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMTExLCBjYXRlZ29yeV9uYW1lOiAn5ZSv54mp6K66JyB9LAogICAgICAgIHsgY2F0ZWdvcnlfaWQ6IDExMiwgY2F0ZWdvcnlfbmFtZTogJ+i+qeivgeazlScgfSwKICAgICAgICB7IGNhdGVnb3J5X2lkOiAxMTMsIGNhdGVnb3J5X25hbWU6ICforqTor4borronIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMTIsIGNhdGVnb3J5X25hbWU6ICfpqazlhYvmgJ3kuLvkuYnmlL/msrvnu4/mtY7lraYnIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMTMsIGNhdGVnb3J5X25hbWU6ICfnp5HlrabnpL7kvJrkuLvkuYknIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMjEsIGNhdGVnb3J5X25hbWU6ICfmr5vms73kuJzmgJ3mg7MnIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMjIsIGNhdGVnb3J5X25hbWU6ICfpgpPlsI/lubPnkIborronIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMjMsIGNhdGVnb3J5X25hbWU6ICfkuInkuKrku6Pooajph43opoHmgJ3mg7MnIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMzEsIGNhdGVnb3J5X25hbWU6ICfml6fmsJHkuLvkuLvkuYnpnanlkb3ml7bmnJ8nIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMzIsIGNhdGVnb3J5X25hbWU6ICfmlrDmsJHkuLvkuLvkuYnpnanlkb3ml7bmnJ8nIH0sCiAgICAgICAgeyBjYXRlZ29yeV9pZDogMzMsIGNhdGVnb3J5X25hbWU6ICfnpL7kvJrkuLvkuYnpnanlkb3lkozlu7rorr7ml7bmnJ8nIH0KICAgICAgXQogICAgfSwKCiAgICBoYW5kbGVGaWx0ZXIoKSB7CiAgICAgIHRoaXMubGlzdFF1ZXJ5LnBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAoKICAgIGhhbmRsZUNyZWF0ZSgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9xdWVzdGlvbnMvYWRkJykKICAgIH0sCgogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3F1ZXN0aW9ucy91cGxvYWQnKQogICAgfSwKCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKGAvcXVlc3Rpb25zL2VkaXQvJHtyb3cucXVlc3Rpb25faWR9YCkKICAgIH0sCgogICAgaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb24gPSByb3cKICAgICAgdGhpcy52aWV3RGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCgogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTor6Xpopjnm67lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICB9KQogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6NA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索题目内容\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.question_type\"\n          placeholder=\"题目类型\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"单选题\" value=\"single_choice\" />\n          <el-option label=\"多选题\" value=\"multiple_choice\" />\n          <el-option label=\"判断题\" value=\"true_false\" />\n          <el-option label=\"填空题\" value=\"fill_blank\" />\n          <el-option label=\"简答题\" value=\"short_answer\" />\n          <el-option label=\"论述题\" value=\"essay\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.difficulty\"\n          placeholder=\"难度等级\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"简单\" value=\"easy\" />\n          <el-option label=\"中等\" value=\"medium\" />\n          <el-option label=\"困难\" value=\"hard\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.category_id\"\n          placeholder=\"题目分类\"\n          clearable\n          style=\"width: 150px\"\n          class=\"filter-item\"\n        >\n          <el-option\n            v-for=\"category in categoryOptions\"\n            :key=\"category.category_id\"\n            :label=\"category.category_name\"\n            :value=\"category.category_id\"\n          />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加题目\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          type=\"success\"\n          icon=\"el-icon-upload\"\n          @click=\"handleImport\"\n        >\n          批量导入\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"question_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"题目内容\" prop=\"question_content\" min-width=\"300\">\n        <template slot-scope=\"{row}\">\n          <div class=\"question-content\">\n            {{ row.question_content.length > 100 ? row.question_content.substring(0, 100) + '...' : row.question_content }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"题目类型\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"typeTagMap[row.question_type]\">\n            {{ typeTextMap[row.question_type] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"难度\" width=\"80\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"difficultyTagMap[row.difficulty]\" size=\"mini\">\n            {{ difficultyTextMap[row.difficulty] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"分类\" prop=\"category_name\" min-width=\"120\" />\n      <el-table-column label=\"标签\" min-width=\"150\">\n        <template slot-scope=\"{row}\">\n          <el-tag\n            v-for=\"tag in row.tags\"\n            :key=\"tag.tag_id\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n          >\n            {{ tag.tag_name }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"分值\" prop=\"score\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ formatDate(row.created_at) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button type=\"info\" size=\"mini\" @click=\"handleView(row)\">\n            查看\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 查看题目对话框 -->\n    <el-dialog title=\"题目详情\" :visible.sync=\"viewDialogVisible\" width=\"60%\">\n      <div v-if=\"currentQuestion\" class=\"question-detail\">\n        <div class=\"detail-item\">\n          <label>题目类型：</label>\n          <el-tag :type=\"typeTagMap[currentQuestion.question_type]\">\n            {{ typeTextMap[currentQuestion.question_type] }}\n          </el-tag>\n        </div>\n        <div class=\"detail-item\">\n          <label>难度等级：</label>\n          <el-tag :type=\"difficultyTagMap[currentQuestion.difficulty]\">\n            {{ difficultyTextMap[currentQuestion.difficulty] }}\n          </el-tag>\n        </div>\n        <div class=\"detail-item\">\n          <label>题目内容：</label>\n          <div class=\"content\">{{ currentQuestion.question_content }}</div>\n        </div>\n        <div v-if=\"currentQuestion.options && currentQuestion.options.length > 0\" class=\"detail-item\">\n          <label>选项：</label>\n          <div class=\"options\">\n            <div\n              v-for=\"(option, index) in currentQuestion.options\"\n              :key=\"index\"\n              class=\"option\"\n            >\n              {{ String.fromCharCode(65 + index) }}. {{ option.content }}\n            </div>\n          </div>\n        </div>\n        <div class=\"detail-item\">\n          <label>正确答案：</label>\n          <div class=\"answer\">{{ currentQuestion.correct_answer }}</div>\n        </div>\n        <div v-if=\"currentQuestion.explanation\" class=\"detail-item\">\n          <label>题目解析：</label>\n          <div class=\"explanation\">{{ currentQuestion.explanation }}</div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionList',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        question_type: '',\n        difficulty: '',\n        category_id: '',\n        status: ''\n      },\n      categoryOptions: [],\n      viewDialogVisible: false,\n      currentQuestion: null,\n      typeTagMap: {\n        single_choice: 'primary',\n        multiple_choice: 'success',\n        true_false: 'warning',\n        fill_blank: 'info',\n        short_answer: 'danger',\n        essay: 'danger'\n      },\n      typeTextMap: {\n        single_choice: '单选题',\n        multiple_choice: '多选题',\n        true_false: '判断题',\n        fill_blank: '填空题',\n        short_answer: '简答题',\n        essay: '论述题'\n      },\n      difficultyTagMap: {\n        easy: 'success',\n        medium: 'warning',\n        hard: 'danger'\n      },\n      difficultyTextMap: {\n        easy: '简单',\n        medium: '中等',\n        hard: '困难'\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.loadCategoryOptions()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n\n      // 模拟题目数据\n      setTimeout(() => {\n        this.list = [\n          // 马克思主义基本原理 - 哲学部分\n          {\n            question_id: 1,\n            question_content: '马克思主义哲学的基本问题是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '物质和意识的关系问题' },\n              { content: '理论和实践的关系问题' },\n              { content: '个人和社会的关系问题' },\n              { content: '自由和必然的关系问题' }\n            ],\n            correct_answer: 'A',\n            explanation: '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。它包括两个方面：第一，物质和意识何者为第一性；第二，物质和意识是否具有同一性。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 2,\n            question_content: '物质的唯一特性是（）',\n            question_type: 'single_choice',\n            difficulty: 'easy',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 16, tag_name: '2023年真题' }\n            ],\n            options: [\n              { content: '运动性' },\n              { content: '客观实在性' },\n              { content: '可知性' },\n              { content: '绝对性' }\n            ],\n            correct_answer: 'B',\n            explanation: '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在，并能为人的意识所反映。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 3,\n            question_content: '意识的本质是（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 1112,\n            category_name: '意识本质',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '意识是人脑的机能' },\n              { content: '意识是客观存在的反映' },\n              { content: '意识是社会的产物' },\n              { content: '意识具有主观能动性' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '意识的本质包括：（1）意识是人脑的机能；（2）意识是客观存在的反映；（3）意识是社会的产物；（4）意识具有主观能动性。这四个方面构成了意识本质的完整内容。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 4,\n            question_content: '马克思主义哲学认为，世界的真正统一性在于它的物质性。',\n            question_type: 'true_false',\n            difficulty: 'easy',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' }\n            ],\n            options: [],\n            correct_answer: 'true',\n            explanation: '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。这是马克思主义一元论的基本观点，强调世界万物都是物质的不同表现形式。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 5,\n            question_content: '联系的特点包括（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 1121,\n            category_name: '联系观',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '客观性' },\n              { content: '普遍性' },\n              { content: '多样性' },\n              { content: '条件性' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '联系具有客观性、普遍性、多样性和条件性等特点。客观性指联系是事物本身所固有的；普遍性指任何事物都处在联系之中；多样性指联系的形式多种多样；条件性指联系是有条件的。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 6,\n            question_content: '矛盾的基本属性是_____和_____。',\n            question_type: 'fill_blank',\n            difficulty: 'easy',\n            category_id: 1123,\n            category_name: '矛盾规律',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [],\n            correct_answer: '同一性；斗争性',\n            explanation: '矛盾的基本属性是同一性和斗争性。同一性是指矛盾双方相互依存、相互贯通的性质和趋势；斗争性是指矛盾双方相互排斥、相互对立的性质和趋势。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 7,\n            question_content: '实践是认识的基础，主要表现在（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 113,\n            category_name: '认识论',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [\n              { content: '实践是认识的来源' },\n              { content: '实践是认识发展的动力' },\n              { content: '实践是检验认识真理性的唯一标准' },\n              { content: '实践是认识的目的' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '实践是认识的基础，主要表现在四个方面：（1）实践是认识的来源；（2）实践是认识发展的动力；（3）实践是检验认识真理性的唯一标准；（4）实践是认识的目的。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          // 毛泽东思想部分\n          {\n            question_id: 8,\n            question_content: '毛泽东思想形成的时代背景是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '帝国主义和无产阶级革命的时代' },\n              { content: '资本主义向社会主义过渡的时代' },\n              { content: '和平与发展的时代' },\n              { content: '全球化的时代' }\n            ],\n            correct_answer: 'A',\n            explanation: '毛泽东思想形成的时代背景是帝国主义和无产阶级革命的时代。这一时代特征为毛泽东思想的形成和发展提供了重要的历史条件。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          {\n            question_id: 9,\n            question_content: '新民主主义革命的总路线是（）',\n            question_type: 'single_choice',\n            difficulty: 'hard',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [\n              { content: '无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命' },\n              { content: '工人阶级领导的，以工农联盟为基础的人民民主专政' },\n              { content: '中国共产党领导的多党合作和政治协商制度' },\n              { content: '人民当家作主的社会主义民主政治' }\n            ],\n            correct_answer: 'A',\n            explanation: '新民主主义革命的总路线是：无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命。这是毛泽东对新民主主义革命本质的科学概括。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          {\n            question_id: 10,\n            question_content: '中国革命的基本问题是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 17, tag_name: '2022年真题' }\n            ],\n            options: [\n              { content: '农民问题' },\n              { content: '武装斗争问题' },\n              { content: '统一战线问题' },\n              { content: '党的建设问题' }\n            ],\n            correct_answer: 'A',\n            explanation: '中国革命的基本问题是农民问题。毛泽东指出，农民问题乃国民革命的中心问题，农民是中国革命的主力军，农民问题是中国革命的基本问题。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          // 中国近现代史纲要部分\n          {\n            question_id: 11,\n            question_content: '中国近代史的起点是（）',\n            question_type: 'single_choice',\n            difficulty: 'easy',\n            category_id: 31,\n            category_name: '旧民主主义革命时期',\n            tags: [\n              { tag_id: 3, tag_name: '中国近现代史纲要' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '1840年鸦片战争' },\n              { content: '1842年《南京条约》签订' },\n              { content: '1851年太平天国运动' },\n              { content: '1894年甲午中日战争' }\n            ],\n            correct_answer: 'A',\n            explanation: '中国近代史的起点是1840年鸦片战争。鸦片战争是中国历史的转折点，标志着中国开始沦为半殖民地半封建社会，中国近代史由此开始。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-17'\n          },\n          {\n            question_id: 12,\n            question_content: '五四运动的历史意义包括（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 32,\n            category_name: '新民主主义革命时期',\n            tags: [\n              { tag_id: 3, tag_name: '中国近现代史纲要' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '是中国新民主主义革命的开端' },\n              { content: '促进了马克思主义在中国的传播' },\n              { content: '促进了马克思主义与中国工人运动的结合' },\n              { content: '为中国共产党的成立作了思想和干部准备' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '五四运动的历史意义重大：（1）是中国新民主主义革命的开端；（2）促进了马克思主义在中国的传播；（3）促进了马克思主义与中国工人运动的结合；（4）为中国共产党的成立作了思想和干部准备。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-17'\n          },\n          // 思想道德与法治部分\n          {\n            question_id: 13,\n            question_content: '理想信念的作用主要表现在（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '指引人生的奋斗目标' },\n              { content: '提供人生的前进动力' },\n              { content: '提高人生的精神境界' },\n              { content: '增强人生的使命感' }\n            ],\n            correct_answer: 'A,B,C',\n            explanation: '理想信念的作用主要表现在三个方面：（1）指引人生的奋斗目标；（2）提供人生的前进动力；（3）提高人生的精神境界。理想信念是人生的精神支柱和前进动力。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-18'\n          },\n          {\n            question_id: 14,\n            question_content: '社会主义核心价值观的基本内容是_____、_____、_____。',\n            question_type: 'fill_blank',\n            difficulty: 'easy',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 15, tag_name: '新时代' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [],\n            correct_answer: '富强、民主、文明、和谐；自由、平等、公正、法治；爱国、敬业、诚信、友善',\n            explanation: '社会主义核心价值观的基本内容是：国家层面的价值要求是富强、民主、文明、和谐；社会层面的价值要求是自由、平等、公正、法治；个人层面的价值要求是爱国、敬业、诚信、友善。',\n            score: 3,\n            status: 'active',\n            created_at: '2024-01-18'\n          },\n          {\n            question_id: 15,\n            question_content: '请论述新时代爱国主义的基本要求。',\n            question_type: 'essay',\n            difficulty: 'hard',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 15, tag_name: '新时代' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [],\n            correct_answer: '新时代爱国主义的基本要求包括：（1）坚持爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）坚持立足民族又面向世界。新时代的爱国主义必须坚持爱国和爱党、爱社会主义相统一，这是当代中国爱国主义精神最重要的体现。',\n            explanation: '这道题考查新时代爱国主义的基本要求。答题要点：（1）爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）立足民族又面向世界。要结合新时代特点进行论述。',\n            score: 10,\n            status: 'active',\n            created_at: '2024-01-18'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n      }, 500)\n    },\n\n    loadCategoryOptions() {\n      // 模拟分类选项（扁平化）\n      this.categoryOptions = [\n        { category_id: 111, category_name: '唯物论' },\n        { category_id: 112, category_name: '辩证法' },\n        { category_id: 113, category_name: '认识论' },\n        { category_id: 12, category_name: '马克思主义政治经济学' },\n        { category_id: 13, category_name: '科学社会主义' },\n        { category_id: 21, category_name: '毛泽东思想' },\n        { category_id: 22, category_name: '邓小平理论' },\n        { category_id: 23, category_name: '三个代表重要思想' },\n        { category_id: 31, category_name: '旧民主主义革命时期' },\n        { category_id: 32, category_name: '新民主主义革命时期' },\n        { category_id: 33, category_name: '社会主义革命和建设时期' }\n      ]\n    },\n\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n\n    handleCreate() {\n      this.$router.push('/questions/add')\n    },\n\n    handleImport() {\n      this.$router.push('/questions/upload')\n    },\n\n    handleUpdate(row) {\n      this.$router.push(`/questions/edit/${row.question_id}`)\n    },\n\n    handleView(row) {\n      this.currentQuestion = row\n      this.viewDialogVisible = true\n    },\n\n    handleDelete(row) {\n      this.$confirm('确定要删除该题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n\n    .question-content {\n      line-height: 1.5;\n      word-break: break-word;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n\n.question-detail {\n  .detail-item {\n    margin-bottom: 20px;\n\n    label {\n      font-weight: bold;\n      color: #303133;\n      margin-bottom: 8px;\n      display: block;\n    }\n\n    .content,\n    .answer,\n    .explanation {\n      background: #f5f7fa;\n      padding: 12px;\n      border-radius: 4px;\n      line-height: 1.6;\n    }\n\n    .options {\n      .option {\n        padding: 8px 12px;\n        margin-bottom: 8px;\n        background: #f5f7fa;\n        border-radius: 4px;\n        border-left: 3px solid #409eff;\n      }\n    }\n  }\n}\n</style>\n"]}]}