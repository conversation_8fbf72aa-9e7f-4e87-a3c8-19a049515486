{"_from": "inquirer@^7.1.0", "_id": "inquirer@7.3.3", "_inBundle": false, "_integrity": "sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==", "_location": "/inquirer", "_phantomChildren": {"signal-exit": "3.0.7"}, "_requested": {"type": "range", "registry": true, "raw": "inquirer@^7.1.0", "name": "inquirer", "escapedName": "inquirer", "rawSpec": "^7.1.0", "saveSpec": null, "fetchSpec": "^7.1.0"}, "_requiredBy": ["/@vue/cli-plugin-eslint", "/eslint"], "_resolved": "https://registry.npmmirror.com/inquirer/-/inquirer-7.3.3.tgz", "_shasum": "04d176b2af04afc157a83fd7c100e98ee0aad003", "_spec": "inquirer@^7.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\@vue\\cli-plugin-eslint", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/SBoudrias/Inquirer.js/issues"}, "bundleDependencies": false, "dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.19", "mute-stream": "0.0.8", "run-async": "^2.4.0", "rxjs": "^6.6.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}, "deprecated": false, "description": "A collection of common interactive command line user interfaces.", "devDependencies": {"chai": "^4.2.0", "chalk-pipe": "^4.0.0", "cmdify": "^0.0.4", "mocha": "^8.0.1", "mockery": "^2.1.0", "nyc": "^15.0.0", "sinon": "^9.0.0"}, "engines": {"node": ">=8.0.0"}, "files": ["lib", "README.md"], "gitHead": "808d5538531c1ca1c34f832d77bc98dfd0ba4000", "homepage": "https://github.com/SBoudrias/Inquirer.js#readme", "keywords": ["command", "prompt", "stdin", "cli", "tty", "menu"], "license": "MIT", "main": "lib/inquirer.js", "name": "inquirer", "repository": {"type": "git", "url": "git+https://github.com/SBoudrias/Inquirer.js.git"}, "scripts": {"postpublish": "rm -f README.md", "posttest": "nyc report --reporter=text-lcov > ../../coverage/nyc-report.lcov", "prepublishOnly": "cp ../../README.md .", "test": "nyc mocha test/**/* -r ./test/before"}, "version": "7.3.3"}