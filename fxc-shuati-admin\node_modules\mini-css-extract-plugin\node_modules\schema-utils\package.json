{"_from": "schema-utils@^1.0.0", "_id": "schema-utils@1.0.0", "_inBundle": false, "_integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "_location": "/mini-css-extract-plugin/schema-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "schema-utils@^1.0.0", "name": "schema-utils", "escapedName": "schema-utils", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/mini-css-extract-plugin"], "_resolved": "https://registry.npmmirror.com/schema-utils/-/schema-utils-1.0.0.tgz", "_shasum": "0b79a93204d7b600d4b2850d1f66c2a34951c770", "_spec": "schema-utils@^1.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\mini-css-extract-plugin", "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "bundleDependencies": false, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "deprecated": false, "description": "webpack Validation Utils", "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0", "del-cli": "^1.0.0", "eslint": "^5.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "jest": "^21.0.0", "prettier": "^1.0.0", "standard-version": "^4.0.0"}, "engines": {"node": ">= 4"}, "files": ["src"], "homepage": "https://github.com/webpack-contrib/schema-utils", "license": "MIT", "main": "src/index.js", "name": "schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "scripts": {"clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "lint": "eslint --cache src test", "release": "npm run commits && standard-version", "test": "jest --env node --verbose --coverage"}, "version": "1.0.0"}