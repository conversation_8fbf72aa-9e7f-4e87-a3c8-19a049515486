{"_from": "big.js@^3.1.3", "_id": "big.js@3.2.0", "_inBundle": false, "_integrity": "sha512-+hN/Zh2D08Mx65pZ/4g5bsmNiZUuChDiQfTUQ7qJr4/kuopCr88xZsAXv6mBoZEsUI4OuGHlX59qE94K2mMW8Q==", "_location": "/html-webpack-plugin/big.js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "big.js@^3.1.3", "name": "big.js", "escapedName": "big.js", "rawSpec": "^3.1.3", "saveSpec": null, "fetchSpec": "^3.1.3"}, "_requiredBy": ["/html-webpack-plugin/loader-utils"], "_resolved": "https://registry.npmmirror.com/big.js/-/big.js-3.2.0.tgz", "_shasum": "a5fc298b81b9e0dca2e458824784b65c52ba588e", "_spec": "big.js@^3.1.3", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\html-webpack-plugin\\node_modules\\loader-utils", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "engines": {"node": "*"}, "files": ["big.js", "big.min.js"], "homepage": "https://github.com/MikeMcl/big.js#readme", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "license": "MIT", "main": "big.js", "name": "big.js", "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/big.js.git"}, "scripts": {"build": "uglifyjs big.js --source-map doc/big.js.map -c -m -o big.min.js --preamble \"/* big.js v3.2.0 https://github.com/MikeMcl/big.js/LICENCE */\"", "test": "node ./test/every-test.js"}, "version": "3.2.0"}