import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: '首页', icon: 'dashboard' }
    }]
  },

  // 系统管理
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    name: 'System',
    meta: { title: '系统管理', icon: 'el-icon-setting' },
    children: [
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/users/list'),
        meta: { title: '用户管理', icon: 'el-icon-user-solid' }
      },
      {
        path: 'roles',
        name: 'RoleList',
        component: () => import('@/views/users/roles'),
        meta: { title: '角色管理', icon: 'el-icon-s-custom' }
      },
      {
        path: 'permissions',
        name: 'PermissionList',
        component: () => import('@/views/users/permissions'),
        meta: { title: '权限管理', icon: 'el-icon-key' }
      },
      {
        path: 'menus',
        name: 'MenuManagement',
        component: () => import('@/views/system/menus'),
        meta: { title: '菜单管理', icon: 'el-icon-menu' }
      }
    ]
  },

  // 题库管理
  {
    path: '/questions',
    component: Layout,
    redirect: '/questions/list',
    name: 'Questions',
    meta: { title: '题库管理', icon: 'el-icon-document' },
    children: [
      {
        path: 'categories',
        name: 'QuestionCategories',
        component: () => import('@/views/questions/categories'),
        meta: { title: '题目分类', icon: 'el-icon-folder' }
      },
      {
        path: 'tags',
        name: 'QuestionTags',
        component: () => import('@/views/questions/tags'),
        meta: { title: '题目标签', icon: 'el-icon-collection-tag' }
      },
      {
        path: 'list',
        name: 'QuestionList',
        component: () => import('@/views/questions/list'),
        meta: { title: '题目列表', icon: 'el-icon-document-copy' }
      },
      {
        path: 'add',
        name: 'QuestionAdd',
        component: () => import('@/views/questions/add'),
        meta: { title: '添加题目', icon: 'el-icon-plus' }
      },
      {
        path: 'edit/:id',
        name: 'QuestionEdit',
        component: () => import('@/views/questions/edit'),
        meta: { title: '编辑题目', icon: 'el-icon-edit' },
        hidden: true
      },
      {
        path: 'upload',
        name: 'QuestionUpload',
        component: () => import('@/views/questions/upload'),
        meta: { title: '批量导入', icon: 'el-icon-upload' }
      }
    ]
  },

  // 学习数据
  {
    path: '/learning',
    component: Layout,
    redirect: '/learning/favorites',
    name: 'Learning',
    meta: { title: '学习数据', icon: 'el-icon-data-analysis' },
    children: [
      {
        path: 'favorites',
        name: 'FavoritesList',
        component: () => import('@/views/learning/favorites'),
        meta: { title: '收藏管理', icon: 'el-icon-star-on' }
      },
      {
        path: 'errors',
        name: 'ErrorsList',
        component: () => import('@/views/learning/errors'),
        meta: { title: '错题管理', icon: 'el-icon-warning' }
      },
      {
        path: 'records',
        name: 'RecordsList',
        component: () => import('@/views/learning/records'),
        meta: { title: '练习记录', icon: 'el-icon-tickets' }
      }
    ]
  },



  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
