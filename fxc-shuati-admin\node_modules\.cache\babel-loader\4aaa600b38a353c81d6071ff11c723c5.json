{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\index.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IE5hdmJhciwgU2lkZWJhciwgQXBwTWFpbiB9IGZyb20gJy4vY29tcG9uZW50cyc7CmltcG9ydCBSZXNpemVNaXhpbiBmcm9tICcuL21peGluL1Jlc2l6ZUhhbmRsZXInOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0xheW91dCcsCiAgY29tcG9uZW50czogewogICAgTmF2YmFyOiBOYXZiYXIsCiAgICBTaWRlYmFyOiBTaWRlYmFyLAogICAgQXBwTWFpbjogQXBwTWFpbgogIH0sCiAgbWl4aW5zOiBbUmVzaXplTWl4aW5dLAogIGNvbXB1dGVkOiB7CiAgICBzaWRlYmFyOiBmdW5jdGlvbiBzaWRlYmFyKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuYXBwLnNpZGViYXI7CiAgICB9LAogICAgZGV2aWNlOiBmdW5jdGlvbiBkZXZpY2UoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5hcHAuZGV2aWNlOwogICAgfSwKICAgIGZpeGVkSGVhZGVyOiBmdW5jdGlvbiBmaXhlZEhlYWRlcigpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLmZpeGVkSGVhZGVyOwogICAgfSwKICAgIGNsYXNzT2JqOiBmdW5jdGlvbiBjbGFzc09iaigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBoaWRlU2lkZWJhcjogIXRoaXMuc2lkZWJhci5vcGVuZWQsCiAgICAgICAgb3BlblNpZGViYXI6IHRoaXMuc2lkZWJhci5vcGVuZWQsCiAgICAgICAgd2l0aG91dEFuaW1hdGlvbjogdGhpcy5zaWRlYmFyLndpdGhvdXRBbmltYXRpb24sCiAgICAgICAgbW9iaWxlOiB0aGlzLmRldmljZSA9PT0gJ21vYmlsZScKICAgICAgfTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZUNsaWNrT3V0c2lkZTogZnVuY3Rpb24gaGFuZGxlQ2xpY2tPdXRzaWRlKCkgewogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL2Nsb3NlU2lkZUJhcicsIHsKICAgICAgICB3aXRob3V0QW5pbWF0aW9uOiBmYWxzZQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "Sidebar", "<PERSON><PERSON><PERSON><PERSON>", "ResizeMixin", "name", "components", "mixins", "computed", "sidebar", "$store", "state", "app", "device", "fixedHeader", "settings", "classObj", "hideSidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "methods", "handleClickOutside", "dispatch"], "sources": ["src/layout/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\n    <sidebar class=\"sidebar-container\" />\n    <div class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar />\n      </div>\n      <app-main />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Navbar, Sidebar, AppMain } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\n\nexport default {\n  name: 'Layout',\n  components: {\n    Navbar,\n    Sidebar,\n    AppMain\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    sidebar() {\n      return this.$store.state.app.sidebar\n    },\n    device() {\n      return this.$store.state.app.device\n    },\n    fixedHeader() {\n      return this.$store.state.settings.fixedHeader\n    },\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/styles/mixin.scss\";\n  @import \"~@/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n    &.mobile.openSidebar{\n      position: fixed;\n      top: 0;\n    }\n  }\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$sideBarWidth});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px)\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AAcA,SAAAA,MAAA,EAAAC,OAAA,EAAAC,OAAA;AACA,OAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,MAAA,EAAAA,MAAA;IACAC,OAAA,EAAAA,OAAA;IACAC,OAAA,EAAAA;EACA;EACAI,MAAA,GAAAH,WAAA;EACAI,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,OAAA;IACA;IACAI,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,MAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAJ,MAAA,CAAAC,KAAA,CAAAI,QAAA,CAAAD,WAAA;IACA;IACAE,QAAA,WAAAA,SAAA;MACA;QACAC,WAAA,QAAAR,OAAA,CAAAS,MAAA;QACAC,WAAA,OAAAV,OAAA,CAAAS,MAAA;QACAE,gBAAA,OAAAX,OAAA,CAAAW,gBAAA;QACAC,MAAA,OAAAR,MAAA;MACA;IACA;EACA;EACAS,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAb,MAAA,CAAAc,QAAA;QAAAJ,gBAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}