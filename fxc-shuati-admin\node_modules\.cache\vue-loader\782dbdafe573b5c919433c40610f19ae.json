{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue", "mtime": 1752628330377}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcycKaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNZW51TWFuYWdlbWVudCcsCiAgY29tcG9uZW50czogeyBQYWdpbmF0aW9uIH0sCiAgZGlyZWN0aXZlczogeyB3YXZlcyB9LAogIGZpbHRlcnM6IHsKICAgIHBhcnNlVGltZQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRhYmxlS2V5OiAwLAogICAgICBsaXN0OiBbXSwKICAgICAgdG90YWw6IDAsCiAgICAgIGxpc3RMb2FkaW5nOiB0cnVlLAogICAgICBsaXN0UXVlcnk6IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIHBhZ2VTaXplOiA1MCwKICAgICAgICBrZXl3b3JkOiAnJywKICAgICAgICBzdGF0dXM6ICcnCiAgICAgIH0sCiAgICAgIHBhcmVudE9wdGlvbnM6IFtdLAogICAgICB0ZW1wOiB7CiAgICAgICAgbWVudV9pZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICBtZW51X25hbWU6ICcnLAogICAgICAgIG1lbnVfcGF0aDogJycsCiAgICAgICAgY29tcG9uZW50OiAnJywKICAgICAgICBtZW51X2ljb246ICcnLAogICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgc29ydF9vcmRlcjogMAogICAgICB9LAogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIGRpYWxvZ1N0YXR1czogJycsCiAgICAgIHRleHRNYXA6IHsKICAgICAgICB1cGRhdGU6ICfnvJbovpHoj5zljZUnLAogICAgICAgIGNyZWF0ZTogJ+a3u+WKoOiPnOWNlScKICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICBtZW51X25hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6I+c5Y2V5ZCN56ew5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIG1lbnVfcGF0aDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfoj5zljZXot6/lvoTkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0KICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHRyZWVEYXRhKCkgewogICAgICAvLyDlsIblubPpk7rmlbDmja7ovazmjaLkuLrmoJHlvaLnu5PmnoQKICAgICAgY29uc3QgdHJlZSA9IFtdCiAgICAgIGNvbnN0IG1hcCA9IHt9CgogICAgICAvLyDlhYjliJvlu7rmiYDmnInoioLngrnnmoTmmKDlsIQKICAgICAgdGhpcy5saXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgbWFwW2l0ZW0ubWVudV9pZF0gPSB7IC4uLml0ZW0sIGNoaWxkcmVuOiBbXSB9CiAgICAgIH0pCgogICAgICAvLyDmnoTlu7rmoJHlvaLnu5PmnoQKICAgICAgdGhpcy5saXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0ucGFyZW50X2lkID09PSAwKSB7CiAgICAgICAgICAvLyDpobbnuqfoj5zljZUKICAgICAgICAgIHRyZWUucHVzaChtYXBbaXRlbS5tZW51X2lkXSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5a2Q6I+c5Y2VCiAgICAgICAgICBpZiAobWFwW2l0ZW0ucGFyZW50X2lkXSkgewogICAgICAgICAgICBtYXBbaXRlbS5wYXJlbnRfaWRdLmNoaWxkcmVuLnB1c2gobWFwW2l0ZW0ubWVudV9pZF0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQoKICAgICAgcmV0dXJuIHRyZWUKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWUKICAgICAgCiAgICAgIC8vIOaooeaLn+aVsOaNru+8jOW<PERSON>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"}, {"version": 3, "sources": ["menus.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "menus.vue", "sourceRoot": "src/views/system", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索菜单名称、路径\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加菜单\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"menu_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"menu_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"菜单名称\" prop=\"menu_name\" min-width=\"150\" />\n      <el-table-column label=\"菜单路径\" prop=\"menu_path\" min-width=\"180\" />\n      <el-table-column label=\"菜单图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.menu_icon\" :class=\"row.menu_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"组件路径\" prop=\"component\" min-width=\"180\" />\n      <el-table-column label=\"父菜单\" prop=\"parent_name\" min-width=\"120\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ formatDate(row.created_at) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父菜单\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父菜单\" style=\"width: 100%\">\n            <el-option label=\"顶级菜单\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.menu_id\"\n              :label=\"item.menu_name\"\n              :value=\"item.menu_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"菜单名称\" prop=\"menu_name\">\n          <el-input v-model=\"temp.menu_name\" />\n        </el-form-item>\n        <el-form-item label=\"菜单路径\" prop=\"menu_path\">\n          <el-input v-model=\"temp.menu_path\" />\n        </el-form-item>\n        <el-form-item label=\"组件路径\" prop=\"component\">\n          <el-input v-model=\"temp.component\" />\n        </el-form-item>\n        <el-form-item label=\"菜单图标\" prop=\"menu_icon\">\n          <el-input v-model=\"temp.menu_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'MenuManagement',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑菜单',\n        create: '添加菜单'\n      },\n      rules: {\n        menu_name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],\n        menu_path: [{ required: true, message: '菜单路径不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  computed: {\n    treeData() {\n      // 将平铺数据转换为树形结构\n      const tree = []\n      const map = {}\n\n      // 先创建所有节点的映射\n      this.list.forEach(item => {\n        map[item.menu_id] = { ...item, children: [] }\n      })\n\n      // 构建树形结构\n      this.list.forEach(item => {\n        if (item.parent_id === 0) {\n          // 顶级菜单\n          tree.push(map[item.menu_id])\n        } else {\n          // 子菜单\n          if (map[item.parent_id]) {\n            map[item.parent_id].children.push(map[item.menu_id])\n          }\n        }\n      })\n\n      return tree\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟数据，实际项目中应该调用API\n      setTimeout(() => {\n        this.list = [\n          // 首页\n          {\n            menu_id: 1,\n            menu_name: '首页',\n            menu_path: '/dashboard',\n            component: 'Layout',\n            menu_icon: 'dashboard',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            menu_id: 2,\n            menu_name: '首页',\n            menu_path: '/dashboard/index',\n            component: 'dashboard/index',\n            menu_icon: 'dashboard',\n            parent_id: 1,\n            parent_name: '首页',\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          // 系统管理\n          {\n            menu_id: 10,\n            menu_name: '系统管理',\n            menu_path: '/system',\n            component: 'Layout',\n            menu_icon: 'el-icon-setting',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            menu_id: 11,\n            menu_name: '用户管理',\n            menu_path: '/system/users',\n            component: 'users/list',\n            menu_icon: 'el-icon-user-solid',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            menu_id: 12,\n            menu_name: '角色管理',\n            menu_path: '/system/roles',\n            component: 'users/roles',\n            menu_icon: 'el-icon-s-custom',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            menu_id: 13,\n            menu_name: '权限管理',\n            menu_path: '/system/permissions',\n            component: 'users/permissions',\n            menu_icon: 'el-icon-key',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          },\n          {\n            menu_id: 14,\n            menu_name: '菜单管理',\n            menu_path: '/system/menus',\n            component: 'system/menus',\n            menu_icon: 'el-icon-menu',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-02'\n          },\n          // 题库管理\n          {\n            menu_id: 20,\n            menu_name: '题库管理',\n            menu_path: '/questions',\n            component: 'Layout',\n            menu_icon: 'el-icon-document',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 21,\n            menu_name: '题目分类',\n            menu_path: '/questions/categories',\n            component: 'questions/categories',\n            menu_icon: 'el-icon-folder',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-03'\n          },\n          {\n            menu_id: 22,\n            menu_name: '题目列表',\n            menu_path: '/questions/list',\n            component: 'questions/list',\n            menu_icon: 'el-icon-document-copy',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-03'\n          },\n          {\n            menu_id: 23,\n            menu_name: '添加题目',\n            menu_path: '/questions/add',\n            component: 'questions/add',\n            menu_icon: 'el-icon-plus',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-03'\n          },\n          {\n            menu_id: 24,\n            menu_name: '批量导入',\n            menu_path: '/questions/upload',\n            component: 'questions/upload',\n            menu_icon: 'el-icon-upload',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-03'\n          },\n          // 学习数据\n          {\n            menu_id: 30,\n            menu_name: '学习数据',\n            menu_path: '/learning',\n            component: 'Layout',\n            menu_icon: 'el-icon-data-analysis',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-04'\n          },\n          {\n            menu_id: 31,\n            menu_name: '收藏管理',\n            menu_path: '/learning/favorites',\n            component: 'learning/favorites',\n            menu_icon: 'el-icon-star-on',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-04'\n          },\n          {\n            menu_id: 32,\n            menu_name: '错题管理',\n            menu_path: '/learning/errors',\n            component: 'learning/errors',\n            menu_icon: 'el-icon-warning',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-04'\n          },\n          {\n            menu_id: 33,\n            menu_name: '练习记录',\n            menu_path: '/learning/records',\n            component: 'learning/records',\n            menu_icon: 'el-icon-tickets',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-04'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n        \n        // 更新父菜单选项\n        this.parentOptions = this.list.filter(item => item.parent_id === 0)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该菜单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}