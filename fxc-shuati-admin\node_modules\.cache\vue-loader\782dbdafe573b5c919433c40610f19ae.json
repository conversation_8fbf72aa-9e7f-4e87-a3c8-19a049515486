{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue", "mtime": 1752627989997}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcycKaW1wb3J0IHsgcGFyc2VUaW1lIH0gZnJvbSAnQC91dGlscycKaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ01lbnVNYW5hZ2VtZW50JywKICBjb21wb25lbnRzOiB7IFBhZ2luYXRpb24gfSwKICBkaXJlY3RpdmVzOiB7IHdhdmVzIH0sCiAgZmlsdGVyczogewogICAgcGFyc2VUaW1lCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDUwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHN0YXR1czogJycKICAgICAgfSwKICAgICAgcGFyZW50T3B0aW9uczogW10sCiAgICAgIHRlbXA6IHsKICAgICAgICBtZW51X2lkOiB1bmRlZmluZWQsCiAgICAgICAgcGFyZW50X2lkOiAwLAogICAgICAgIG1lbnVfbmFtZTogJycsCiAgICAgICAgbWVudV9wYXRoOiAnJywKICAgICAgICBjb21wb25lbnQ6ICcnLAogICAgICAgIG1lbnVfaWNvbjogJycsCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICBzb3J0X29yZGVyOiAwCiAgICAgIH0sCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nU3RhdHVzOiAnJywKICAgICAgdGV4dE1hcDogewogICAgICAgIHVwZGF0ZTogJ+e8lui+keiPnOWNlScsCiAgICAgICAgY3JlYXRlOiAn5re75Yqg6I+c5Y2VJwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIG1lbnVfbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfoj5zljZXlkI3np7DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgbWVudV9wYXRoOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+iPnOWNlei3r+W+hOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9XQogICAgICB9CiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgdHJlZURhdGEoKSB7CiAgICAgIC8vIOWwhuW5s+mTuuaVsOaNrui9rOaNouS4uuagkeW9oue7k+aehAogICAgICBjb25zdCB0cmVlID0gW10KICAgICAgY29uc3QgbWFwID0ge30KCiAgICAgIC8vIOWFiOWIm+W7uuaJgOacieiKgueCueeahOaYoOWwhAogICAgICB0aGlzLmxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBtYXBbaXRlbS5tZW51X2lkXSA9IHsgLi4uaXRlbSwgY2hpbGRyZW46IFtdIH0KICAgICAgfSkKCiAgICAgIC8vIOaehOW7uuagkeW9oue7k+aehAogICAgICB0aGlzLmxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS5wYXJlbnRfaWQgPT09IDApIHsKICAgICAgICAgIC8vIOmhtue6p+iPnOWNlQogICAgICAgICAgdHJlZS5wdXNoKG1hcFtpdGVtLm1lbnVfaWRdKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDlrZDoj5zljZUKICAgICAgICAgIGlmIChtYXBbaXRlbS5wYXJlbnRfaWRdKSB7CiAgICAgICAgICAgIG1hcFtpdGVtLnBhcmVudF9pZF0uY2hpbGRyZW4ucHVzaChtYXBbaXRlbS5tZW51X2lkXSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pCgogICAgICByZXR1cm4gdHJlZQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZQogICAgICAKICAgICAgLy8g5qih5ouf5pWw5o2u77yM5a6e6ZmF6aG555uu5Lit5bqU6K+l6LCD55SoQVBJCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIHRoaXMubGlzdCA9IFsKICAgICAgICAgIC8vIOmmlumhtQogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAxLAogICAgICAgICAgICBtZW51X25hbWU6ICfpppbpobUnLAogICAgICAgICAgICBtZW51X3BhdGg6ICcvZGFzaGJvYXJkJywKICAgICAgICAgICAgY29tcG9uZW50OiAnTGF5b3V0JywKICAgICAgICAgICAgbWVudV9pY29uOiAnZGFzaGJvYXJkJywKICAgICAgICAgICAgcGFyZW50X2lkOiAwLAogICAgICAgICAgICBwYXJlbnRfbmFtZTogJ+mhtue6p+iPnOWNlScsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDEKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIG1lbnVfaWQ6IDIsCiAgICAgICAgICAgIG1lbnVfbmFtZTogJ+mmlumhtScsCiAgICAgICAgICAgIG1lbnVfcGF0aDogJy9kYXNoYm9hcmQvaW5kZXgnLAogICAgICAgICAgICBjb21wb25lbnQ6ICdkYXNoYm9hcmQvaW5kZXgnLAogICAgICAgICAgICBtZW51X2ljb246ICdkYXNoYm9hcmQnLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDEsCiAgICAgICAgICAgIHBhcmVudF9uYW1lOiAn6aaW6aG1JywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMQogICAgICAgICAgfSwKICAgICAgICAgIC8vIOezu+e7n+euoeeQhgogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAxMCwKICAgICAgICAgICAgbWVudV9uYW1lOiAn57O757uf566h55CGJywKICAgICAgICAgICAgbWVudV9wYXRoOiAnL3N5c3RlbScsCiAgICAgICAgICAgIGNvbXBvbmVudDogJ0xheW91dCcsCiAgICAgICAgICAgIG1lbnVfaWNvbjogJ2VsLWljb24tc2V0dGluZycsCiAgICAgICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICAgICAgcGFyZW50X25hbWU6ICfpobbnuqfoj5zljZUnLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAyCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAxMSwKICAgICAgICAgICAgbWVudV9uYW1lOiAn55So5oi3566h55CGJywKICAgICAgICAgICAgbWVudV9wYXRoOiAnL3N5c3RlbS91c2VycycsCiAgICAgICAgICAgIGNvbXBvbmVudDogJ3VzZXJzL2xpc3QnLAogICAgICAgICAgICBtZW51X2ljb246ICdlbC1pY29uLXVzZXItc29saWQnLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDEwLAogICAgICAgICAgICBwYXJlbnRfbmFtZTogJ+ezu+e7n+euoeeQhicsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDEKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIG1lbnVfaWQ6IDEyLAogICAgICAgICAgICBtZW51X25hbWU6ICfop5LoibLnrqHnkIYnLAogICAgICAgICAgICBtZW51X3BhdGg6ICcvc3lzdGVtL3JvbGVzJywKICAgICAgICAgICAgY29tcG9uZW50OiAndXNlcnMvcm9sZXMnLAogICAgICAgICAgICBtZW51X2ljb246ICdlbC1pY29uLXMtY3VzdG9tJywKICAgICAgICAgICAgcGFyZW50X2lkOiAxMCwKICAgICAgICAgICAgcGFyZW50X25hbWU6ICfns7vnu5/nrqHnkIYnLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAyCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAxMywKICAgICAgICAgICAgbWVudV9uYW1lOiAn5p2D6ZmQ566h55CGJywKICAgICAgICAgICAgbWVudV9wYXRoOiAnL3N5c3RlbS9wZXJtaXNzaW9ucycsCiAgICAgICAgICAgIGNvbXBvbmVudDogJ3VzZXJzL3Blcm1pc3Npb25zJywKICAgICAgICAgICAgbWVudV9pY29uOiAnZWwtaWNvbi1rZXknLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDEwLAogICAgICAgICAgICBwYXJlbnRfbmFtZTogJ+ezu+e7n+euoeeQhicsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDMKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIG1lbnVfaWQ6IDE0LAogICAgICAgICAgICBtZW51X25hbWU6ICfoj5zljZXnrqHnkIYnLAogICAgICAgICAgICBtZW51X3BhdGg6ICcvc3lzdGVtL21lbnVzJywKICAgICAgICAgICAgY29tcG9uZW50OiAnc3lzdGVtL21lbnVzJywKICAgICAgICAgICAgbWVudV9pY29uOiAnZWwtaWNvbi1tZW51JywKICAgICAgICAgICAgcGFyZW50X2lkOiAxMCwKICAgICAgICAgICAgcGFyZW50X25hbWU6ICfns7vnu5/nrqHnkIYnLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiA0CiAgICAgICAgICB9LAogICAgICAgICAgLy8g6aKY5bqT566h55CGCiAgICAgICAgICB7CiAgICAgICAgICAgIG1lbnVfaWQ6IDIwLAogICAgICAgICAgICBtZW51X25hbWU6ICfpopjlupPnrqHnkIYnLAogICAgICAgICAgICBtZW51X3BhdGg6ICcvcXVlc3Rpb25zJywKICAgICAgICAgICAgY29tcG9uZW50OiAnTGF5b3V0JywKICAgICAgICAgICAgbWVudV9pY29uOiAnZWwtaWNvbi1kb2N1bWVudCcsCiAgICAgICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICAgICAgcGFyZW50X25hbWU6ICfpobbnuqfoj5zljZUnLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAzCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAyMSwKICAgICAgICAgICAgbWVudV9uYW1lOiAn6aKY55uu5YiG57G7JywKICAgICAgICAgICAgbWVudV9wYXRoOiAnL3F1ZXN0aW9ucy9jYXRlZ29yaWVzJywKICAgICAgICAgICAgY29tcG9uZW50OiAncXVlc3Rpb25zL2NhdGVnb3JpZXMnLAogICAgICAgICAgICBtZW51X2ljb246ICdlbC1pY29uLWZvbGRlcicsCiAgICAgICAgICAgIHBhcmVudF9pZDogMjAsCiAgICAgICAgICAgIHBhcmVudF9uYW1lOiAn6aKY5bqT566h55CGJywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbWVudV9pZDogMjIsCiAgICAgICAgICAgIG1lbnVfbmFtZTogJ+mimOebruWIl+ihqCcsCiAgICAgICAgICAgIG1lbnVfcGF0aDogJy9xdWVzdGlvbnMvbGlzdCcsCiAgICAgICAgICAgIGNvbXBvbmVudDogJ3F1ZXN0aW9ucy9saXN0JywKICAgICAgICAgICAgbWVudV9pY29uOiAnZWwtaWNvbi1kb2N1bWVudC1jb3B5JywKICAgICAgICAgICAgcGFyZW50X2lkOiAyMCwKICAgICAgICAgICAgcGFyZW50X25hbWU6ICfpopjlupPnrqHnkIYnLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAyCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAyMywKICAgICAgICAgICAgbWVudV9uYW1lOiAn5re75Yqg6aKY55uuJywKICAgICAgICAgICAgbWVudV9wYXRoOiAnL3F1ZXN0aW9ucy9hZGQnLAogICAgICAgICAgICBjb21wb25lbnQ6ICdxdWVzdGlvbnMvYWRkJywKICAgICAgICAgICAgbWVudV9pY29uOiAnZWwtaWNvbi1wbHVzJywKICAgICAgICAgICAgcGFyZW50X2lkOiAyMCwKICAgICAgICAgICAgcGFyZW50X25hbWU6ICfpopjlupPnrqHnkIYnLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAzCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAyNCwKICAgICAgICAgICAgbWVudV9uYW1lOiAn5om56YeP5a+85YWlJywKICAgICAgICAgICAgbWVudV9wYXRoOiAnL3F1ZXN0aW9ucy91cGxvYWQnLAogICAgICAgICAgICBjb21wb25lbnQ6ICdxdWVzdGlvbnMvdXBsb2FkJywKICAgICAgICAgICAgbWVudV9pY29uOiAnZWwtaWNvbi11cGxvYWQnLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDIwLAogICAgICAgICAgICBwYXJlbnRfbmFtZTogJ+mimOW6k+euoeeQhicsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDQKICAgICAgICAgIH0sCiAgICAgICAgICAvLyDlrabkuaDmlbDmja4KICAgICAgICAgIHsKICAgICAgICAgICAgbWVudV9pZDogMzAsCiAgICAgICAgICAgIG1lbnVfbmFtZTogJ+WtpuS5oOaVsOaNricsCiAgICAgICAgICAgIG1lbnVfcGF0aDogJy9sZWFybmluZycsCiAgICAgICAgICAgIGNvbXBvbmVudDogJ0xheW91dCcsCiAgICAgICAgICAgIG1lbnVfaWNvbjogJ2VsLWljb24tZGF0YS1hbmFseXNpcycsCiAgICAgICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICAgICAgcGFyZW50X25hbWU6ICfpobbnuqfoj5zljZUnLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiA0CiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBtZW51X2lkOiAzMSwKICAgICAgICAgICAgbWVudV9uYW1lOiAn5pS26JeP566h55CGJywKICAgICAgICAgICAgbWVudV9wYXRoOiAnL2xlYXJuaW5nL2Zhdm9yaXRlcycsCiAgICAgICAgICAgIGNvbXBvbmVudDogJ2xlYXJuaW5nL2Zhdm9yaXRlcycsCiAgICAgICAgICAgIG1lbnVfaWNvbjogJ2VsLWljb24tc3Rhci1vbicsCiAgICAgICAgICAgIHBhcmVudF9pZDogMzAsCiAgICAgICAgICAgIHBhcmVudF9uYW1lOiAn5a2m5Lmg5pWw5o2uJywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbWVudV9pZDogMzIsCiAgICAgICAgICAgIG1lbnVfbmFtZTogJ+mUmemimOeuoeeQhicsCiAgICAgICAgICAgIG1lbnVfcGF0aDogJy9sZWFybmluZy9lcnJvcnMnLAogICAgICAgICAgICBjb21wb25lbnQ6ICdsZWFybmluZy9lcnJvcnMnLAogICAgICAgICAgICBtZW51X2ljb246ICdlbC1pY29uLXdhcm5pbmcnLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDMwLAogICAgICAgICAgICBwYXJlbnRfbmFtZTogJ+WtpuS5oOaVsOaNricsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDIKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIG1lbnVfaWQ6IDMzLAogICAgICAgICAgICBtZW51X25hbWU6ICfnu4PkuaDorrDlvZUnLAogICAgICAgICAgICBtZW51X3BhdGg6ICcvbGVhcm5pbmcvcmVjb3JkcycsCiAgICAgICAgICAgIGNvbXBvbmVudDogJ2xlYXJuaW5nL3JlY29yZHMnLAogICAgICAgICAgICBtZW51X2ljb246ICdlbC1pY29uLXRpY2tldHMnLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDMwLAogICAgICAgICAgICBwYXJlbnRfbmFtZTogJ+WtpuS5oOaVsOaNricsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDMKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMubGlzdC5sZW5ndGgKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2UKICAgICAgICAKICAgICAgICAvLyDmm7TmlrDniLboj5zljZXpgInpobkKICAgICAgICB0aGlzLnBhcmVudE9wdGlvbnMgPSB0aGlzLmxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5wYXJlbnRfaWQgPT09IDApCiAgICAgIH0sIDUwMCkKICAgIH0sCiAgICBoYW5kbGVGaWx0ZXIoKSB7CiAgICAgIHRoaXMubGlzdFF1ZXJ5LnBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgcmVzZXRUZW1wKCkgewogICAgICB0aGlzLnRlbXAgPSB7CiAgICAgICAgbWVudV9pZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICBtZW51X25hbWU6ICcnLAogICAgICAgIG1lbnVfcGF0aDogJycsCiAgICAgICAgY29tcG9uZW50OiAnJywKICAgICAgICBtZW51X2ljb246ICcnLAogICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgc29ydF9vcmRlcjogMAogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ3JlYXRlKCkgewogICAgICB0aGlzLnJlc2V0VGVtcCgpCiAgICAgIHRoaXMuZGlhbG9nU3RhdHVzID0gJ2NyZWF0ZScKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgY3JlYXRlRGF0YSgpIHsKICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgbWVzc2FnZTogJ+WIm+W7uuaIkOWKn++8iOaooeaLn++8iScsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnRlbXAgPSBPYmplY3QuYXNzaWduKHt9LCByb3cpCiAgICAgIHRoaXMuZGlhbG9nU3RhdHVzID0gJ3VwZGF0ZScKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgdXBkYXRlRGF0YSgpIHsKICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgbWVzc2FnZTogJ+abtOaWsOaIkOWKn++8iOaooeaLn++8iScsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTor6Xoj5zljZXlkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICB9KQogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["menus.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "menus.vue", "sourceRoot": "src/views/system", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索菜单名称、路径\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加菜单\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"menu_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"menu_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"菜单名称\" prop=\"menu_name\" min-width=\"150\" />\n      <el-table-column label=\"菜单路径\" prop=\"menu_path\" min-width=\"180\" />\n      <el-table-column label=\"菜单图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.menu_icon\" :class=\"row.menu_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"组件路径\" prop=\"component\" min-width=\"180\" />\n      <el-table-column label=\"父菜单\" prop=\"parent_name\" min-width=\"120\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父菜单\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父菜单\" style=\"width: 100%\">\n            <el-option label=\"顶级菜单\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.menu_id\"\n              :label=\"item.menu_name\"\n              :value=\"item.menu_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"菜单名称\" prop=\"menu_name\">\n          <el-input v-model=\"temp.menu_name\" />\n        </el-form-item>\n        <el-form-item label=\"菜单路径\" prop=\"menu_path\">\n          <el-input v-model=\"temp.menu_path\" />\n        </el-form-item>\n        <el-form-item label=\"组件路径\" prop=\"component\">\n          <el-input v-model=\"temp.component\" />\n        </el-form-item>\n        <el-form-item label=\"菜单图标\" prop=\"menu_icon\">\n          <el-input v-model=\"temp.menu_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'MenuManagement',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑菜单',\n        create: '添加菜单'\n      },\n      rules: {\n        menu_name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],\n        menu_path: [{ required: true, message: '菜单路径不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  computed: {\n    treeData() {\n      // 将平铺数据转换为树形结构\n      const tree = []\n      const map = {}\n\n      // 先创建所有节点的映射\n      this.list.forEach(item => {\n        map[item.menu_id] = { ...item, children: [] }\n      })\n\n      // 构建树形结构\n      this.list.forEach(item => {\n        if (item.parent_id === 0) {\n          // 顶级菜单\n          tree.push(map[item.menu_id])\n        } else {\n          // 子菜单\n          if (map[item.parent_id]) {\n            map[item.parent_id].children.push(map[item.menu_id])\n          }\n        }\n      })\n\n      return tree\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟数据，实际项目中应该调用API\n      setTimeout(() => {\n        this.list = [\n          // 首页\n          {\n            menu_id: 1,\n            menu_name: '首页',\n            menu_path: '/dashboard',\n            component: 'Layout',\n            menu_icon: 'dashboard',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 2,\n            menu_name: '首页',\n            menu_path: '/dashboard/index',\n            component: 'dashboard/index',\n            menu_icon: 'dashboard',\n            parent_id: 1,\n            parent_name: '首页',\n            status: 'active',\n            sort_order: 1\n          },\n          // 系统管理\n          {\n            menu_id: 10,\n            menu_name: '系统管理',\n            menu_path: '/system',\n            component: 'Layout',\n            menu_icon: 'el-icon-setting',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 11,\n            menu_name: '用户管理',\n            menu_path: '/system/users',\n            component: 'users/list',\n            menu_icon: 'el-icon-user-solid',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 12,\n            menu_name: '角色管理',\n            menu_path: '/system/roles',\n            component: 'users/roles',\n            menu_icon: 'el-icon-s-custom',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 13,\n            menu_name: '权限管理',\n            menu_path: '/system/permissions',\n            component: 'users/permissions',\n            menu_icon: 'el-icon-key',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 14,\n            menu_name: '菜单管理',\n            menu_path: '/system/menus',\n            component: 'system/menus',\n            menu_icon: 'el-icon-menu',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 题库管理\n          {\n            menu_id: 20,\n            menu_name: '题库管理',\n            menu_path: '/questions',\n            component: 'Layout',\n            menu_icon: 'el-icon-document',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 21,\n            menu_name: '题目分类',\n            menu_path: '/questions/categories',\n            component: 'questions/categories',\n            menu_icon: 'el-icon-folder',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 22,\n            menu_name: '题目列表',\n            menu_path: '/questions/list',\n            component: 'questions/list',\n            menu_icon: 'el-icon-document-copy',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 23,\n            menu_name: '添加题目',\n            menu_path: '/questions/add',\n            component: 'questions/add',\n            menu_icon: 'el-icon-plus',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 24,\n            menu_name: '批量导入',\n            menu_path: '/questions/upload',\n            component: 'questions/upload',\n            menu_icon: 'el-icon-upload',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 学习数据\n          {\n            menu_id: 30,\n            menu_name: '学习数据',\n            menu_path: '/learning',\n            component: 'Layout',\n            menu_icon: 'el-icon-data-analysis',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 4\n          },\n          {\n            menu_id: 31,\n            menu_name: '收藏管理',\n            menu_path: '/learning/favorites',\n            component: 'learning/favorites',\n            menu_icon: 'el-icon-star-on',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 32,\n            menu_name: '错题管理',\n            menu_path: '/learning/errors',\n            component: 'learning/errors',\n            menu_icon: 'el-icon-warning',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 33,\n            menu_name: '练习记录',\n            menu_path: '/learning/records',\n            component: 'learning/records',\n            menu_icon: 'el-icon-tickets',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 3\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n        \n        // 更新父菜单选项\n        this.parentOptions = this.list.filter(item => item.parent_id === 0)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该菜单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}