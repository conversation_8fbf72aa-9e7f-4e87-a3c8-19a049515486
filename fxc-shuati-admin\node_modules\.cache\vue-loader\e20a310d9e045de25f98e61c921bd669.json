{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\login\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\login\\index.vue", "mtime": 1752570612893}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/login", "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" auto-complete=\"on\" label-position=\"left\">\n\n      <div class=\"title-container\">\n        <h3 class=\"title\">Login Form</h3>\n      </div>\n\n      <el-form-item prop=\"username\">\n        <span class=\"svg-container\">\n          <svg-icon icon-class=\"user\" />\n        </span>\n        <el-input\n          ref=\"username\"\n          v-model=\"loginForm.username\"\n          placeholder=\"Username\"\n          name=\"username\"\n          type=\"text\"\n          tabindex=\"1\"\n          auto-complete=\"on\"\n        />\n      </el-form-item>\n\n      <el-form-item prop=\"password\">\n        <span class=\"svg-container\">\n          <svg-icon icon-class=\"password\" />\n        </span>\n        <el-input\n          :key=\"passwordType\"\n          ref=\"password\"\n          v-model=\"loginForm.password\"\n          :type=\"passwordType\"\n          placeholder=\"Password\"\n          name=\"password\"\n          tabindex=\"2\"\n          auto-complete=\"on\"\n          @keyup.enter.native=\"handleLogin\"\n        />\n        <span class=\"show-pwd\" @click=\"showPwd\">\n          <svg-icon :icon-class=\"passwordType === 'password' ? 'eye' : 'eye-open'\" />\n        </span>\n      </el-form-item>\n\n      <el-button :loading=\"loading\" type=\"primary\" style=\"width:100%;margin-bottom:30px;\" @click.native.prevent=\"handleLogin\">Login</el-button>\n\n      <div class=\"tips\">\n        <span style=\"margin-right:20px;\">username: admin</span>\n        <span> password: any</span>\n      </div>\n\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { validUsername } from '@/utils/validate'\n\nexport default {\n  name: 'Login',\n  data() {\n    const validateUsername = (rule, value, callback) => {\n      if (!validUsername(value)) {\n        callback(new Error('Please enter the correct user name'))\n      } else {\n        callback()\n      }\n    }\n    const validatePassword = (rule, value, callback) => {\n      if (value.length < 6) {\n        callback(new Error('The password can not be less than 6 digits'))\n      } else {\n        callback()\n      }\n    }\n    return {\n      loginForm: {\n        username: 'admin',\n        password: '123456'\n      },\n      loginRules: {\n        username: [{ required: true, trigger: 'blur', validator: validateUsername }],\n        password: [{ required: true, trigger: 'blur', validator: validatePassword }]\n      },\n      loading: false,\n      passwordType: 'password',\n      redirect: undefined\n    }\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        this.redirect = route.query && route.query.redirect\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    showPwd() {\n      if (this.passwordType === 'password') {\n        this.passwordType = ''\n      } else {\n        this.passwordType = 'password'\n      }\n      this.$nextTick(() => {\n        this.$refs.password.focus()\n      })\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          this.$store.dispatch('user/login', this.loginForm).then(() => {\n            // 登录成功后获取用户信息\n            return this.$store.dispatch('user/getInfo')\n          }).then(() => {\n            // 获取用户信息成功后跳转\n            this.$router.push({ path: this.redirect || '/' })\n            this.loading = false\n          }).catch((error) => {\n            console.error('登录或获取用户信息失败:', error)\n            this.loading = false\n          })\n        } else {\n          console.log('error submit!!')\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/* 修复input 背景不协调 和光标变色 */\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\n\n$bg:#283443;\n$light_gray:#fff;\n$cursor: #fff;\n\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\n  .login-container .el-input input {\n    color: $cursor;\n  }\n}\n\n/* reset element-ui css */\n.login-container {\n  .el-input {\n    display: inline-block;\n    height: 47px;\n    width: 85%;\n\n    input {\n      background: transparent;\n      border: 0px;\n      -webkit-appearance: none;\n      border-radius: 0px;\n      padding: 12px 5px 12px 15px;\n      color: $light_gray;\n      height: 47px;\n      caret-color: $cursor;\n\n      &:-webkit-autofill {\n        box-shadow: 0 0 0px 1000px $bg inset !important;\n        -webkit-text-fill-color: $cursor !important;\n      }\n    }\n  }\n\n  .el-form-item {\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    background: rgba(0, 0, 0, 0.1);\n    border-radius: 5px;\n    color: #454545;\n  }\n}\n</style>\n\n<style lang=\"scss\" scoped>\n$bg:#2d3a4b;\n$dark_gray:#889aa4;\n$light_gray:#eee;\n\n.login-container {\n  min-height: 100%;\n  width: 100%;\n  background-color: $bg;\n  overflow: hidden;\n\n  .login-form {\n    position: relative;\n    width: 520px;\n    max-width: 100%;\n    padding: 160px 35px 0;\n    margin: 0 auto;\n    overflow: hidden;\n  }\n\n  .tips {\n    font-size: 14px;\n    color: #fff;\n    margin-bottom: 10px;\n\n    span {\n      &:first-of-type {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .svg-container {\n    padding: 6px 5px 6px 15px;\n    color: $dark_gray;\n    vertical-align: middle;\n    width: 30px;\n    display: inline-block;\n  }\n\n  .title-container {\n    position: relative;\n\n    .title {\n      font-size: 26px;\n      color: $light_gray;\n      margin: 0px auto 40px auto;\n      text-align: center;\n      font-weight: bold;\n    }\n  }\n\n  .show-pwd {\n    position: absolute;\n    right: 10px;\n    top: 7px;\n    font-size: 16px;\n    color: $dark_gray;\n    cursor: pointer;\n    user-select: none;\n  }\n}\n</style>\n"]}]}