{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\api\\users.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\api\\users.js", "mtime": 1752568233199}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "getWechatUserList", "params", "url", "method", "getWechatUserDetail", "id", "concat", "updateWechatUser", "data", "deleteWechatUser", "batchDeleteWechatUsers", "ids", "getAdminUserList", "createAdminUser", "updateAdminUser", "deleteAdminUser", "getRoleList", "createRole", "updateRole", "deleteRole", "getRolePermissions", "updateRolePermissions", "getPermissionList", "getPermissionTree", "createPermission", "updatePermission", "deletePermission", "getUserStats", "getWechatUserStats"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/api/users.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 微信用户管理API\nexport function getWechatUserList(params) {\n  return request({\n    url: '/api/wechat-users',\n    method: 'get',\n    params\n  })\n}\n\nexport function getWechatUserDetail(id) {\n  return request({\n    url: `/api/wechat-users/${id}`,\n    method: 'get'\n  })\n}\n\nexport function updateWechatUser(id, data) {\n  return request({\n    url: `/api/wechat-users/${id}`,\n    method: 'put',\n    data\n  })\n}\n\nexport function deleteWechatUser(id) {\n  return request({\n    url: `/api/wechat-users/${id}`,\n    method: 'delete'\n  })\n}\n\nexport function batchDeleteWechatUsers(ids) {\n  return request({\n    url: '/api/wechat-users/batch',\n    method: 'delete',\n    data: { ids }\n  })\n}\n\n// 管理员用户API\nexport function getAdminUserList(params) {\n  return request({\n    url: '/api/admin-users',\n    method: 'get',\n    params\n  })\n}\n\nexport function createAdminUser(data) {\n  return request({\n    url: '/api/admin-users',\n    method: 'post',\n    data\n  })\n}\n\nexport function updateAdminUser(id, data) {\n  return request({\n    url: `/api/admin-users/${id}`,\n    method: 'put',\n    data\n  })\n}\n\nexport function deleteAdminUser(id) {\n  return request({\n    url: `/api/admin-users/${id}`,\n    method: 'delete'\n  })\n}\n\n// 角色管理API\nexport function getRoleList(params) {\n  return request({\n    url: '/api/roles',\n    method: 'get',\n    params\n  })\n}\n\nexport function createRole(data) {\n  return request({\n    url: '/api/roles',\n    method: 'post',\n    data\n  })\n}\n\nexport function updateRole(id, data) {\n  return request({\n    url: `/api/roles/${id}`,\n    method: 'put',\n    data\n  })\n}\n\nexport function deleteRole(id) {\n  return request({\n    url: `/api/roles/${id}`,\n    method: 'delete'\n  })\n}\n\nexport function getRolePermissions(id) {\n  return request({\n    url: `/api/roles/${id}/permissions`,\n    method: 'get'\n  })\n}\n\nexport function updateRolePermissions(id, data) {\n  return request({\n    url: `/api/roles/${id}/permissions`,\n    method: 'put',\n    data\n  })\n}\n\n// 权限管理API\nexport function getPermissionList(params) {\n  return request({\n    url: '/api/permissions',\n    method: 'get',\n    params\n  })\n}\n\nexport function getPermissionTree() {\n  return request({\n    url: '/api/permissions/tree',\n    method: 'get'\n  })\n}\n\nexport function createPermission(data) {\n  return request({\n    url: '/api/permissions',\n    method: 'post',\n    data\n  })\n}\n\nexport function updatePermission(id, data) {\n  return request({\n    url: `/api/permissions/${id}`,\n    method: 'put',\n    data\n  })\n}\n\nexport function deletePermission(id) {\n  return request({\n    url: `/api/permissions/${id}`,\n    method: 'delete'\n  })\n}\n\n// 用户统计API\nexport function getUserStats() {\n  return request({\n    url: '/api/users/stats',\n    method: 'get'\n  })\n}\n\nexport function getWechatUserStats() {\n  return request({\n    url: '/api/wechat-users/stats',\n    method: 'get'\n  })\n}\n\n// 已在上面定义了getPermissionTree函数，这里不需要重复定义\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAE;EACxC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASG,mBAAmBA,CAACC,EAAE,EAAE;EACtC,OAAON,OAAO,CAAC;IACbG,GAAG,uBAAAI,MAAA,CAAuBD,EAAE,CAAE;IAC9BF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASI,gBAAgBA,CAACF,EAAE,EAAEG,IAAI,EAAE;EACzC,OAAOT,OAAO,CAAC;IACbG,GAAG,uBAAAI,MAAA,CAAuBD,EAAE,CAAE;IAC9BF,MAAM,EAAE,KAAK;IACbK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASC,gBAAgBA,CAACJ,EAAE,EAAE;EACnC,OAAON,OAAO,CAAC;IACbG,GAAG,uBAAAI,MAAA,CAAuBD,EAAE,CAAE;IAC9BF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASO,sBAAsBA,CAACC,GAAG,EAAE;EAC1C,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAE;MAAEG,GAAG,EAAHA;IAAI;EACd,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAACX,MAAM,EAAE;EACvC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASY,eAAeA,CAACL,IAAI,EAAE;EACpC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASM,eAAeA,CAACT,EAAE,EAAEG,IAAI,EAAE;EACxC,OAAOT,OAAO,CAAC;IACbG,GAAG,sBAAAI,MAAA,CAAsBD,EAAE,CAAE;IAC7BF,MAAM,EAAE,KAAK;IACbK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASO,eAAeA,CAACV,EAAE,EAAE;EAClC,OAAON,OAAO,CAAC;IACbG,GAAG,sBAAAI,MAAA,CAAsBD,EAAE,CAAE;IAC7BF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,WAAWA,CAACf,MAAM,EAAE;EAClC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASgB,UAAUA,CAACT,IAAI,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASU,UAAUA,CAACb,EAAE,EAAEG,IAAI,EAAE;EACnC,OAAOT,OAAO,CAAC;IACbG,GAAG,gBAAAI,MAAA,CAAgBD,EAAE,CAAE;IACvBF,MAAM,EAAE,KAAK;IACbK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASW,UAAUA,CAACd,EAAE,EAAE;EAC7B,OAAON,OAAO,CAAC;IACbG,GAAG,gBAAAI,MAAA,CAAgBD,EAAE,CAAE;IACvBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASiB,kBAAkBA,CAACf,EAAE,EAAE;EACrC,OAAON,OAAO,CAAC;IACbG,GAAG,gBAAAI,MAAA,CAAgBD,EAAE,iBAAc;IACnCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASkB,qBAAqBA,CAAChB,EAAE,EAAEG,IAAI,EAAE;EAC9C,OAAOT,OAAO,CAAC;IACbG,GAAG,gBAAAI,MAAA,CAAgBD,EAAE,iBAAc;IACnCF,MAAM,EAAE,KAAK;IACbK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,iBAAiBA,CAACrB,MAAM,EAAE;EACxC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASsB,iBAAiBA,CAAA,EAAG;EAClC,OAAOxB,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASqB,gBAAgBA,CAAChB,IAAI,EAAE;EACrC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASiB,gBAAgBA,CAACpB,EAAE,EAAEG,IAAI,EAAE;EACzC,OAAOT,OAAO,CAAC;IACbG,GAAG,sBAAAI,MAAA,CAAsBD,EAAE,CAAE;IAC7BF,MAAM,EAAE,KAAK;IACbK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASkB,gBAAgBA,CAACrB,EAAE,EAAE;EACnC,OAAON,OAAO,CAAC;IACbG,GAAG,sBAAAI,MAAA,CAAsBD,EAAE,CAAE;IAC7BF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASwB,YAAYA,CAAA,EAAG;EAC7B,OAAO5B,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASyB,kBAAkBA,CAAA,EAAG;EACnC,OAAO7B,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA", "ignoreList": []}]}