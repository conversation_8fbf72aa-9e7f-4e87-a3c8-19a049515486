{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\categories.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\categories.vue", "mtime": 1752630494570}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["waves", "formatDate", "Pagination", "name", "components", "directives", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "status", "parentOptions", "temp", "category_id", "undefined", "parent_id", "category_name", "category_code", "category_desc", "sort_order", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "computed", "treeData", "tree", "map", "for<PERSON>ach", "item", "_objectSpread", "children", "push", "created", "getList", "methods", "_this", "setTimeout", "question_count", "created_at", "length", "filter", "handleFilter", "resetTemp", "handleCreate", "_this2", "$nextTick", "$refs", "clearValidate", "createData", "_this3", "validate", "valid", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this4", "Object", "assign", "updateData", "_this5", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "then"], "sources": ["src/views/questions/categories.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索分类名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加分类\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"category_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"category_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"分类名称\" prop=\"category_name\" min-width=\"200\" />\n      <el-table-column label=\"分类编码\" prop=\"category_code\" min-width=\"150\" />\n      <el-table-column label=\"分类描述\" prop=\"category_desc\" min-width=\"200\" />\n      <el-table-column label=\"题目数量\" prop=\"question_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ formatDate(row.created_at) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父分类\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父分类\" style=\"width: 100%\">\n            <el-option label=\"顶级分类\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.category_id\"\n              :label=\"item.category_name\"\n              :value=\"item.category_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"分类名称\" prop=\"category_name\">\n          <el-input v-model=\"temp.category_name\" />\n        </el-form-item>\n        <el-form-item label=\"分类编码\" prop=\"category_code\">\n          <el-input v-model=\"temp.category_code\" />\n        </el-form-item>\n        <el-form-item label=\"分类描述\" prop=\"category_desc\">\n          <el-input v-model=\"temp.category_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionCategories',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        category_id: undefined,\n        parent_id: 0,\n        category_name: '',\n        category_code: '',\n        category_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑分类',\n        create: '添加分类'\n      },\n      rules: {\n        category_name: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],\n        category_code: [{ required: true, message: '分类编码不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  computed: {\n    treeData() {\n      // 将平铺数据转换为树形结构\n      const tree = []\n      const map = {}\n\n      // 先创建所有节点的映射\n      this.list.forEach(item => {\n        map[item.category_id] = { ...item, children: [] }\n      })\n\n      // 构建树形结构\n      this.list.forEach(item => {\n        if (item.parent_id === 0) {\n          // 顶级分类\n          tree.push(map[item.category_id])\n        } else {\n          // 子分类\n          if (map[item.parent_id]) {\n            map[item.parent_id].children.push(map[item.category_id])\n          }\n        }\n      })\n\n      return tree\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n\n      // 模拟考研政治分类数据\n      setTimeout(() => {\n        this.list = [\n          // 一级分类\n          {\n            category_id: 1,\n            parent_id: 0,\n            category_name: '马克思主义基本原理',\n            category_code: 'marxism',\n            category_desc: '马克思主义基本原理概论',\n            question_count: 245,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 2,\n            parent_id: 0,\n            category_name: '毛泽东思想和中国特色社会主义理论体系概论',\n            category_code: 'maoism',\n            category_desc: '毛泽东思想和中国特色社会主义理论体系概论',\n            question_count: 312,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 3,\n            parent_id: 0,\n            category_name: '中国近现代史纲要',\n            category_code: 'history',\n            category_desc: '中国近现代史纲要',\n            question_count: 198,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 4,\n            parent_id: 0,\n            category_name: '思想道德与法治',\n            category_code: 'morality',\n            category_desc: '思想道德与法治',\n            question_count: 167,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 5,\n            parent_id: 0,\n            category_name: '形势与政策',\n            category_code: 'policy',\n            category_desc: '形势与政策以及当代世界经济与政治',\n            question_count: 123,\n            status: 'active',\n            sort_order: 5,\n            created_at: '2024-01-01'\n          },\n          // 马克思主义基本原理的二级分类\n          {\n            category_id: 11,\n            parent_id: 1,\n            category_name: '马克思主义哲学',\n            category_code: 'marxism_philosophy',\n            category_desc: '马克思主义哲学基本原理',\n            question_count: 78,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 12,\n            parent_id: 1,\n            category_name: '马克思主义政治经济学',\n            category_code: 'marxism_economics',\n            category_desc: '马克思主义政治经济学原理',\n            question_count: 45,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 13,\n            parent_id: 1,\n            category_name: '科学社会主义',\n            category_code: 'scientific_socialism',\n            category_desc: '科学社会主义基本原理',\n            question_count: 33,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          },\n          // 马克思主义哲学的三级分类\n          {\n            category_id: 111,\n            parent_id: 11,\n            category_name: '唯物论',\n            category_code: 'materialism',\n            category_desc: '马克思主义唯物论',\n            question_count: 35,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 112,\n            parent_id: 11,\n            category_name: '辩证法',\n            category_code: 'dialectics',\n            category_desc: '马克思主义辩证法',\n            question_count: 42,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 113,\n            parent_id: 11,\n            category_name: '认识论',\n            category_code: 'epistemology',\n            category_desc: '马克思主义认识论',\n            question_count: 38,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 114,\n            parent_id: 11,\n            category_name: '历史观',\n            category_code: 'historical_materialism',\n            category_desc: '马克思主义历史观',\n            question_count: 32,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-03'\n          },\n          // 唯物论的四级分类\n          {\n            category_id: 1111,\n            parent_id: 111,\n            category_name: '物质概念',\n            category_code: 'matter_concept',\n            category_desc: '物质的哲学概念',\n            question_count: 12,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1112,\n            parent_id: 111,\n            category_name: '意识本质',\n            category_code: 'consciousness_essence',\n            category_desc: '意识的本质和特点',\n            question_count: 15,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1113,\n            parent_id: 111,\n            category_name: '物质与意识关系',\n            category_code: 'matter_consciousness_relation',\n            category_desc: '物质与意识的辩证关系',\n            question_count: 8,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-04'\n          },\n          // 辩证法的四级分类\n          {\n            category_id: 1121,\n            parent_id: 112,\n            category_name: '联系观',\n            category_code: 'connection_view',\n            category_desc: '马克思主义联系观',\n            question_count: 14,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1122,\n            parent_id: 112,\n            category_name: '发展观',\n            category_code: 'development_view',\n            category_desc: '马克思主义发展观',\n            question_count: 16,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1123,\n            parent_id: 112,\n            category_name: '矛盾规律',\n            category_code: 'contradiction_law',\n            category_desc: '对立统一规律',\n            question_count: 12,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-04'\n          },\n          // 毛泽东思想的二级分类\n          {\n            category_id: 21,\n            parent_id: 2,\n            category_name: '毛泽东思想',\n            category_code: 'mao_thought',\n            category_desc: '毛泽东思想的形成和发展',\n            question_count: 89,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 22,\n            parent_id: 2,\n            category_name: '邓小平理论',\n            category_code: 'deng_theory',\n            category_desc: '邓小平理论',\n            question_count: 67,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 23,\n            parent_id: 2,\n            category_name: '三个代表重要思想',\n            category_code: 'three_represents',\n            category_desc: '三个代表重要思想',\n            question_count: 47,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          },\n          // 中国近现代史的二级分类\n          {\n            category_id: 31,\n            parent_id: 3,\n            category_name: '旧民主主义革命时期',\n            category_code: 'old_democratic_revolution',\n            category_desc: '1840-1919年旧民主主义革命时期',\n            question_count: 56,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 32,\n            parent_id: 3,\n            category_name: '新民主主义革命时期',\n            category_code: 'new_democratic_revolution',\n            category_desc: '1919-1949年新民主主义革命时期',\n            question_count: 78,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 33,\n            parent_id: 3,\n            category_name: '社会主义革命和建设时期',\n            category_code: 'socialist_construction',\n            category_desc: '1949年以来社会主义革命和建设时期',\n            question_count: 44,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n\n        // 更新父分类选项（只显示可以作为父级的分类）\n        this.parentOptions = this.list.filter(item => item.parent_id === 0 || item.parent_id === 1 || item.parent_id === 2 || item.parent_id === 3)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        category_id: undefined,\n        parent_id: 0,\n        category_name: '',\n        category_code: '',\n        category_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该分类吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwJA,OAAAA,KAAA;AACA,SAAAC,UAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,aAAA;MACAC,IAAA;QACAC,WAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,aAAA;QACAC,aAAA;QACAC,aAAA;QACAR,MAAA;QACAS,UAAA;MACA;MACAC,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAT,aAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAX,aAAA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,QAAA,WAAAA,SAAA;MACA;MACA,IAAAC,IAAA;MACA,IAAAC,GAAA;;MAEA;MACA,KAAA7B,IAAA,CAAA8B,OAAA,WAAAC,IAAA;QACAF,GAAA,CAAAE,IAAA,CAAArB,WAAA,IAAAsB,aAAA,CAAAA,aAAA,KAAAD,IAAA;UAAAE,QAAA;QAAA;MACA;;MAEA;MACA,KAAAjC,IAAA,CAAA8B,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAnB,SAAA;UACA;UACAgB,IAAA,CAAAM,IAAA,CAAAL,GAAA,CAAAE,IAAA,CAAArB,WAAA;QACA;UACA;UACA,IAAAmB,GAAA,CAAAE,IAAA,CAAAnB,SAAA;YACAiB,GAAA,CAAAE,IAAA,CAAAnB,SAAA,EAAAqB,QAAA,CAAAC,IAAA,CAAAL,GAAA,CAAAE,IAAA,CAAArB,WAAA;UACA;QACA;MACA;MAEA,OAAAkB,IAAA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApC,WAAA;;MAEA;MACAqC,UAAA;QACAD,KAAA,CAAAtC,IAAA;QACA;QACA;UACAU,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA;QACA;QACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA;QACA;QACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA;QACA;QACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA;QACA;QACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA;QACA;QACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA;QACA;QACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,GACA;UACA/B,WAAA;UACAE,SAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAyB,cAAA;UACAjC,MAAA;UACAS,UAAA;UACAyB,UAAA;QACA,EACA;QACAH,KAAA,CAAArC,KAAA,GAAAqC,KAAA,CAAAtC,IAAA,CAAA0C,MAAA;QACAJ,KAAA,CAAApC,WAAA;;QAEA;QACAoC,KAAA,CAAA9B,aAAA,GAAA8B,KAAA,CAAAtC,IAAA,CAAA2C,MAAA,WAAAZ,IAAA;UAAA,OAAAA,IAAA,CAAAnB,SAAA,UAAAmB,IAAA,CAAAnB,SAAA,UAAAmB,IAAA,CAAAnB,SAAA,UAAAmB,IAAA,CAAAnB,SAAA;QAAA;MACA;IACA;IACAgC,YAAA,WAAAA,aAAA;MACA,KAAAzC,SAAA,CAAAC,IAAA;MACA,KAAAgC,OAAA;IACA;IACAS,SAAA,WAAAA,UAAA;MACA,KAAApC,IAAA;QACAC,WAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,aAAA;QACAC,aAAA;QACAC,aAAA;QACAR,MAAA;QACAS,UAAA;MACA;IACA;IACA8B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAA3B,YAAA;MACA,KAAAD,iBAAA;MACA,KAAA+B,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAG,OAAA;YACAC,KAAA;YACAhC,OAAA;YACAiC,IAAA;YACAC,QAAA;UACA;UACAN,MAAA,CAAAnC,iBAAA;QACA;MACA;IACA;IACA0C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAApD,IAAA,GAAAqD,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAA1C,YAAA;MACA,KAAAD,iBAAA;MACA,KAAA+B,SAAA;QACAa,MAAA,CAAAZ,KAAA,aAAAC,aAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAW,MAAA,CAAAV,OAAA;YACAC,KAAA;YACAhC,OAAA;YACAiC,IAAA;YACAC,QAAA;UACA;UACAO,MAAA,CAAAhD,iBAAA;QACA;MACA;IACA;IACAiD,YAAA,WAAAA,aAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAb,IAAA;MACA,GAAAc,IAAA;QACAJ,MAAA,CAAAZ,OAAA;UACAC,KAAA;UACAhC,OAAA;UACAiC,IAAA;UACAC,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}