{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\list.vue?vue&type=style&index=0&id=7a1cb35f&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\list.vue", "mtime": 1752572002945}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYXBwLWNvbnRhaW5lciB7CiAgLmZpbHRlci1jYXJkIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICAuZmlsdGVyLWNvbnRhaW5lciB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtd3JhcDogd3JhcDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgLmZpbHRlci1pdGVtIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICAgICAgfQogICAgfQogIH0KICAuZWwtdGFibGUgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICB9CiAgLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICAgIHBhZGRpbmc6IDE1cHggMDsKICB9Cn0K"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/users", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索用户名、姓名、邮箱\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"正常\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"banned\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加管理员\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"admin_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"用户名\" prop=\"username\" width=\"120\" />\n      <el-table-column label=\"真实姓名\" prop=\"real_name\" width=\"120\" />\n      <el-table-column label=\"邮箱\" prop=\"email\" width=\"200\" />\n      <el-table-column label=\"手机号\" prop=\"phone\" width=\"120\" />\n      <el-table-column label=\"角色\" width=\"150\">\n        <template slot-scope=\"{row}\">\n          <el-tag\n            v-for=\"role in (row.roles || '').split(',')\"\n            :key=\"role\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n          >\n            {{ role }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'danger'\">\n            {{ row.status === 'active' ? '正常' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"最后登录\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.last_login_time | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            v-if=\"row.admin_id !== 1\"\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"temp.username\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"temp.password\" type=\"password\" show-password />\n        </el-form-item>\n        <el-form-item label=\"真实姓名\" prop=\"real_name\">\n          <el-input v-model=\"temp.real_name\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"temp.email\" />\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"temp.phone\" />\n        </el-form-item>\n        <el-form-item label=\"角色\" prop=\"role_ids\">\n          <el-select v-model=\"temp.role_ids\" multiple placeholder=\"请选择角色\">\n            <el-option\n              v-for=\"role in roleOptions\"\n              :key=\"role.role_id\"\n              :label=\"role.role_name\"\n              :value=\"role.role_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"正常\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"banned\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getAdminUserList, createAdminUser, updateAdminUser, deleteAdminUser } from '@/api/users'\nimport { getRoleList } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'UserList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      roleOptions: [],\n      temp: {\n        admin_id: undefined,\n        username: '',\n        password: '',\n        real_name: '',\n        email: '',\n        phone: '',\n        role_ids: [],\n        status: 'active'\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑管理员',\n        create: '添加管理员'\n      },\n      rules: {\n        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],\n        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getRoleOptions()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getAdminUserList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n      })\n    },\n    getRoleOptions() {\n      getRoleList({ pageSize: 100 }).then(response => {\n        this.roleOptions = response.data.list\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        admin_id: undefined,\n        username: '',\n        password: '',\n        real_name: '',\n        email: '',\n        phone: '',\n        role_ids: [],\n        status: 'active'\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createAdminUser(this.temp).then(() => {\n            this.list.unshift(this.temp)\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.temp.password = ''\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          if (!tempData.password) {\n            delete tempData.password\n          }\n          updateAdminUser(tempData.admin_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该管理员吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteAdminUser(row.admin_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}