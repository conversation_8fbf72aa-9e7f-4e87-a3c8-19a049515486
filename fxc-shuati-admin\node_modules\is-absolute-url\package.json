{"_from": "is-absolute-url@^2.0.0", "_id": "is-absolute-url@2.1.0", "_inBundle": false, "_integrity": "sha512-vOx7VprsKyllwjSkLV79NIhpyLfr3jAp7VaTCMXOJHu4m0Ew1CZ2fcjASwmV1jI3BWuWHB013M48eyeldk9gYg==", "_location": "/is-absolute-url", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-absolute-url@^2.0.0", "name": "is-absolute-url", "escapedName": "is-absolute-url", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/postcss-normalize-url"], "_resolved": "https://registry.npmmirror.com/is-absolute-url/-/is-absolute-url-2.1.0.tgz", "_shasum": "50530dfb84fcc9aa7dbe7852e83a37b93b9f2aa6", "_spec": "is-absolute-url@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-normalize-url", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-absolute-url/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if an URL is absolute", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-absolute-url#readme", "keywords": ["url", "absolute", "relative", "uri", "is", "check"], "license": "MIT", "name": "is-absolute-url", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-absolute-url.git"}, "scripts": {"test": "mocha"}, "version": "2.1.0"}