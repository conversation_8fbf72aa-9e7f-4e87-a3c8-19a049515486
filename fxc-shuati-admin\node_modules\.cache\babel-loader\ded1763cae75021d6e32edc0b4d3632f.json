{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\list.vue", "mtime": 1752631122937}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcyc7CmltcG9ydCB7IGZvcm1hdERhdGUgfSBmcm9tICdAL3V0aWxzJzsKaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24nOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1F1ZXN0aW9uTGlzdCcsCiAgY29tcG9uZW50czogewogICAgUGFnaW5hdGlvbjogUGFnaW5hdGlvbgogIH0sCiAgZGlyZWN0aXZlczogewogICAgd2F2ZXM6IHdhdmVzCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHF1ZXN0aW9uX3R5cGU6ICcnLAogICAgICAgIGRpZmZpY3VsdHk6ICcnLAogICAgICAgIGNhdGVnb3J5X2lkOiAnJywKICAgICAgICBzdGF0dXM6ICcnCiAgICAgIH0sCiAgICAgIGNhdGVnb3J5T3B0aW9uczogW10sCiAgICAgIHZpZXdEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudFF1ZXN0aW9uOiBudWxsLAogICAgICB0eXBlVGFnTWFwOiB7CiAgICAgICAgc2luZ2xlX2Nob2ljZTogJ3ByaW1hcnknLAogICAgICAgIG11bHRpcGxlX2Nob2ljZTogJ3N1Y2Nlc3MnLAogICAgICAgIHRydWVfZmFsc2U6ICd3YXJuaW5nJywKICAgICAgICBmaWxsX2JsYW5rOiAnaW5mbycsCiAgICAgICAgc2hvcnRfYW5zd2VyOiAnZGFuZ2VyJywKICAgICAgICBlc3NheTogJ2RhbmdlcicKICAgICAgfSwKICAgICAgdHlwZVRleHRNYXA6IHsKICAgICAgICBzaW5nbGVfY2hvaWNlOiAn5Y2V6YCJ6aKYJywKICAgICAgICBtdWx0aXBsZV9jaG9pY2U6ICflpJrpgInpopgnLAogICAgICAgIHRydWVfZmFsc2U6ICfliKTmlq3popgnLAogICAgICAgIGZpbGxfYmxhbms6ICfloavnqbrpopgnLAogICAgICAgIHNob3J0X2Fuc3dlcjogJ+eugOetlOmimCcsCiAgICAgICAgZXNzYXk6ICforrrov7DpopgnCiAgICAgIH0sCiAgICAgIGRpZmZpY3VsdHlUYWdNYXA6IHsKICAgICAgICBlYXN5OiAnc3VjY2VzcycsCiAgICAgICAgbWVkaXVtOiAnd2FybmluZycsCiAgICAgICAgaGFyZDogJ2RhbmdlcicKICAgICAgfSwKICAgICAgZGlmZmljdWx0eVRleHRNYXA6IHsKICAgICAgICBlYXN5OiAn566A5Y2VJywKICAgICAgICBtZWRpdW06ICfkuK3nrYknLAogICAgICAgIGhhcmQ6ICflm7Dpmr4nCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmxvYWRDYXRlZ29yeU9wdGlvbnMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwoKICAgICAgLy8g5qih5ouf6aKY55uu5pWw5o2uCiAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmxpc3QgPSBbCiAgICAgICAgLy8g6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGIC0g5ZOy5a2m6YOo5YiGCiAgICAgICAgewogICAgICAgICAgcXVlc3Rpb25faWQ6IDEsCiAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn6ams5YWL5oCd5Li75LmJ5ZOy5a2m55qE5Z+65pys6Zeu6aKY5piv77yI77yJJywKICAgICAgICAgIHF1ZXN0aW9uX3R5cGU6ICdzaW5nbGVfY2hvaWNlJywKICAgICAgICAgIGRpZmZpY3VsdHk6ICdtZWRpdW0nLAogICAgICAgICAgY2F0ZWdvcnlfaWQ6IDExMTEsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn54mp6LSo5qaC5b+1JywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogMSwKICAgICAgICAgICAgdGFnX25hbWU6ICfpqazlhYvmgJ3kuLvkuYnln7rmnKzljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogOSwKICAgICAgICAgICAgdGFnX25hbWU6ICflk7Llrabljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogNywKICAgICAgICAgICAgdGFnX25hbWU6ICfpq5jpopHogIPngrknCiAgICAgICAgICB9XSwKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfnianotKjlkozmhI/or4bnmoTlhbPns7vpl67popgnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfnkIborrrlkozlrp7ot7XnmoTlhbPns7vpl67popgnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfkuKrkurrlkoznpL7kvJrnmoTlhbPns7vpl67popgnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfoh6rnlLHlkozlv4XnhLbnmoTlhbPns7vpl67popgnCiAgICAgICAgICB9XSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQScsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+mprOWFi+aAneS4u+S5ieWTsuWtpueahOWfuuacrOmXrumimOaYr+eJqei0qOWSjOaEj+ivhueahOWFs+ezu+mXrumimO+8jOi/meaYr+WTsuWtpueahOagueacrOmXrumimOOAguWug+WMheaLrOS4pOS4quaWuemdou+8muesrOS4gO+8jOeJqei0qOWSjOaEj+ivhuS9leiAheS4uuesrOS4gOaAp++8m+esrOS6jO+8jOeJqei0qOWSjOaEj+ivhuaYr+WQpuWFt+acieWQjOS4gOaAp+OAgicsCiAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNScKICAgICAgICB9LCB7CiAgICAgICAgICBxdWVzdGlvbl9pZDogMiwKICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfnianotKjnmoTllK/kuIDnibnmgKfmmK/vvIjvvIknLAogICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgZGlmZmljdWx0eTogJ2Vhc3knLAogICAgICAgICAgY2F0ZWdvcnlfaWQ6IDExMTEsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn54mp6LSo5qaC5b+1JywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogMSwKICAgICAgICAgICAgdGFnX25hbWU6ICfpqazlhYvmgJ3kuLvkuYnln7rmnKzljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogOSwKICAgICAgICAgICAgdGFnX25hbWU6ICflk7Llrabljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogMTYsCiAgICAgICAgICAgIHRhZ19uYW1lOiAnMjAyM+W5tOecn+mimCcKICAgICAgICAgIH1dLAogICAgICAgICAgb3B0aW9uczogW3sKICAgICAgICAgICAgY29udGVudDogJ+i/kOWKqOaApycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgY29udGVudDogJ+WuouinguWunuWcqOaApycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgY29udGVudDogJ+WPr+efpeaApycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgY29udGVudDogJ+e7neWvueaApycKICAgICAgICAgIH1dLAogICAgICAgICAgY29ycmVjdF9hbnN3ZXI6ICdCJywKICAgICAgICAgIGV4cGxhbmF0aW9uOiAn54mp6LSo55qE5ZSv5LiA54m55oCn5piv5a6i6KeC5a6e5Zyo5oCn44CC6L+Z5piv54mp6LSo5qaC5b+155qE5qC45b+D77yM5oyH54mp6LSo5LiN5L6d6LWW5LqO5Lq655qE5oSP6K+G6ICM5a2Y5Zyo77yM5bm26IO95Li65Lq655qE5oSP6K+G5omA5Y+N5pig44CCJywKICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1JwogICAgICAgIH0sIHsKICAgICAgICAgIHF1ZXN0aW9uX2lkOiAzLAogICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+aEj+ivhueahOacrOi0qOaYr++8iO+8iScsCiAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnbXVsdGlwbGVfY2hvaWNlJywKICAgICAgICAgIGRpZmZpY3VsdHk6ICdtZWRpdW0nLAogICAgICAgICAgY2F0ZWdvcnlfaWQ6IDExMTIsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5oSP6K+G5pys6LSoJywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogMSwKICAgICAgICAgICAgdGFnX25hbWU6ICfpqazlhYvmgJ3kuLvkuYnln7rmnKzljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogOSwKICAgICAgICAgICAgdGFnX25hbWU6ICflk7Llrabljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogNywKICAgICAgICAgICAgdGFnX25hbWU6ICfpq5jpopHogIPngrknCiAgICAgICAgICB9XSwKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmhI/or4bmmK/kurrohJHnmoTmnLrog70nCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmhI/or4bmmK/lrqLop4LlrZjlnKjnmoTlj43mmKAnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmhI/or4bmmK/npL7kvJrnmoTkuqfniaknCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmhI/or4blhbfmnInkuLvop4Log73liqjmgKcnCiAgICAgICAgICB9XSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQSxCLEMsRCcsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+aEj+ivhueahOacrOi0qOWMheaLrO+8mu+8iDHvvInmhI/or4bmmK/kurrohJHnmoTmnLrog73vvJvvvIgy77yJ5oSP6K+G5piv5a6i6KeC5a2Y5Zyo55qE5Y+N5pig77yb77yIM++8ieaEj+ivhuaYr+ekvuS8mueahOS6p+eJqe+8m++8iDTvvInmhI/or4blhbfmnInkuLvop4Log73liqjmgKfjgILov5nlm5vkuKrmlrnpnaLmnoTmiJDkuobmhI/or4bmnKzotKjnmoTlrozmlbTlhoXlrrnjgIInLAogICAgICAgICAgc2NvcmU6IDIsCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTUnCiAgICAgICAgfSwgewogICAgICAgICAgcXVlc3Rpb25faWQ6IDQsCiAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn6ams5YWL5oCd5Li75LmJ5ZOy5a2m6K6k5Li677yM5LiW55WM55qE55yf5q2j57uf5LiA5oCn5Zyo5LqO5a6D55qE54mp6LSo5oCn44CCJywKICAgICAgICAgIHF1ZXN0aW9uX3R5cGU6ICd0cnVlX2ZhbHNlJywKICAgICAgICAgIGRpZmZpY3VsdHk6ICdlYXN5JywKICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTExLAogICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+eJqei0qOamguW/tScsCiAgICAgICAgICB0YWdzOiBbewogICAgICAgICAgICB0YWdfaWQ6IDEsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGJwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDksCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5ZOy5a2m5Y6f55CGJwogICAgICAgICAgfV0sCiAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAndHJ1ZScsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+ato+ehruOAgumprOWFi+aAneS4u+S5ieWTsuWtpuiupOS4uu+8jOS4lueVjOeahOecn+ato+e7n+S4gOaAp+WcqOS6juWug+eahOeJqei0qOaAp+OAgui/meaYr+mprOWFi+aAneS4u+S5ieS4gOWFg+iuuueahOWfuuacrOingueCue+8jOW8uuiwg+S4lueVjOS4h+eJqemDveaYr+eJqei0qOeahOS4jeWQjOihqOeOsOW9ouW8j+OAgicsCiAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNScKICAgICAgICB9LCB7CiAgICAgICAgICBxdWVzdGlvbl9pZDogNSwKICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfogZTns7vnmoTnibnngrnljIXmi6zvvIjvvIknLAogICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ211bHRpcGxlX2Nob2ljZScsCiAgICAgICAgICBkaWZmaWN1bHR5OiAnbWVkaXVtJywKICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTIxLAogICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+iBlOezu+ingicsCiAgICAgICAgICB0YWdzOiBbewogICAgICAgICAgICB0YWdfaWQ6IDEsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGJwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDksCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5ZOy5a2m5Y6f55CGJwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDcsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6auY6aKR6ICD54K5JwogICAgICAgICAgfV0sCiAgICAgICAgICBvcHRpb25zOiBbewogICAgICAgICAgICBjb250ZW50OiAn5a6i6KeC5oCnJwogICAgICAgICAgfSwgewogICAgICAgICAgICBjb250ZW50OiAn5pmu6YGN5oCnJwogICAgICAgICAgfSwgewogICAgICAgICAgICBjb250ZW50OiAn5aSa5qC35oCnJwogICAgICAgICAgfSwgewogICAgICAgICAgICBjb250ZW50OiAn5p2h5Lu25oCnJwogICAgICAgICAgfV0sCiAgICAgICAgICBjb3JyZWN0X2Fuc3dlcjogJ0EsQixDLEQnLAogICAgICAgICAgZXhwbGFuYXRpb246ICfogZTns7vlhbfmnInlrqLop4LmgKfjgIHmma7pgY3mgKfjgIHlpJrmoLfmgKflkozmnaHku7bmgKfnrYnnibnngrnjgILlrqLop4LmgKfmjIfogZTns7vmmK/kuovnianmnKzouqvmiYDlm7rmnInnmoTvvJvmma7pgY3mgKfmjIfku7vkvZXkuovnianpg73lpITlnKjogZTns7vkuYvkuK3vvJvlpJrmoLfmgKfmjIfogZTns7vnmoTlvaLlvI/lpJrnp43lpJrmoLfvvJvmnaHku7bmgKfmjIfogZTns7vmmK/mnInmnaHku7bnmoTjgIInLAogICAgICAgICAgc2NvcmU6IDIsCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTUnCiAgICAgICAgfSwgewogICAgICAgICAgcXVlc3Rpb25faWQ6IDYsCiAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn55+b55u+55qE5Z+65pys5bGe5oCn5pivX19fX1/lkoxfX19fX+OAgicsCiAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnZmlsbF9ibGFuaycsCiAgICAgICAgICBkaWZmaWN1bHR5OiAnZWFzeScsCiAgICAgICAgICBjYXRlZ29yeV9pZDogMTEyMywKICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfnn5vnm77op4TlvosnLAogICAgICAgICAgdGFnczogW3sKICAgICAgICAgICAgdGFnX2lkOiAxLAogICAgICAgICAgICB0YWdfbmFtZTogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhicKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgdGFnX2lkOiA5LAogICAgICAgICAgICB0YWdfbmFtZTogJ+WTsuWtpuWOn+eQhicKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgdGFnX2lkOiA3LAogICAgICAgICAgICB0YWdfbmFtZTogJ+mrmOmikeiAg+eCuScKICAgICAgICAgIH1dLAogICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICBjb3JyZWN0X2Fuc3dlcjogJ+WQjOS4gOaAp++8m+aWl+S6ieaApycsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+efm+ebvueahOWfuuacrOWxnuaAp+aYr+WQjOS4gOaAp+WSjOaWl+S6ieaAp+OAguWQjOS4gOaAp+aYr+aMh+efm+ebvuWPjOaWueebuOS6kuS+neWtmOOAgeebuOS6kui0r+mAmueahOaAp+i0qOWSjOi2i+WKv++8m+aWl+S6ieaAp+aYr+aMh+efm+ebvuWPjOaWueebuOS6kuaOkuaWpeOAgeebuOS6kuWvueeri+eahOaAp+i0qOWSjOi2i+WKv+OAgicsCiAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNScKICAgICAgICB9LCB7CiAgICAgICAgICBxdWVzdGlvbl9pZDogNywKICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICflrp7ot7XmmK/orqTor4bnmoTln7rnoYDvvIzkuLvopoHooajnjrDlnKjvvIjvvIknLAogICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ211bHRpcGxlX2Nob2ljZScsCiAgICAgICAgICBkaWZmaWN1bHR5OiAnbWVkaXVtJywKICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTMsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn6K6k6K+G6K66JywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogMSwKICAgICAgICAgICAgdGFnX25hbWU6ICfpqazlhYvmgJ3kuLvkuYnln7rmnKzljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogOSwKICAgICAgICAgICAgdGFnX25hbWU6ICflk7Llrabljp/nkIYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogNiwKICAgICAgICAgICAgdGFnX25hbWU6ICfph43ngrnpmr7ngrknCiAgICAgICAgICB9XSwKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflrp7ot7XmmK/orqTor4bnmoTmnaXmupAnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflrp7ot7XmmK/orqTor4blj5HlsZXnmoTliqjlipsnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflrp7ot7XmmK/mo4DpqozorqTor4bnnJ/nkIbmgKfnmoTllK/kuIDmoIflh4YnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflrp7ot7XmmK/orqTor4bnmoTnm67nmoQnCiAgICAgICAgICB9XSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQSxCLEMsRCcsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+Wunui3teaYr+iupOivhueahOWfuuehgO+8jOS4u+imgeihqOeOsOWcqOWbm+S4quaWuemdou+8mu+8iDHvvInlrp7ot7XmmK/orqTor4bnmoTmnaXmupDvvJvvvIgy77yJ5a6e6Le15piv6K6k6K+G5Y+R5bGV55qE5Yqo5Yqb77yb77yIM++8ieWunui3teaYr+ajgOmqjOiupOivhuecn+eQhuaAp+eahOWUr+S4gOagh+WHhu+8m++8iDTvvInlrp7ot7XmmK/orqTor4bnmoTnm67nmoTjgIInLAogICAgICAgICAgc2NvcmU6IDIsCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTUnCiAgICAgICAgfSwKICAgICAgICAvLyDmr5vms73kuJzmgJ3mg7Ppg6jliIYKICAgICAgICB7CiAgICAgICAgICBxdWVzdGlvbl9pZDogOCwKICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfmr5vms73kuJzmgJ3mg7PlvaLmiJDnmoTml7bku6Pog4zmma/mmK/vvIjvvIknLAogICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgZGlmZmljdWx0eTogJ21lZGl1bScsCiAgICAgICAgICBjYXRlZ29yeV9pZDogMjEsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5q+b5rO95Lic5oCd5oOzJywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogMiwKICAgICAgICAgICAgdGFnX25hbWU6ICfmr5vms73kuJzmgJ3mg7MnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogNywKICAgICAgICAgICAgdGFnX25hbWU6ICfpq5jpopHogIPngrknCiAgICAgICAgICB9XSwKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfluJ3lm73kuLvkuYnlkozml6DkuqfpmLbnuqfpnanlkb3nmoTml7bku6MnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfotYTmnKzkuLvkuYnlkJHnpL7kvJrkuLvkuYnov4fmuKHnmoTml7bku6MnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflkozlubPkuI7lj5HlsZXnmoTml7bku6MnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflhajnkIPljJbnmoTml7bku6MnCiAgICAgICAgICB9XSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQScsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+avm+azveS4nOaAneaDs+W9ouaIkOeahOaXtuS7o+iDjOaZr+aYr+W4neWbveS4u+S5ieWSjOaXoOS6p+mYtue6p+mdqeWRveeahOaXtuS7o+OAgui/meS4gOaXtuS7o+eJueW+geS4uuavm+azveS4nOaAneaDs+eahOW9ouaIkOWSjOWPkeWxleaPkOS+m+S6humHjeimgeeahOWOhuWPsuadoeS7tuOAgicsCiAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNicKICAgICAgICB9LCB7CiAgICAgICAgICBxdWVzdGlvbl9pZDogOSwKICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfmlrDmsJHkuLvkuLvkuYnpnanlkb3nmoTmgLvot6/nur/mmK/vvIjvvIknLAogICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgZGlmZmljdWx0eTogJ2hhcmQnLAogICAgICAgICAgY2F0ZWdvcnlfaWQ6IDIxLAogICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+avm+azveS4nOaAneaDsycsCiAgICAgICAgICB0YWdzOiBbewogICAgICAgICAgICB0YWdfaWQ6IDIsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5q+b5rO95Lic5oCd5oOzJwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDEyLAogICAgICAgICAgICB0YWdfbmFtZTogJ+aWsOawkeS4u+S4u+S5iemdqeWRvScKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgdGFnX2lkOiA2LAogICAgICAgICAgICB0YWdfbmFtZTogJ+mHjeeCuemavueCuScKICAgICAgICAgIH1dLAogICAgICAgICAgb3B0aW9uczogW3sKICAgICAgICAgICAgY29udGVudDogJ+aXoOS6p+mYtue6p+mihuWvvOeahO+8jOS6uuawkeWkp+S8l+eahO+8jOWPjeWvueW4neWbveS4u+S5ieOAgeWwgeW7uuS4u+S5ieWSjOWumOWDmui1hOacrOS4u+S5ieeahOmdqeWRvScKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgY29udGVudDogJ+W3peS6uumYtue6p+mihuWvvOeahO+8jOS7peW3peWGnOiBlOebn+S4uuWfuuehgOeahOS6uuawkeawkeS4u+S4k+aUvycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgY29udGVudDogJ+S4reWbveWFseS6p+WFmumihuWvvOeahOWkmuWFmuWQiOS9nOWSjOaUv+ayu+WNj+WVhuWItuW6picKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgY29udGVudDogJ+S6uuawkeW9k+WutuS9nOS4u+eahOekvuS8muS4u+S5ieawkeS4u+aUv+ayuycKICAgICAgICAgIH1dLAogICAgICAgICAgY29ycmVjdF9hbnN3ZXI6ICdBJywKICAgICAgICAgIGV4cGxhbmF0aW9uOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG955qE5oC76Lev57q/5piv77ya5peg5Lqn6Zi257qn6aKG5a+855qE77yM5Lq65rCR5aSn5LyX55qE77yM5Y+N5a+55bid5Zu95Li75LmJ44CB5bCB5bu65Li75LmJ5ZKM5a6Y5YOa6LWE5pys5Li75LmJ55qE6Z2p5ZG944CC6L+Z5piv5q+b5rO95Lic5a+55paw5rCR5Li75Li75LmJ6Z2p5ZG95pys6LSo55qE56eR5a2m5qaC5ous44CCJywKICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE2JwogICAgICAgIH0sIHsKICAgICAgICAgIHF1ZXN0aW9uX2lkOiAxMCwKICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfkuK3lm73pnanlkb3nmoTln7rmnKzpl67popjmmK/vvIjvvIknLAogICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgZGlmZmljdWx0eTogJ21lZGl1bScsCiAgICAgICAgICBjYXRlZ29yeV9pZDogMjEsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5q+b5rO95Lic5oCd5oOzJywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogMiwKICAgICAgICAgICAgdGFnX25hbWU6ICfmr5vms73kuJzmgJ3mg7MnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogMTIsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG9JwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDE3LAogICAgICAgICAgICB0YWdfbmFtZTogJzIwMjLlubTnnJ/popgnCiAgICAgICAgICB9XSwKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflhpzmsJHpl67popgnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmraboo4Xmlpfkuonpl67popgnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfnu5/kuIDmiJjnur/pl67popgnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflhZrnmoTlu7rorr7pl67popgnCiAgICAgICAgICB9XSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQScsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+S4reWbvemdqeWRveeahOWfuuacrOmXrumimOaYr+WGnOawkemXrumimOOAguavm+azveS4nOaMh+WHuu+8jOWGnOawkemXrumimOS5g+WbveawkemdqeWRveeahOS4reW/g+mXrumimO+8jOWGnOawkeaYr+S4reWbvemdqeWRveeahOS4u+WKm+WGm++8jOWGnOawkemXrumimOaYr+S4reWbvemdqeWRveeahOWfuuacrOmXrumimOOAgicsCiAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNicKICAgICAgICB9LAogICAgICAgIC8vIOS4reWbvei/keeOsOS7o+WPsue6suimgemDqOWIhgogICAgICAgIHsKICAgICAgICAgIHF1ZXN0aW9uX2lkOiAxMSwKICAgICAgICAgIHF1ZXN0aW9uX2NvbnRlbnQ6ICfkuK3lm73ov5Hku6Plj7LnmoTotbfngrnmmK/vvIjvvIknLAogICAgICAgICAgcXVlc3Rpb25fdHlwZTogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgZGlmZmljdWx0eTogJ2Vhc3knLAogICAgICAgICAgY2F0ZWdvcnlfaWQ6IDMxLAogICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+aXp+awkeS4u+S4u+S5iemdqeWRveaXtuacnycsCiAgICAgICAgICB0YWdzOiBbewogICAgICAgICAgICB0YWdfaWQ6IDMsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5Lit5Zu96L+R546w5Luj5Y+y57qy6KaBJwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDcsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6auY6aKR6ICD54K5JwogICAgICAgICAgfV0sCiAgICAgICAgICBvcHRpb25zOiBbewogICAgICAgICAgICBjb250ZW50OiAnMTg0MOW5tOm4pueJh+aImOS6iScKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgY29udGVudDogJzE4NDLlubTjgIrljZfkuqzmnaHnuqbjgIvnrb7orqInCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICcxODUx5bm05aSq5bmz5aSp5Zu96L+Q5YqoJwogICAgICAgICAgfSwgewogICAgICAgICAgICBjb250ZW50OiAnMTg5NOW5tOeUsuWNiOS4reaXpeaImOS6iScKICAgICAgICAgIH1dLAogICAgICAgICAgY29ycmVjdF9hbnN3ZXI6ICdBJywKICAgICAgICAgIGV4cGxhbmF0aW9uOiAn5Lit5Zu96L+R5Luj5Y+y55qE6LW354K55pivMTg0MOW5tOm4pueJh+aImOS6ieOAgum4pueJh+aImOS6ieaYr+S4reWbveWOhuWPsueahOi9rOaKmOeCue+8jOagh+W/l+edgOS4reWbveW8gOWni+aypuS4uuWNiuauluawkeWcsOWNiuWwgeW7uuekvuS8mu+8jOS4reWbvei/keS7o+WPsueUseatpOW8gOWni+OAgicsCiAgICAgICAgICBzY29yZTogMiwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNycKICAgICAgICB9LCB7CiAgICAgICAgICBxdWVzdGlvbl9pZDogMTIsCiAgICAgICAgICBxdWVzdGlvbl9jb250ZW50OiAn5LqU5Zub6L+Q5Yqo55qE5Y6G5Y+y5oSP5LmJ5YyF5ous77yI77yJJywKICAgICAgICAgIHF1ZXN0aW9uX3R5cGU6ICdtdWx0aXBsZV9jaG9pY2UnLAogICAgICAgICAgZGlmZmljdWx0eTogJ21lZGl1bScsCiAgICAgICAgICBjYXRlZ29yeV9pZDogMzIsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG95pe25pyfJywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogMywKICAgICAgICAgICAgdGFnX25hbWU6ICfkuK3lm73ov5HnjrDku6Plj7LnurLopoEnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogMTIsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG9JwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDcsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6auY6aKR6ICD54K5JwogICAgICAgICAgfV0sCiAgICAgICAgICBvcHRpb25zOiBbewogICAgICAgICAgICBjb250ZW50OiAn5piv5Lit5Zu95paw5rCR5Li75Li75LmJ6Z2p5ZG955qE5byA56uvJwogICAgICAgICAgfSwgewogICAgICAgICAgICBjb250ZW50OiAn5L+D6L+b5LqG6ams5YWL5oCd5Li75LmJ5Zyo5Lit5Zu955qE5Lyg5pKtJwogICAgICAgICAgfSwgewogICAgICAgICAgICBjb250ZW50OiAn5L+D6L+b5LqG6ams5YWL5oCd5Li75LmJ5LiO5Lit5Zu95bel5Lq66L+Q5Yqo55qE57uT5ZCIJwogICAgICAgICAgfSwgewogICAgICAgICAgICBjb250ZW50OiAn5Li65Lit5Zu95YWx5Lqn5YWa55qE5oiQ56uL5L2c5LqG5oCd5oOz5ZKM5bmy6YOo5YeG5aSHJwogICAgICAgICAgfV0sCiAgICAgICAgICBjb3JyZWN0X2Fuc3dlcjogJ0EsQixDLEQnLAogICAgICAgICAgZXhwbGFuYXRpb246ICfkupTlm5vov5DliqjnmoTljoblj7LmhI/kuYnph43lpKfvvJrvvIgx77yJ5piv5Lit5Zu95paw5rCR5Li75Li75LmJ6Z2p5ZG955qE5byA56uv77yb77yIMu+8ieS/g+i/m+S6humprOWFi+aAneS4u+S5ieWcqOS4reWbveeahOS8oOaSre+8m++8iDPvvInkv4Pov5vkuobpqazlhYvmgJ3kuLvkuYnkuI7kuK3lm73lt6Xkurrov5DliqjnmoTnu5PlkIjvvJvvvIg077yJ5Li65Lit5Zu95YWx5Lqn5YWa55qE5oiQ56uL5L2c5LqG5oCd5oOz5ZKM5bmy6YOo5YeG5aSH44CCJywKICAgICAgICAgIHNjb3JlOiAyLAogICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE3JwogICAgICAgIH0sCiAgICAgICAgLy8g5oCd5oOz6YGT5b635LiO5rOV5rK76YOo5YiGCiAgICAgICAgewogICAgICAgICAgcXVlc3Rpb25faWQ6IDEzLAogICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+eQhuaDs+S/oeW/teeahOS9nOeUqOS4u+imgeihqOeOsOWcqO+8iO+8iScsCiAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnbXVsdGlwbGVfY2hvaWNlJywKICAgICAgICAgIGRpZmZpY3VsdHk6ICdtZWRpdW0nLAogICAgICAgICAgY2F0ZWdvcnlfaWQ6IDQsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5oCd5oOz6YGT5b635LiO5rOV5rK7JywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogNCwKICAgICAgICAgICAgdGFnX25hbWU6ICfmgJ3mg7PpgZPlvrfkuI7ms5XmsrsnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogNywKICAgICAgICAgICAgdGFnX25hbWU6ICfpq5jpopHogIPngrknCiAgICAgICAgICB9XSwKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmjIflvJXkurrnlJ/nmoTlpYvmlpfnm67moIcnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmj5DkvpvkurrnlJ/nmoTliY3ov5vliqjlipsnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICfmj5Dpq5jkurrnlJ/nmoTnsr7npZ7looPnlYwnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICflop7lvLrkurrnlJ/nmoTkvb/lkb3mhJ8nCiAgICAgICAgICB9XSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAnQSxCLEMnLAogICAgICAgICAgZXhwbGFuYXRpb246ICfnkIbmg7Pkv6Hlv7XnmoTkvZznlKjkuLvopoHooajnjrDlnKjkuInkuKrmlrnpnaLvvJrvvIgx77yJ5oyH5byV5Lq655Sf55qE5aWL5paX55uu5qCH77yb77yIMu+8ieaPkOS+m+S6uueUn+eahOWJjei/m+WKqOWKm++8m++8iDPvvInmj5Dpq5jkurrnlJ/nmoTnsr7npZ7looPnlYzjgILnkIbmg7Pkv6Hlv7XmmK/kurrnlJ/nmoTnsr7npZ7mlK/mn7HlkozliY3ov5vliqjlipvjgIInLAogICAgICAgICAgc2NvcmU6IDIsCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTgnCiAgICAgICAgfSwgewogICAgICAgICAgcXVlc3Rpb25faWQ6IDE0LAogICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+ekvuS8muS4u+S5ieaguOW/g+S7t+WAvOingueahOWfuuacrOWGheWuueaYr19fX19f44CBX19fX1/jgIFfX19fX+OAgicsCiAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnZmlsbF9ibGFuaycsCiAgICAgICAgICBkaWZmaWN1bHR5OiAnZWFzeScsCiAgICAgICAgICBjYXRlZ29yeV9pZDogNCwKICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfmgJ3mg7PpgZPlvrfkuI7ms5XmsrsnLAogICAgICAgICAgdGFnczogW3sKICAgICAgICAgICAgdGFnX2lkOiA0LAogICAgICAgICAgICB0YWdfbmFtZTogJ+aAneaDs+mBk+W+t+S4juazleayuycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgdGFnX2lkOiAxNSwKICAgICAgICAgICAgdGFnX25hbWU6ICfmlrDml7bku6MnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogNywKICAgICAgICAgICAgdGFnX25hbWU6ICfpq5jpopHogIPngrknCiAgICAgICAgICB9XSwKICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgY29ycmVjdF9hbnN3ZXI6ICflr4zlvLrjgIHmsJHkuLvjgIHmlofmmI7jgIHlkozosJDvvJvoh6rnlLHjgIHlubPnrYnjgIHlhazmraPjgIHms5XmsrvvvJvniLHlm73jgIHmlazkuJrjgIHor5rkv6HjgIHlj4vlloQnLAogICAgICAgICAgZXhwbGFuYXRpb246ICfnpL7kvJrkuLvkuYnmoLjlv4Pku7flgLzop4LnmoTln7rmnKzlhoXlrrnmmK/vvJrlm73lrrblsYLpnaLnmoTku7flgLzopoHmsYLmmK/lr4zlvLrjgIHmsJHkuLvjgIHmlofmmI7jgIHlkozosJDvvJvnpL7kvJrlsYLpnaLnmoTku7flgLzopoHmsYLmmK/oh6rnlLHjgIHlubPnrYnjgIHlhazmraPjgIHms5XmsrvvvJvkuKrkurrlsYLpnaLnmoTku7flgLzopoHmsYLmmK/niLHlm73jgIHmlazkuJrjgIHor5rkv6HjgIHlj4vlloTjgIInLAogICAgICAgICAgc2NvcmU6IDMsCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTgnCiAgICAgICAgfSwgewogICAgICAgICAgcXVlc3Rpb25faWQ6IDE1LAogICAgICAgICAgcXVlc3Rpb25fY29udGVudDogJ+ivt+iuuui/sOaWsOaXtuS7o+eIseWbveS4u+S5ieeahOWfuuacrOimgeaxguOAgicsCiAgICAgICAgICBxdWVzdGlvbl90eXBlOiAnZXNzYXknLAogICAgICAgICAgZGlmZmljdWx0eTogJ2hhcmQnLAogICAgICAgICAgY2F0ZWdvcnlfaWQ6IDQsCiAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5oCd5oOz6YGT5b635LiO5rOV5rK7JywKICAgICAgICAgIHRhZ3M6IFt7CiAgICAgICAgICAgIHRhZ19pZDogNCwKICAgICAgICAgICAgdGFnX25hbWU6ICfmgJ3mg7PpgZPlvrfkuI7ms5XmsrsnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIHRhZ19pZDogMTUsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5paw5pe25LujJwogICAgICAgICAgfSwgewogICAgICAgICAgICB0YWdfaWQ6IDYsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6YeN54K56Zq+54K5JwogICAgICAgICAgfV0sCiAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgIGNvcnJlY3RfYW5zd2VyOiAn5paw5pe25Luj54ix5Zu95Li75LmJ55qE5Z+65pys6KaB5rGC5YyF5ous77ya77yIMe+8ieWdmuaMgeeIseWbveWSjOeIseWFmuOAgeeIseekvuS8muS4u+S5ieebuOe7n+S4gO+8m++8iDLvvInnu7TmiqTnpZblm73nu5/kuIDlkozmsJHml4/lm6Lnu5PvvJvvvIgz77yJ5bCK6YeN5ZKM5Lyg5om/5Lit5Y2O5rCR5peP5Y6G5Y+y5ZKM5paH5YyW77yb77yINO+8ieWdmuaMgeeri+i2s+awkeaXj+WPiOmdouWQkeS4lueVjOOAguaWsOaXtuS7o+eahOeIseWbveS4u+S5ieW/hemhu+WdmuaMgeeIseWbveWSjOeIseWFmuOAgeeIseekvuS8muS4u+S5ieebuOe7n+S4gO+8jOi/meaYr+W9k+S7o+S4reWbveeIseWbveS4u+S5ieeyvuelnuacgOmHjeimgeeahOS9k+eOsOOAgicsCiAgICAgICAgICBleHBsYW5hdGlvbjogJ+i/memBk+mimOiAg+afpeaWsOaXtuS7o+eIseWbveS4u+S5ieeahOWfuuacrOimgeaxguOAguetlOmimOimgeeCue+8mu+8iDHvvInniLHlm73lkozniLHlhZrjgIHniLHnpL7kvJrkuLvkuYnnm7jnu5/kuIDvvJvvvIgy77yJ57u05oqk56WW5Zu957uf5LiA5ZKM5rCR5peP5Zui57uT77yb77yIM++8ieWwiumHjeWSjOS8oOaJv+S4reWNjuawkeaXj+WOhuWPsuWSjOaWh+WMlu+8m++8iDTvvInnq4votrPmsJHml4/lj4jpnaLlkJHkuJbnlYzjgILopoHnu5PlkIjmlrDml7bku6Pnibnngrnov5vooYzorrrov7DjgIInLAogICAgICAgICAgc2NvcmU6IDEwLAogICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE4JwogICAgICAgIH1dOwogICAgICAgIF90aGlzLnRvdGFsID0gX3RoaXMubGlzdC5sZW5ndGg7CiAgICAgICAgX3RoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSwgNTAwKTsKICAgIH0sCiAgICBsb2FkQ2F0ZWdvcnlPcHRpb25zOiBmdW5jdGlvbiBsb2FkQ2F0ZWdvcnlPcHRpb25zKCkgewogICAgICAvLyDmqKHmi5/liIbnsbvpgInpobnvvIjmiYHlubPljJbvvIkKICAgICAgdGhpcy5jYXRlZ29yeU9wdGlvbnMgPSBbewogICAgICAgIGNhdGVnb3J5X2lkOiAxMTEsCiAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+WUr+eJqeiuuicKICAgICAgfSwgewogICAgICAgIGNhdGVnb3J5X2lkOiAxMTIsCiAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+i+qeivgeazlScKICAgICAgfSwgewogICAgICAgIGNhdGVnb3J5X2lkOiAxMTMsCiAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+iupOivhuiuuicKICAgICAgfSwgewogICAgICAgIGNhdGVnb3J5X2lkOiAxMiwKICAgICAgICBjYXRlZ29yeV9uYW1lOiAn6ams5YWL5oCd5Li75LmJ5pS/5rK757uP5rWO5a2mJwogICAgICB9LCB7CiAgICAgICAgY2F0ZWdvcnlfaWQ6IDEzLAogICAgICAgIGNhdGVnb3J5X25hbWU6ICfnp5HlrabnpL7kvJrkuLvkuYknCiAgICAgIH0sIHsKICAgICAgICBjYXRlZ29yeV9pZDogMjEsCiAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+avm+azveS4nOaAneaDsycKICAgICAgfSwgewogICAgICAgIGNhdGVnb3J5X2lkOiAyMiwKICAgICAgICBjYXRlZ29yeV9uYW1lOiAn6YKT5bCP5bmz55CG6K66JwogICAgICB9LCB7CiAgICAgICAgY2F0ZWdvcnlfaWQ6IDIzLAogICAgICAgIGNhdGVnb3J5X25hbWU6ICfkuInkuKrku6Pooajph43opoHmgJ3mg7MnCiAgICAgIH0sIHsKICAgICAgICBjYXRlZ29yeV9pZDogMzEsCiAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+aXp+awkeS4u+S4u+S5iemdqeWRveaXtuacnycKICAgICAgfSwgewogICAgICAgIGNhdGVnb3J5X2lkOiAzMiwKICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG95pe25pyfJwogICAgICB9LCB7CiAgICAgICAgY2F0ZWdvcnlfaWQ6IDMzLAogICAgICAgIGNhdGVnb3J5X25hbWU6ICfnpL7kvJrkuLvkuYnpnanlkb3lkozlu7rorr7ml7bmnJ8nCiAgICAgIH1dOwogICAgfSwKICAgIGhhbmRsZUZpbHRlcjogZnVuY3Rpb24gaGFuZGxlRmlsdGVyKCkgewogICAgICB0aGlzLmxpc3RRdWVyeS5wYWdlID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlQ3JlYXRlOiBmdW5jdGlvbiBoYW5kbGVDcmVhdGUoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvcXVlc3Rpb25zL2FkZCcpOwogICAgfSwKICAgIGhhbmRsZUltcG9ydDogZnVuY3Rpb24gaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3F1ZXN0aW9ucy91cGxvYWQnKTsKICAgIH0sCiAgICBoYW5kbGVVcGRhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9xdWVzdGlvbnMvZWRpdC8iLmNvbmNhdChyb3cucXVlc3Rpb25faWQpKTsKICAgIH0sCiAgICBoYW5kbGVWaWV3OiBmdW5jdGlvbiBoYW5kbGVWaWV3KHJvdykgewogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvbiA9IHJvdzsKICAgICAgdGhpcy52aWV3RGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTor6Xpopjnm67lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMyLiRub3RpZnkoewogICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKn++8iOaooeaLn++8iScsCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["waves", "formatDate", "Pagination", "name", "components", "directives", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "question_type", "difficulty", "category_id", "status", "categoryOptions", "viewDialogVisible", "currentQuestion", "typeTagMap", "single_choice", "multiple_choice", "true_false", "fill_blank", "short_answer", "essay", "typeTextMap", "difficultyTagMap", "easy", "medium", "hard", "difficultyTextMap", "created", "getList", "loadCategoryOptions", "methods", "_this", "setTimeout", "question_id", "question_content", "category_name", "tags", "tag_id", "tag_name", "options", "content", "correct_answer", "explanation", "score", "created_at", "length", "handleFilter", "handleCreate", "$router", "push", "handleImport", "handleUpdate", "row", "concat", "handleView", "handleDelete", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "$notify", "title", "message", "duration"], "sources": ["src/views/questions/list.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索题目内容\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.question_type\"\n          placeholder=\"题目类型\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"单选题\" value=\"single_choice\" />\n          <el-option label=\"多选题\" value=\"multiple_choice\" />\n          <el-option label=\"判断题\" value=\"true_false\" />\n          <el-option label=\"填空题\" value=\"fill_blank\" />\n          <el-option label=\"简答题\" value=\"short_answer\" />\n          <el-option label=\"论述题\" value=\"essay\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.difficulty\"\n          placeholder=\"难度等级\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"简单\" value=\"easy\" />\n          <el-option label=\"中等\" value=\"medium\" />\n          <el-option label=\"困难\" value=\"hard\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.category_id\"\n          placeholder=\"题目分类\"\n          clearable\n          style=\"width: 150px\"\n          class=\"filter-item\"\n        >\n          <el-option\n            v-for=\"category in categoryOptions\"\n            :key=\"category.category_id\"\n            :label=\"category.category_name\"\n            :value=\"category.category_id\"\n          />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加题目\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          type=\"success\"\n          icon=\"el-icon-upload\"\n          @click=\"handleImport\"\n        >\n          批量导入\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"question_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"题目内容\" prop=\"question_content\" min-width=\"300\">\n        <template slot-scope=\"{row}\">\n          <div class=\"question-content\">\n            {{ row.question_content.length > 100 ? row.question_content.substring(0, 100) + '...' : row.question_content }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"题目类型\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"typeTagMap[row.question_type]\">\n            {{ typeTextMap[row.question_type] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"难度\" width=\"80\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"difficultyTagMap[row.difficulty]\" size=\"mini\">\n            {{ difficultyTextMap[row.difficulty] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"分类\" prop=\"category_name\" min-width=\"120\" />\n      <el-table-column label=\"标签\" min-width=\"150\">\n        <template slot-scope=\"{row}\">\n          <el-tag\n            v-for=\"tag in row.tags\"\n            :key=\"tag.tag_id\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n          >\n            {{ tag.tag_name }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"分值\" prop=\"score\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button type=\"info\" size=\"mini\" @click=\"handleView(row)\">\n            查看\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 查看题目对话框 -->\n    <el-dialog title=\"题目详情\" :visible.sync=\"viewDialogVisible\" width=\"60%\">\n      <div v-if=\"currentQuestion\" class=\"question-detail\">\n        <div class=\"detail-item\">\n          <label>题目类型：</label>\n          <el-tag :type=\"typeTagMap[currentQuestion.question_type]\">\n            {{ typeTextMap[currentQuestion.question_type] }}\n          </el-tag>\n        </div>\n        <div class=\"detail-item\">\n          <label>难度等级：</label>\n          <el-tag :type=\"difficultyTagMap[currentQuestion.difficulty]\">\n            {{ difficultyTextMap[currentQuestion.difficulty] }}\n          </el-tag>\n        </div>\n        <div class=\"detail-item\">\n          <label>题目内容：</label>\n          <div class=\"content\">{{ currentQuestion.question_content }}</div>\n        </div>\n        <div v-if=\"currentQuestion.options && currentQuestion.options.length > 0\" class=\"detail-item\">\n          <label>选项：</label>\n          <div class=\"options\">\n            <div\n              v-for=\"(option, index) in currentQuestion.options\"\n              :key=\"index\"\n              class=\"option\"\n            >\n              {{ String.fromCharCode(65 + index) }}. {{ option.content }}\n            </div>\n          </div>\n        </div>\n        <div class=\"detail-item\">\n          <label>正确答案：</label>\n          <div class=\"answer\">{{ currentQuestion.correct_answer }}</div>\n        </div>\n        <div v-if=\"currentQuestion.explanation\" class=\"detail-item\">\n          <label>题目解析：</label>\n          <div class=\"explanation\">{{ currentQuestion.explanation }}</div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionList',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        question_type: '',\n        difficulty: '',\n        category_id: '',\n        status: ''\n      },\n      categoryOptions: [],\n      viewDialogVisible: false,\n      currentQuestion: null,\n      typeTagMap: {\n        single_choice: 'primary',\n        multiple_choice: 'success',\n        true_false: 'warning',\n        fill_blank: 'info',\n        short_answer: 'danger',\n        essay: 'danger'\n      },\n      typeTextMap: {\n        single_choice: '单选题',\n        multiple_choice: '多选题',\n        true_false: '判断题',\n        fill_blank: '填空题',\n        short_answer: '简答题',\n        essay: '论述题'\n      },\n      difficultyTagMap: {\n        easy: 'success',\n        medium: 'warning',\n        hard: 'danger'\n      },\n      difficultyTextMap: {\n        easy: '简单',\n        medium: '中等',\n        hard: '困难'\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.loadCategoryOptions()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n\n      // 模拟题目数据\n      setTimeout(() => {\n        this.list = [\n          // 马克思主义基本原理 - 哲学部分\n          {\n            question_id: 1,\n            question_content: '马克思主义哲学的基本问题是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '物质和意识的关系问题' },\n              { content: '理论和实践的关系问题' },\n              { content: '个人和社会的关系问题' },\n              { content: '自由和必然的关系问题' }\n            ],\n            correct_answer: 'A',\n            explanation: '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。它包括两个方面：第一，物质和意识何者为第一性；第二，物质和意识是否具有同一性。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 2,\n            question_content: '物质的唯一特性是（）',\n            question_type: 'single_choice',\n            difficulty: 'easy',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 16, tag_name: '2023年真题' }\n            ],\n            options: [\n              { content: '运动性' },\n              { content: '客观实在性' },\n              { content: '可知性' },\n              { content: '绝对性' }\n            ],\n            correct_answer: 'B',\n            explanation: '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在，并能为人的意识所反映。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 3,\n            question_content: '意识的本质是（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 1112,\n            category_name: '意识本质',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '意识是人脑的机能' },\n              { content: '意识是客观存在的反映' },\n              { content: '意识是社会的产物' },\n              { content: '意识具有主观能动性' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '意识的本质包括：（1）意识是人脑的机能；（2）意识是客观存在的反映；（3）意识是社会的产物；（4）意识具有主观能动性。这四个方面构成了意识本质的完整内容。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 4,\n            question_content: '马克思主义哲学认为，世界的真正统一性在于它的物质性。',\n            question_type: 'true_false',\n            difficulty: 'easy',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' }\n            ],\n            options: [],\n            correct_answer: 'true',\n            explanation: '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。这是马克思主义一元论的基本观点，强调世界万物都是物质的不同表现形式。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 5,\n            question_content: '联系的特点包括（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 1121,\n            category_name: '联系观',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '客观性' },\n              { content: '普遍性' },\n              { content: '多样性' },\n              { content: '条件性' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '联系具有客观性、普遍性、多样性和条件性等特点。客观性指联系是事物本身所固有的；普遍性指任何事物都处在联系之中；多样性指联系的形式多种多样；条件性指联系是有条件的。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 6,\n            question_content: '矛盾的基本属性是_____和_____。',\n            question_type: 'fill_blank',\n            difficulty: 'easy',\n            category_id: 1123,\n            category_name: '矛盾规律',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [],\n            correct_answer: '同一性；斗争性',\n            explanation: '矛盾的基本属性是同一性和斗争性。同一性是指矛盾双方相互依存、相互贯通的性质和趋势；斗争性是指矛盾双方相互排斥、相互对立的性质和趋势。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 7,\n            question_content: '实践是认识的基础，主要表现在（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 113,\n            category_name: '认识论',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [\n              { content: '实践是认识的来源' },\n              { content: '实践是认识发展的动力' },\n              { content: '实践是检验认识真理性的唯一标准' },\n              { content: '实践是认识的目的' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '实践是认识的基础，主要表现在四个方面：（1）实践是认识的来源；（2）实践是认识发展的动力；（3）实践是检验认识真理性的唯一标准；（4）实践是认识的目的。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          // 毛泽东思想部分\n          {\n            question_id: 8,\n            question_content: '毛泽东思想形成的时代背景是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '帝国主义和无产阶级革命的时代' },\n              { content: '资本主义向社会主义过渡的时代' },\n              { content: '和平与发展的时代' },\n              { content: '全球化的时代' }\n            ],\n            correct_answer: 'A',\n            explanation: '毛泽东思想形成的时代背景是帝国主义和无产阶级革命的时代。这一时代特征为毛泽东思想的形成和发展提供了重要的历史条件。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          {\n            question_id: 9,\n            question_content: '新民主主义革命的总路线是（）',\n            question_type: 'single_choice',\n            difficulty: 'hard',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [\n              { content: '无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命' },\n              { content: '工人阶级领导的，以工农联盟为基础的人民民主专政' },\n              { content: '中国共产党领导的多党合作和政治协商制度' },\n              { content: '人民当家作主的社会主义民主政治' }\n            ],\n            correct_answer: 'A',\n            explanation: '新民主主义革命的总路线是：无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命。这是毛泽东对新民主主义革命本质的科学概括。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          {\n            question_id: 10,\n            question_content: '中国革命的基本问题是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 17, tag_name: '2022年真题' }\n            ],\n            options: [\n              { content: '农民问题' },\n              { content: '武装斗争问题' },\n              { content: '统一战线问题' },\n              { content: '党的建设问题' }\n            ],\n            correct_answer: 'A',\n            explanation: '中国革命的基本问题是农民问题。毛泽东指出，农民问题乃国民革命的中心问题，农民是中国革命的主力军，农民问题是中国革命的基本问题。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          // 中国近现代史纲要部分\n          {\n            question_id: 11,\n            question_content: '中国近代史的起点是（）',\n            question_type: 'single_choice',\n            difficulty: 'easy',\n            category_id: 31,\n            category_name: '旧民主主义革命时期',\n            tags: [\n              { tag_id: 3, tag_name: '中国近现代史纲要' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '1840年鸦片战争' },\n              { content: '1842年《南京条约》签订' },\n              { content: '1851年太平天国运动' },\n              { content: '1894年甲午中日战争' }\n            ],\n            correct_answer: 'A',\n            explanation: '中国近代史的起点是1840年鸦片战争。鸦片战争是中国历史的转折点，标志着中国开始沦为半殖民地半封建社会，中国近代史由此开始。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-17'\n          },\n          {\n            question_id: 12,\n            question_content: '五四运动的历史意义包括（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 32,\n            category_name: '新民主主义革命时期',\n            tags: [\n              { tag_id: 3, tag_name: '中国近现代史纲要' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '是中国新民主主义革命的开端' },\n              { content: '促进了马克思主义在中国的传播' },\n              { content: '促进了马克思主义与中国工人运动的结合' },\n              { content: '为中国共产党的成立作了思想和干部准备' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '五四运动的历史意义重大：（1）是中国新民主主义革命的开端；（2）促进了马克思主义在中国的传播；（3）促进了马克思主义与中国工人运动的结合；（4）为中国共产党的成立作了思想和干部准备。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-17'\n          },\n          // 思想道德与法治部分\n          {\n            question_id: 13,\n            question_content: '理想信念的作用主要表现在（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '指引人生的奋斗目标' },\n              { content: '提供人生的前进动力' },\n              { content: '提高人生的精神境界' },\n              { content: '增强人生的使命感' }\n            ],\n            correct_answer: 'A,B,C',\n            explanation: '理想信念的作用主要表现在三个方面：（1）指引人生的奋斗目标；（2）提供人生的前进动力；（3）提高人生的精神境界。理想信念是人生的精神支柱和前进动力。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-18'\n          },\n          {\n            question_id: 14,\n            question_content: '社会主义核心价值观的基本内容是_____、_____、_____。',\n            question_type: 'fill_blank',\n            difficulty: 'easy',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 15, tag_name: '新时代' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [],\n            correct_answer: '富强、民主、文明、和谐；自由、平等、公正、法治；爱国、敬业、诚信、友善',\n            explanation: '社会主义核心价值观的基本内容是：国家层面的价值要求是富强、民主、文明、和谐；社会层面的价值要求是自由、平等、公正、法治；个人层面的价值要求是爱国、敬业、诚信、友善。',\n            score: 3,\n            status: 'active',\n            created_at: '2024-01-18'\n          },\n          {\n            question_id: 15,\n            question_content: '请论述新时代爱国主义的基本要求。',\n            question_type: 'essay',\n            difficulty: 'hard',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 15, tag_name: '新时代' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [],\n            correct_answer: '新时代爱国主义的基本要求包括：（1）坚持爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）坚持立足民族又面向世界。新时代的爱国主义必须坚持爱国和爱党、爱社会主义相统一，这是当代中国爱国主义精神最重要的体现。',\n            explanation: '这道题考查新时代爱国主义的基本要求。答题要点：（1）爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）立足民族又面向世界。要结合新时代特点进行论述。',\n            score: 10,\n            status: 'active',\n            created_at: '2024-01-18'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n      }, 500)\n    },\n\n    loadCategoryOptions() {\n      // 模拟分类选项（扁平化）\n      this.categoryOptions = [\n        { category_id: 111, category_name: '唯物论' },\n        { category_id: 112, category_name: '辩证法' },\n        { category_id: 113, category_name: '认识论' },\n        { category_id: 12, category_name: '马克思主义政治经济学' },\n        { category_id: 13, category_name: '科学社会主义' },\n        { category_id: 21, category_name: '毛泽东思想' },\n        { category_id: 22, category_name: '邓小平理论' },\n        { category_id: 23, category_name: '三个代表重要思想' },\n        { category_id: 31, category_name: '旧民主主义革命时期' },\n        { category_id: 32, category_name: '新民主主义革命时期' },\n        { category_id: 33, category_name: '社会主义革命和建设时期' }\n      ]\n    },\n\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n\n    handleCreate() {\n      this.$router.push('/questions/add')\n    },\n\n    handleImport() {\n      this.$router.push('/questions/upload')\n    },\n\n    handleUpdate(row) {\n      this.$router.push(`/questions/edit/${row.question_id}`)\n    },\n\n    handleView(row) {\n      this.currentQuestion = row\n      this.viewDialogVisible = true\n    },\n\n    handleDelete(row) {\n      this.$confirm('确定要删除该题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n\n    .question-content {\n      line-height: 1.5;\n      word-break: break-word;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n\n.question-detail {\n  .detail-item {\n    margin-bottom: 20px;\n\n    label {\n      font-weight: bold;\n      color: #303133;\n      margin-bottom: 8px;\n      display: block;\n    }\n\n    .content,\n    .answer,\n    .explanation {\n      background: #f5f7fa;\n      padding: 12px;\n      border-radius: 4px;\n      line-height: 1.6;\n    }\n\n    .options {\n      .option {\n        padding: 8px 12px;\n        margin-bottom: 8px;\n        background: #f5f7fa;\n        border-radius: 4px;\n        border-left: 3px solid #409eff;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6NA,OAAAA,KAAA;AACA,SAAAC,UAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,aAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACAC,eAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,UAAA;QACAC,aAAA;QACAC,eAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,KAAA;MACA;MACAC,WAAA;QACAN,aAAA;QACAC,eAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,KAAA;MACA;MACAE,gBAAA;QACAC,IAAA;QACAC,MAAA;QACAC,IAAA;MACA;MACAC,iBAAA;QACAH,IAAA;QACAC,MAAA;QACAC,IAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,mBAAA;EACA;EACAC,OAAA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAA7B,WAAA;;MAEA;MACA8B,UAAA;QACAD,KAAA,CAAA/B,IAAA;QACA;QACA;UACAiC,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA;UACAE,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA;UACAE,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA;QACA;QACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA;QACA;QACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA;QACA;QACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA,GACA;YAAAC,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,GACA;YAAAA,OAAA;UAAA,EACA;UACAC,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA;UACAE,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,GACA;UACAX,WAAA;UACAC,gBAAA;UACA3B,aAAA;UACAC,UAAA;UACAC,WAAA;UACA0B,aAAA;UACAC,IAAA,GACA;YAAAC,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,MAAA;YAAAC,QAAA;UAAA,EACA;UACAC,OAAA;UACAE,cAAA;UACAC,WAAA;UACAC,KAAA;UACAjC,MAAA;UACAkC,UAAA;QACA,EACA;QACAb,KAAA,CAAA9B,KAAA,GAAA8B,KAAA,CAAA/B,IAAA,CAAA6C,MAAA;QACAd,KAAA,CAAA7B,WAAA;MACA;IACA;IAEA2B,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAAlB,eAAA,IACA;QAAAF,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,GACA;QAAA1B,WAAA;QAAA0B,aAAA;MAAA,EACA;IACA;IAEAW,YAAA,WAAAA,aAAA;MACA,KAAA3C,SAAA,CAAAC,IAAA;MACA,KAAAwB,OAAA;IACA;IAEAmB,YAAA,WAAAA,aAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;IACA;IAEAE,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAJ,OAAA,CAAAC,IAAA,oBAAAI,MAAA,CAAAD,GAAA,CAAAnB,WAAA;IACA;IAEAqB,UAAA,WAAAA,WAAAF,GAAA;MACA,KAAAvC,eAAA,GAAAuC,GAAA;MACA,KAAAxC,iBAAA;IACA;IAEA2C,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,MAAA,CAAAM,OAAA;UACAC,KAAA;UACAC,OAAA;UACAJ,IAAA;UACAK,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}