// practice.js
const { api } = require('../../utils/api.js')
const { getDifficultyText, showError } = require('../../utils/util.js')

Page({
  data: {
    searchKeyword: '',
    categories: [
      {
        id: 1,
        name: '考研英语一',
        icon: '📖',
        questionCount: 1250
      },
      {
        id: 2,
        name: '考研英语二',
        icon: '📚',
        questionCount: 980
      },
      {
        id: 3,
        name: '阅读理解',
        icon: '👁️',
        questionCount: 650
      },
      {
        id: 4,
        name: '完型填空',
        icon: '✏️',
        questionCount: 320
      },
      {
        id: 5,
        name: '翻译',
        icon: '🔄',
        questionCount: 180
      },
      {
        id: 6,
        name: '写作',
        icon: '✍️',
        questionCount: 120
      }
    ],
    recommendList: [
      {
        id: 1,
        title: '2023年考研英语一真题',
        description: '最新真题，全面覆盖考点',
        cover: '🎯',
        questionCount: 50,
        difficulty: 3,
        difficultyText: '困难'
      },
      {
        id: 2,
        title: '阅读理解专项训练',
        description: '提升阅读理解能力',
        cover: '📖',
        questionCount: 30,
        difficulty: 2,
        difficultyText: '中等'
      },
      {
        id: 3,
        title: '语法基础强化',
        description: '巩固语法基础知识',
        cover: '📝',
        questionCount: 40,
        difficulty: 1,
        difficultyText: '简单'
      }
    ]
  },

  onLoad() {
    console.log('练习页面加载')
    this.loadCategories()
    this.loadRecommendList()
  },

  onShow() {
    console.log('练习页面显示')
  },

  // 加载分类数据
  loadCategories() {
    // 使用mock数据，实际应该调用API
    // api.getCategories().then(res => {
    //   this.setData({
    //     categories: res.data
    //   })
    // }).catch(err => {
    //   console.error('获取分类失败', err)
    //   showError('获取分类失败')
    // })
  },

  // 加载推荐列表
  loadRecommendList() {
    // 处理难度文本
    const recommendList = this.data.recommendList.map(item => ({
      ...item,
      difficultyText: getDifficultyText(item.difficulty)
    }))
    
    this.setData({
      recommendList
    })

    // 实际API调用示例
    // api.getRecommendPractice().then(res => {
    //   const list = res.data.map(item => ({
    //     ...item,
    //     difficultyText: getDifficultyText(item.difficulty)
    //   }))
    //   this.setData({
    //     recommendList: list
    //   })
    // }).catch(err => {
    //   console.error('获取推荐练习失败', err)
    // })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 执行搜索
  onSearch() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    console.log('搜索关键词:', keyword)
    
    // 这里应该调用搜索API
    wx.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    })
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    console.log('选择分类:', category)
    
    wx.navigateTo({
      url: `/pages/question/question?categoryId=${category.id}&categoryName=${category.name}`
    })
  },

  // 开始练习
  startPractice(e) {
    const item = e.currentTarget.dataset.item
    console.log('开始练习:', item)
    
    wx.navigateTo({
      url: `/pages/question/question?practiceId=${item.id}&title=${item.title}`
    })
  },

  // 快速练习
  quickPractice(e) {
    const type = e.currentTarget.dataset.type
    console.log('快速练习类型:', type)
    
    switch (type) {
      case 'random':
        wx.navigateTo({
          url: '/pages/question/question?type=random&title=随机练习'
        })
        break
      case 'wrong':
        wx.navigateTo({
          url: '/pages/errors/errors'
        })
        break
      case 'favorite':
        wx.navigateTo({
          url: '/pages/favorites/favorites'
        })
        break
      case 'mock':
        wx.showModal({
          title: '模拟考试',
          content: '确定开始模拟考试吗？考试时间为180分钟。',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/question/question?type=mock&title=模拟考试&timeLimit=10800'
              })
            }
          }
        })
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  }
})
