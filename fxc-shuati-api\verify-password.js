const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function verifyPassword() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '*************',
      user: process.env.DB_USER || 'fxc-shuati',
      password: process.env.DB_PASSWORD || 'haf6LeMjHLKefKs7',
      database: process.env.DB_NAME || 'fxc-shuati',
      port: process.env.DB_PORT || 3306
    });

    console.log('数据库连接成功');

    // 查询admin用户的密码hash
    const [admins] = await connection.query('SELECT username, password FROM admins WHERE username = ?', ['admin']);
    
    if (admins.length === 0) {
      console.log('admin用户不存在');
      return;
    }
    
    const admin = admins[0];
    console.log('用户名:', admin.username);
    console.log('密码hash:', admin.password);
    
    // 测试密码验证
    const testPassword = '123456';
    const isValid = await bcrypt.compare(testPassword, admin.password);
    console.log(`密码 "${testPassword}" 验证结果:`, isValid);
    
    // 生成新的密码hash用于对比
    const newHash = await bcrypt.hash(testPassword, 10);
    console.log('新生成的hash:', newHash);
    
    // 验证新hash
    const newHashValid = await bcrypt.compare(testPassword, newHash);
    console.log('新hash验证结果:', newHashValid);

  } catch (error) {
    console.error('验证失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

verifyPassword();
