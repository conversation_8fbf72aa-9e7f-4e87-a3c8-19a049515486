{"_from": "json5@^0.5.0", "_id": "json5@0.5.1", "_inBundle": false, "_integrity": "sha512-4xrs1aW+6N5DalkqSVA8fxh458CXvR99WU8WLKmq4v8eWAL86Xo3BVqyd3SkA9wEVjCMqyvvRRkshAdOnBp5rw==", "_location": "/html-webpack-plugin/json5", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "json5@^0.5.0", "name": "json5", "escapedName": "json5", "rawSpec": "^0.5.0", "saveSpec": null, "fetchSpec": "^0.5.0"}, "_requiredBy": ["/html-webpack-plugin/loader-utils"], "_resolved": "https://registry.npmmirror.com/json5/-/json5-0.5.1.tgz", "_shasum": "1eade7acc012034ad84e2396767ead9fa5495821", "_spec": "json5@^0.5.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\html-webpack-plugin\\node_modules\\loader-utils", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"json5": "lib/cli.js"}, "bugs": {"url": "https://github.com/aseemk/json5/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "dependencies": {}, "deprecated": false, "description": "JSON for the ES5 era.", "devDependencies": {"gulp": "^3.9.1", "gulp-jshint": "^2.0.1", "jshint": "^2.9.3", "jshint-stylish": "^2.2.1", "mocha": "^3.1.0"}, "files": ["lib/"], "homepage": "http://json5.org/", "keywords": ["json", "es5"], "license": "MIT", "main": "lib/json5.js", "name": "json5", "repository": {"type": "git", "url": "git+https://github.com/aseemk/json5.git"}, "scripts": {"build": "node ./lib/cli.js -c package.json5", "test": "mocha --ui exports --reporter spec"}, "version": "0.5.1"}