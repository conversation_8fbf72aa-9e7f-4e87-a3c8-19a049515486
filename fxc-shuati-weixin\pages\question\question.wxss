/**question.wxss**/

.container {
  padding-bottom: 120rpx;
}

/* 进度条 */
.progress-bar {
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.progress-text,
.time-text {
  font-size: 24rpx;
  color: #666;
}

.time-text {
  color: #409EFF;
  font-weight: 500;
}

/* 题目内容 */
.question-content {
  padding: 20rpx;
}

.composite-content {
  margin-bottom: 20rpx;
  border-left: 6rpx solid #E6A23C;
}

.composite-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #E6A23C;
  margin-bottom: 20rpx;
}

.question-main {
  margin-bottom: 20rpx;
}

.question-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.question-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.question-type {
  font-size: 24rpx;
  color: #409EFF;
  background-color: #e6f3ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.question-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 选项 */
.options-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.option-item:active {
  transform: scale(0.98);
}

.option-item.selected {
  border-color: #409EFF;
  background-color: #e6f3ff;
}

.option-item.correct {
  border-color: #67C23A;
  background-color: #f0f9ff;
}

.option-item.wrong {
  border-color: #F56C6C;
  background-color: #fef0f0;
}

.option-label {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.option-item.selected .option-label {
  background-color: #409EFF;
  color: #fff;
}

.option-item.correct .option-label {
  background-color: #67C23A;
  color: #fff;
}

.option-item.wrong .option-label {
  background-color: #F56C6C;
  color: #fff;
}

.option-content {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.option-check {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 输入框 */
.input-container {
  margin-bottom: 20rpx;
}

.fill-input,
.essay-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background-color: #fff;
}

.essay-input {
  min-height: 200rpx;
}

/* 答案解析 */
.explanation-container {
  border-left: 6rpx solid #409EFF;
  margin-bottom: 20rpx;
}

.explanation-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 20rpx;
}

.explanation-content {
  font-size: 26rpx;
  line-height: 1.6;
  color: #666;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #409EFF;
  color: #fff;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

.btn-favorite {
  background-color: #f0f0f0;
  color: #666;
}

.btn-favorite.active {
  background-color: #ff6b6b;
  color: #fff;
}

.action-btn:disabled {
  opacity: 0.5;
}

/* 退出确认弹窗 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn.cancel {
  background-color: #f0f0f0;
  color: #666;
}

.modal-btn.confirm {
  background-color: #F56C6C;
  color: #fff;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .option-item {
    padding: 20rpx;
  }
  
  .option-label {
    width: 50rpx;
    height: 50rpx;
    font-size: 20rpx;
  }
  
  .option-content {
    font-size: 26rpx;
  }
  
  .question-text {
    font-size: 26rpx;
  }
}

/* 动画效果 */
.option-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}
