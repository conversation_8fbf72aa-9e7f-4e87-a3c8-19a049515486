{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\router\\index.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\router\\index.js", "mtime": 1752572678224}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Router", "use", "Layout", "constantRoutes", "path", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "hidden", "redirect", "children", "name", "meta", "title", "icon", "createRouter", "scroll<PERSON>eh<PERSON>or", "y", "routes", "router", "resetRouter", "newRouter", "matcher"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Router from 'vue-router'\n\nVue.use(Router)\n\n/* Layout */\nimport Layout from '@/layout'\n\n/**\n * Note: sub-menu only appear when route children.length >= 1\n * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html\n *\n * hidden: true                   if set true, item will not show in the sidebar(default is false)\n * alwaysShow: true               if set true, will always show the root menu\n *                                if not set alwaysShow, when item has more than one children route,\n *                                it will becomes nested mode, otherwise not show the root menu\n * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb\n * name:'router-name'             the name is used by <keep-alive> (must set!!!)\n * meta : {\n    roles: ['admin','editor']    control the page roles (you can set multiple roles)\n    title: 'title'               the name show in sidebar and breadcrumb (recommend set)\n    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar\n    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)\n    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set\n  }\n */\n\n/**\n * constantRoutes\n * a base page that does not have permission requirements\n * all roles can be accessed\n */\nexport const constantRoutes = [\n  {\n    path: '/login',\n    component: () => import('@/views/login/index'),\n    hidden: true\n  },\n\n  {\n    path: '/404',\n    component: () => import('@/views/404'),\n    hidden: true\n  },\n\n  {\n    path: '/',\n    component: Layout,\n    redirect: '/dashboard',\n    children: [{\n      path: 'dashboard',\n      name: 'Dashboard',\n      component: () => import('@/views/dashboard/index'),\n      meta: { title: '首页', icon: 'dashboard' }\n    }]\n  },\n\n  // 系统管理\n  {\n    path: '/system',\n    component: Layout,\n    redirect: '/system/users',\n    name: 'System',\n    meta: { title: '系统管理', icon: 'el-icon-setting' },\n    children: [\n      {\n        path: 'users',\n        name: 'UserManagement',\n        component: () => import('@/views/users/list'),\n        meta: { title: '用户管理', icon: 'el-icon-user-solid' }\n      },\n      {\n        path: 'roles',\n        name: 'RoleList',\n        component: () => import('@/views/users/roles'),\n        meta: { title: '角色管理', icon: 'el-icon-s-custom' }\n      },\n      {\n        path: 'permissions',\n        name: 'PermissionList',\n        component: () => import('@/views/users/permissions'),\n        meta: { title: '权限管理', icon: 'el-icon-key' }\n      },\n      {\n        path: 'menus',\n        name: 'MenuManagement',\n        component: () => import('@/views/system/menus'),\n        meta: { title: '菜单管理', icon: 'el-icon-menu' }\n      }\n    ]\n  },\n\n  // 题库管理\n  {\n    path: '/questions',\n    component: Layout,\n    redirect: '/questions/list',\n    name: 'Questions',\n    meta: { title: '题库管理', icon: 'el-icon-document' },\n    children: [\n      {\n        path: 'categories',\n        name: 'QuestionCategories',\n        component: () => import('@/views/questions/categories'),\n        meta: { title: '题目分类', icon: 'el-icon-folder' }\n      },\n      {\n        path: 'list',\n        name: 'QuestionList',\n        component: () => import('@/views/questions/list'),\n        meta: { title: '题目列表', icon: 'el-icon-document-copy' }\n      },\n      {\n        path: 'add',\n        name: 'QuestionAdd',\n        component: () => import('@/views/questions/add'),\n        meta: { title: '添加题目', icon: 'el-icon-plus' }\n      },\n      {\n        path: 'edit/:id',\n        name: 'QuestionEdit',\n        component: () => import('@/views/questions/edit'),\n        meta: { title: '编辑题目', icon: 'el-icon-edit' },\n        hidden: true\n      },\n      {\n        path: 'upload',\n        name: 'QuestionUpload',\n        component: () => import('@/views/questions/upload'),\n        meta: { title: '批量导入', icon: 'el-icon-upload' }\n      }\n    ]\n  },\n\n  // 学习数据\n  {\n    path: '/learning',\n    component: Layout,\n    redirect: '/learning/favorites',\n    name: 'Learning',\n    meta: { title: '学习数据', icon: 'el-icon-data-analysis' },\n    children: [\n      {\n        path: 'favorites',\n        name: 'FavoritesList',\n        component: () => import('@/views/learning/favorites'),\n        meta: { title: '收藏管理', icon: 'el-icon-star-on' }\n      },\n      {\n        path: 'errors',\n        name: 'ErrorsList',\n        component: () => import('@/views/learning/errors'),\n        meta: { title: '错题管理', icon: 'el-icon-warning' }\n      },\n      {\n        path: 'records',\n        name: 'RecordsList',\n        component: () => import('@/views/learning/records'),\n        meta: { title: '练习记录', icon: 'el-icon-tickets' }\n      }\n    ]\n  },\n\n\n\n  // 404 page must be placed at the end !!!\n  { path: '*', redirect: '/404', hidden: true }\n]\n\nconst createRouter = () => new Router({\n  // mode: 'history', // require service support\n  scrollBehavior: () => ({ y: 0 }),\n  routes: constantRoutes\n})\n\nconst router = createRouter()\n\n// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465\nexport function resetRouter() {\n  const newRouter = createRouter()\n  router.matcher = newRouter.matcher // reset router\n}\n\nexport default router\n"], "mappings": ";;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,MAAM,MAAM,YAAY;AAE/BD,GAAG,CAACE,GAAG,CAACD,MAAM,CAAC;;AAEf;AACA,OAAOE,MAAM,MAAM,UAAU;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMC,cAAc,GAAG,CAC5B;EACEC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;IAAA;EAAA,CAAC;EAC9CC,MAAM,EAAE;AACV,CAAC,EAED;EACEP,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,aAAa;IAAA;EAAA,CAAC;EACtCC,MAAM,EAAE;AACV,CAAC,EAED;EACEP,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEH,MAAM;EACjBU,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,CAAC;IACTT,IAAI,EAAE,WAAW;IACjBU,IAAI,EAAE,WAAW;IACjBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDK,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAY;EACzC,CAAC;AACH,CAAC;AAED;AACA;EACEb,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEH,MAAM;EACjBU,QAAQ,EAAE,eAAe;EACzBE,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAkB,CAAC;EAChDJ,QAAQ,EAAE,CACR;IACET,IAAI,EAAE,OAAO;IACbU,IAAI,EAAE,gBAAgB;IACtBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oBAAoB;MAAA;IAAA,CAAC;IAC7CK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAqB;EACpD,CAAC,EACD;IACEb,IAAI,EAAE,OAAO;IACbU,IAAI,EAAE,UAAU;IAChBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qBAAqB;MAAA;IAAA,CAAC;IAC9CK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAmB;EAClD,CAAC,EACD;IACEb,IAAI,EAAE,aAAa;IACnBU,IAAI,EAAE,gBAAgB;IACtBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAc;EAC7C,CAAC,EACD;IACEb,IAAI,EAAE,OAAO;IACbU,IAAI,EAAE,gBAAgB;IACtBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,sBAAsB;MAAA;IAAA,CAAC;IAC/CK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAe;EAC9C,CAAC;AAEL,CAAC;AAED;AACA;EACEb,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEH,MAAM;EACjBU,QAAQ,EAAE,iBAAiB;EAC3BE,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAmB,CAAC;EACjDJ,QAAQ,EAAE,CACR;IACET,IAAI,EAAE,YAAY;IAClBU,IAAI,EAAE,oBAAoB;IAC1BT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAiB;EAChD,CAAC,EACD;IACEb,IAAI,EAAE,MAAM;IACZU,IAAI,EAAE,cAAc;IACpBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,wBAAwB;MAAA;IAAA,CAAC;IACjDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAwB;EACvD,CAAC,EACD;IACEb,IAAI,EAAE,KAAK;IACXU,IAAI,EAAE,aAAa;IACnBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uBAAuB;MAAA;IAAA,CAAC;IAChDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAe;EAC9C,CAAC,EACD;IACEb,IAAI,EAAE,UAAU;IAChBU,IAAI,EAAE,cAAc;IACpBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,wBAAwB;MAAA;IAAA,CAAC;IACjDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAe,CAAC;IAC7CN,MAAM,EAAE;EACV,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdU,IAAI,EAAE,gBAAgB;IACtBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAiB;EAChD,CAAC;AAEL,CAAC;AAED;AACA;EACEb,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEH,MAAM;EACjBU,QAAQ,EAAE,qBAAqB;EAC/BE,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAwB,CAAC;EACtDJ,QAAQ,EAAE,CACR;IACET,IAAI,EAAE,WAAW;IACjBU,IAAI,EAAE,eAAe;IACrBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAkB;EACjD,CAAC,EACD;IACEb,IAAI,EAAE,QAAQ;IACdU,IAAI,EAAE,YAAY;IAClBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAkB;EACjD,CAAC,EACD;IACEb,IAAI,EAAE,SAAS;IACfU,IAAI,EAAE,aAAa;IACnBT,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDK,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAkB;EACjD,CAAC;AAEL,CAAC;AAID;AACA;EAAEb,IAAI,EAAE,GAAG;EAAEQ,QAAQ,EAAE,MAAM;EAAED,MAAM,EAAE;AAAK,CAAC,CAC9C;AAED,IAAMO,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAAS,IAAIlB,MAAM,CAAC;IACpC;IACAmB,cAAc,EAAE,SAAhBA,cAAcA,CAAA;MAAA,OAAS;QAAEC,CAAC,EAAE;MAAE,CAAC;IAAA,CAAC;IAChCC,MAAM,EAAElB;EACV,CAAC,CAAC;AAAA;AAEF,IAAMmB,MAAM,GAAGJ,YAAY,CAAC,CAAC;;AAE7B;AACA,OAAO,SAASK,WAAWA,CAAA,EAAG;EAC5B,IAAMC,SAAS,GAAGN,YAAY,CAAC,CAAC;EAChCI,MAAM,CAACG,OAAO,GAAGD,SAAS,CAACC,OAAO,EAAC;AACrC;AAEA,eAAeH,MAAM", "ignoreList": []}]}