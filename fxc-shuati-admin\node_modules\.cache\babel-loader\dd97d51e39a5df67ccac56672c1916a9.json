{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\FixiOSBug.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\FixiOSBug.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGNvbXB1dGVkOiB7CiAgICBkZXZpY2U6IGZ1bmN0aW9uIGRldmljZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLmFwcC5kZXZpY2U7CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8gSW4gb3JkZXIgdG8gZml4IHRoZSBjbGljayBvbiBtZW51IG9uIHRoZSBpb3MgZGV2aWNlIHdpbGwgdHJpZ2dlciB0aGUgbW91c2VsZWF2ZSBidWcKICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9QYW5KaWFDaGVuL3Z1ZS1lbGVtZW50LWFkbWluL2lzc3Vlcy8xMTM1CiAgICB0aGlzLmZpeEJ1Z0luaU9TKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBmaXhCdWdJbmlPUzogZnVuY3Rpb24gZml4QnVnSW5pT1MoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciAkc3ViTWVudSA9IHRoaXMuJHJlZnMuc3ViTWVudTsKICAgICAgaWYgKCRzdWJNZW51KSB7CiAgICAgICAgdmFyIGhhbmRsZU1vdXNlbGVhdmUgPSAkc3ViTWVudS5oYW5kbGVNb3VzZWxlYXZlOwogICAgICAgICRzdWJNZW51LmhhbmRsZU1vdXNlbGVhdmUgPSBmdW5jdGlvbiAoZSkgewogICAgICAgICAgaWYgKF90aGlzLmRldmljZSA9PT0gJ21vYmlsZScpIHsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgaGFuZGxlTW91c2VsZWF2ZShlKTsKICAgICAgICB9OwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["computed", "device", "$store", "state", "app", "mounted", "fixBugIniOS", "methods", "_this", "$subMenu", "$refs", "subMenu", "handleMouseleave", "e"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/layout/components/Sidebar/FixiOSBug.js"], "sourcesContent": ["export default {\n  computed: {\n    device() {\n      return this.$store.state.app.device\n    }\n  },\n  mounted() {\n    // In order to fix the click on menu on the ios device will trigger the mouseleave bug\n    // https://github.com/PanJiaChen/vue-element-admin/issues/1135\n    this.fixBugIniOS()\n  },\n  methods: {\n    fixBugIniOS() {\n      const $subMenu = this.$refs.subMenu\n      if ($subMenu) {\n        const handleMouseleave = $subMenu.handleMouseleave\n        $subMenu.handleMouseleave = (e) => {\n          if (this.device === 'mobile') {\n            return\n          }\n          handleMouseleave(e)\n        }\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe;EACbA,QAAQ,EAAE;IACRC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,GAAG,CAACH,MAAM;IACrC;EACF,CAAC;EACDI,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR;IACA;IACA,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB,CAAC;EACDC,OAAO,EAAE;IACPD,WAAW,WAAXA,WAAWA,CAAA,EAAG;MAAA,IAAAE,KAAA;MACZ,IAAMC,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACC,OAAO;MACnC,IAAIF,QAAQ,EAAE;QACZ,IAAMG,gBAAgB,GAAGH,QAAQ,CAACG,gBAAgB;QAClDH,QAAQ,CAACG,gBAAgB,GAAG,UAACC,CAAC,EAAK;UACjC,IAAIL,KAAI,CAACP,MAAM,KAAK,QAAQ,EAAE;YAC5B;UACF;UACAW,gBAAgB,CAACC,CAAC,CAAC;QACrB,CAAC;MACH;IACF;EACF;AACF,CAAC", "ignoreList": []}]}