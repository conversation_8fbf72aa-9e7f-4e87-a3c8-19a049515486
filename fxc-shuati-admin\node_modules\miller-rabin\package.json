{"_from": "miller-rabin@^4.0.0", "_id": "miller-rabin@4.0.1", "_inBundle": false, "_integrity": "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==", "_location": "/miller-rabin", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "miller-rabin@^4.0.0", "name": "miller-rabin", "escapedName": "miller-rabin", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/diffie-hellman"], "_resolved": "https://registry.npmmirror.com/miller-rabin/-/miller-rabin-4.0.1.tgz", "_shasum": "f080351c865b0dc562a8462966daa53543c78a4d", "_spec": "miller-rabin@^4.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\diffie-hellman", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"miller-rabin": "bin/miller-rabin"}, "bugs": {"url": "https://github.com/indutny/miller-rabin/issues"}, "bundleDependencies": false, "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "deprecated": false, "description": "<PERSON> algorithm for primality test", "devDependencies": {"mocha": "^2.0.1"}, "homepage": "https://github.com/indutny/miller-rabin", "keywords": ["prime", "miller-rabin", "bignumber"], "license": "MIT", "main": "lib/mr.js", "name": "miller-rabin", "repository": {"type": "git", "url": "git+ssh://**************/indutny/miller-rabin.git"}, "scripts": {"test": "mocha --reporter=spec test/**/*-test.js"}, "version": "4.0.1"}