{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\learning\\errors.vue?vue&type=style&index=0&id=4cc23252&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\learning\\errors.vue", "mtime": 1752572240868}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYXBwLWNvbnRhaW5lciB7CiAgLmNvbnRlbnQtY2FyZCB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICAgIC5jYXJkLWhlYWRlciB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIH0KCiAgICAucGxhY2Vob2xkZXItYm9keSB7CiAgICAgIHBhZGRpbmc6IDQwcHggMDsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIG1pbi1oZWlnaHQ6IDMwMHB4OwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["errors.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "errors.vue", "sourceRoot": "src/views/learning", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"content-card\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>错题管理</span>\n      </div>\n      <div class=\"placeholder-body\">\n        <el-alert\n          title=\"功能开发中\"\n          type=\"info\"\n          description=\"错题管理功能正在开发中，敬请期待...\"\n          show-icon\n          :closable=\"false\"\n        />\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ErrorsList',\n  data() {\n    return {}\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .content-card {\n    margin-bottom: 20px;\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .placeholder-body {\n      padding: 40px 0;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 300px;\n    }\n  }\n}\n</style>\n"]}]}