{"_from": "restore-cursor@^3.1.0", "_id": "restore-cursor@3.1.0", "_inBundle": false, "_integrity": "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==", "_location": "/inquirer/restore-cursor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "restore-cursor@^3.1.0", "name": "restore-cursor", "escapedName": "restore-cursor", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/inquirer/cli-cursor"], "_resolved": "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz", "_shasum": "39f67c54b3a7a58cea5236d95cf0034239631f7e", "_spec": "restore-cursor@^3.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\inquirer\\node_modules\\cli-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "bundleDependencies": false, "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "deprecated": false, "description": "Gracefully restore the CLI cursor on exit", "devDependencies": {"tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "license": "MIT", "name": "restore-cursor", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/restore-cursor.git"}, "scripts": {"test": "xo && tsd"}, "version": "3.1.0"}