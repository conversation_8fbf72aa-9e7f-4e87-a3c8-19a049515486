{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\categories.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\categories.vue", "mtime": 1752631103709}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcycKaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdRdWVzdGlvbkNhdGVnb3JpZXMnLAogIGNvbXBvbmVudHM6IHsgUGFnaW5hdGlvbiB9LAogIGRpcmVjdGl2ZXM6IHsgd2F2ZXMgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDUwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHN0YXR1czogJycKICAgICAgfSwKICAgICAgcGFyZW50T3B0aW9uczogW10sCiAgICAgIHRlbXA6IHsKICAgICAgICBjYXRlZ29yeV9pZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICBjYXRlZ29yeV9uYW1lOiAnJywKICAgICAgICBjYXRlZ29yeV9jb2RlOiAnJywKICAgICAgICBjYXRlZ29yeV9kZXNjOiAnJywKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgIHNvcnRfb3JkZXI6IDAKICAgICAgfSwKICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dTdGF0dXM6ICcnLAogICAgICB0ZXh0TWFwOiB7CiAgICAgICAgdXBkYXRlOiAn57yW6L6R5YiG57G7JywKICAgICAgICBjcmVhdGU6ICfmt7vliqDliIbnsbsnCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgY2F0ZWdvcnlfbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfliIbnsbvlkI3np7DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgY2F0ZWdvcnlfY29kZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfliIbnsbvnvJbnoIHkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0KICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHRyZWVEYXRhKCkgewogICAgICAvLyDlsIblubPpk7rmlbDmja7ovazmjaLkuLrmoJHlvaLnu5PmnoQKICAgICAgY29uc3QgdHJlZSA9IFtdCiAgICAgIGNvbnN0IG1hcCA9IHt9CgogICAgICAvLyDlhYjliJvlu7rmiYDmnInoioLngrnnmoTmmKDlsIQKICAgICAgdGhpcy5saXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgbWFwW2l0ZW0uY2F0ZWdvcnlfaWRdID0geyAuLi5pdGVtLCBjaGlsZHJlbjogW10gfQogICAgICB9KQoKICAgICAgLy8g5p6E5bu65qCR5b2i57uT5p6ECiAgICAgIHRoaXMubGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnBhcmVudF9pZCA9PT0gMCkgewogICAgICAgICAgLy8g6aG257qn5YiG57G7CiAgICAgICAgICB0cmVlLnB1c2gobWFwW2l0ZW0uY2F0ZWdvcnlfaWRdKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDlrZDliIbnsbsKICAgICAgICAgIGlmIChtYXBbaXRlbS5wYXJlbnRfaWRdKSB7CiAgICAgICAgICAgIG1hcFtpdGVtLnBhcmVudF9pZF0uY2hpbGRyZW4ucHVzaChtYXBbaXRlbS5jYXRlZ29yeV9pZF0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQoKICAgICAgcmV0dXJuIHRyZWUKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWUKCiAgICAgIC8vIOaooeaLn+iAg+eglOaUv+ayu+WIhuexu+aVsOaNrgogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLmxpc3QgPSBbCiAgICAgICAgICAvLyDkuIDnuqfliIbnsbsKICAgICAgICAgIHsKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDEsCiAgICAgICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhicsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdtYXJ4aXNtJywKICAgICAgICAgICAgY2F0ZWdvcnlfZGVzYzogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhuamguiuuicsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAyNDUsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDEsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDIsCiAgICAgICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+avm+azveS4nOaAneaDs+WSjOS4reWbveeJueiJsuekvuS8muS4u+S5ieeQhuiuuuS9k+ezu+amguiuuicsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdtYW9pc20nLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn5q+b5rO95Lic5oCd5oOz5ZKM5Lit5Zu954m56Imy56S+5Lya5Li75LmJ55CG6K665L2T57O75qaC6K66JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDMxMiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMiwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBjYXRlZ29yeV9pZDogMywKICAgICAgICAgICAgcGFyZW50X2lkOiAwLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5Lit5Zu96L+R546w5Luj5Y+y57qy6KaBJywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ2hpc3RvcnknLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn5Lit5Zu96L+R546w5Luj5Y+y57qy6KaBJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE5OCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBjYXRlZ29yeV9pZDogNCwKICAgICAgICAgICAgcGFyZW50X2lkOiAwLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5oCd5oOz6YGT5b635LiO5rOV5rK7JywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ21vcmFsaXR5JywKICAgICAgICAgICAgY2F0ZWdvcnlfZGVzYzogJ+aAneaDs+mBk+W+t+S4juazleayuycsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxNjcsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDQsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDUsCiAgICAgICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+W9ouWKv+S4juaUv+etlicsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdwb2xpY3knLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn5b2i5Yq/5LiO5pS/562W5Lul5Y+K5b2T5Luj5LiW55WM57uP5rWO5LiO5pS/5rK7JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDEyMywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogNSwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgICB9LAogICAgICAgICAgLy8g6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CG55qE5LqM57qn5YiG57G7CiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMSwKICAgICAgICAgICAgcGFyZW50X2lkOiAxLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn6ams5YWL5oCd5Li75LmJ5ZOy5a2mJywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ21hcnhpc21fcGhpbG9zb3BoeScsCiAgICAgICAgICAgIGNhdGVnb3J5X2Rlc2M6ICfpqazlhYvmgJ3kuLvkuYnlk7Llrabln7rmnKzljp/nkIYnLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogNzgsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDEsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAyJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDEyLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfpqazlhYvmgJ3kuLvkuYnmlL/msrvnu4/mtY7lraYnLAogICAgICAgICAgICBjYXRlZ29yeV9jb2RlOiAnbWFyeGlzbV9lY29ub21pY3MnLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn6ams5YWL5oCd5Li75LmJ5pS/5rK757uP5rWO5a2m5Y6f55CGJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDQ1LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAyLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMicKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMywKICAgICAgICAgICAgcGFyZW50X2lkOiAxLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn56eR5a2m56S+5Lya5Li75LmJJywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ3NjaWVudGlmaWNfc29jaWFsaXNtJywKICAgICAgICAgICAgY2F0ZWdvcnlfZGVzYzogJ+enkeWtpuekvuS8muS4u+S5ieWfuuacrOWOn+eQhicsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAzMywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9LAogICAgICAgICAgLy8g6ams5YWL5oCd5Li75LmJ5ZOy5a2m55qE5LiJ57qn5YiG57G7CiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTEsCiAgICAgICAgICAgIHBhcmVudF9pZDogMTEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfllK/nianorronLAogICAgICAgICAgICBjYXRlZ29yeV9jb2RlOiAnbWF0ZXJpYWxpc20nLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn6ams5YWL5oCd5Li75LmJ5ZSv54mp6K66JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDM1LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMycKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTIsCiAgICAgICAgICAgIHBhcmVudF9pZDogMTEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfovqnor4Hms5UnLAogICAgICAgICAgICBjYXRlZ29yeV9jb2RlOiAnZGlhbGVjdGljcycsCiAgICAgICAgICAgIGNhdGVnb3J5X2Rlc2M6ICfpqazlhYvmgJ3kuLvkuYnovqnor4Hms5UnLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogNDIsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDIsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAzJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDExMywKICAgICAgICAgICAgcGFyZW50X2lkOiAxMSwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+iupOivhuiuuicsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdlcGlzdGVtb2xvZ3knLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn6ams5YWL5oCd5Li75LmJ6K6k6K+G6K66JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDM4LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAzLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMycKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTQsCiAgICAgICAgICAgIHBhcmVudF9pZDogMTEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfljoblj7Lop4InLAogICAgICAgICAgICBjYXRlZ29yeV9jb2RlOiAnaGlzdG9yaWNhbF9tYXRlcmlhbGlzbScsCiAgICAgICAgICAgIGNhdGVnb3J5X2Rlc2M6ICfpqazlhYvmgJ3kuLvkuYnljoblj7Lop4InLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogMzIsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDQsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAzJwogICAgICAgICAgfSwKICAgICAgICAgIC8vIOWUr+eJqeiuuueahOWbm+e6p+WIhuexuwogICAgICAgICAgewogICAgICAgICAgICBjYXRlZ29yeV9pZDogMTExMSwKICAgICAgICAgICAgcGFyZW50X2lkOiAxMTEsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfnianotKjmpoLlv7UnLAogICAgICAgICAgICBjYXRlZ29yeV9jb2RlOiAnbWF0dGVyX2NvbmNlcHQnLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn54mp6LSo55qE5ZOy5a2m5qaC5b+1JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDEyLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wNCcKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTEyLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDExMSwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+aEj+ivhuacrOi0qCcsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdjb25zY2lvdXNuZXNzX2Vzc2VuY2UnLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn5oSP6K+G55qE5pys6LSo5ZKM54m554K5JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE1LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAyLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wNCcKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTEzLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDExMSwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+eJqei0qOS4juaEj+ivhuWFs+ezuycsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdtYXR0ZXJfY29uc2Npb3VzbmVzc19yZWxhdGlvbicsCiAgICAgICAgICAgIGNhdGVnb3J5X2Rlc2M6ICfnianotKjkuI7mhI/or4bnmoTovqnor4HlhbPns7snLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogOCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDQnCiAgICAgICAgICB9LAogICAgICAgICAgLy8g6L6p6K+B5rOV55qE5Zub57qn5YiG57G7CiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTIxLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDExMiwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+iBlOezu+ingicsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdjb25uZWN0aW9uX3ZpZXcnLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn6ams5YWL5oCd5Li75LmJ6IGU57O76KeCJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE0LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wNCcKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAxMTIyLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDExMiwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+WPkeWxleingicsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICdkZXZlbG9wbWVudF92aWV3JywKICAgICAgICAgICAgY2F0ZWdvcnlfZGVzYzogJ+mprOWFi+aAneS4u+S5ieWPkeWxleingicsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxNiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMiwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDQnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBjYXRlZ29yeV9pZDogMTEyMywKICAgICAgICAgICAgcGFyZW50X2lkOiAxMTIsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfnn5vnm77op4TlvosnLAogICAgICAgICAgICBjYXRlZ29yeV9jb2RlOiAnY29udHJhZGljdGlvbl9sYXcnLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn5a+556uL57uf5LiA6KeE5b6LJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDEyLAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAzLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wNCcKICAgICAgICAgIH0sCiAgICAgICAgICAvLyDmr5vms73kuJzmgJ3mg7PnmoTkuoznuqfliIbnsbsKICAgICAgICAgIHsKICAgICAgICAgICAgY2F0ZWdvcnlfaWQ6IDIxLAogICAgICAgICAgICBwYXJlbnRfaWQ6IDIsCiAgICAgICAgICAgIGNhdGVnb3J5X25hbWU6ICfmr5vms73kuJzmgJ3mg7MnLAogICAgICAgICAgICBjYXRlZ29yeV9jb2RlOiAnbWFvX3Rob3VnaHQnLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAn5q+b5rO95Lic5oCd5oOz55qE5b2i5oiQ5ZKM5Y+R5bGVJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDg5LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMicKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAyMiwKICAgICAgICAgICAgcGFyZW50X2lkOiAyLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn6YKT5bCP5bmz55CG6K66JywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ2RlbmdfdGhlb3J5JywKICAgICAgICAgICAgY2F0ZWdvcnlfZGVzYzogJ+mCk+Wwj+W5s+eQhuiuuicsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiA2NywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMiwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBjYXRlZ29yeV9pZDogMjMsCiAgICAgICAgICAgIHBhcmVudF9pZDogMiwKICAgICAgICAgICAgY2F0ZWdvcnlfbmFtZTogJ+S4ieS4quS7o+ihqOmHjeimgeaAneaDsycsCiAgICAgICAgICAgIGNhdGVnb3J5X2NvZGU6ICd0aHJlZV9yZXByZXNlbnRzJywKICAgICAgICAgICAgY2F0ZWdvcnlfZGVzYzogJ+S4ieS4quS7o+ihqOmHjeimgeaAneaDsycsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiA0NywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9LAogICAgICAgICAgLy8g5Lit5Zu96L+R546w5Luj5Y+y55qE5LqM57qn5YiG57G7CiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAzMSwKICAgICAgICAgICAgcGFyZW50X2lkOiAzLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5pen5rCR5Li75Li75LmJ6Z2p5ZG95pe25pyfJywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ29sZF9kZW1vY3JhdGljX3Jldm9sdXRpb24nLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAnMTg0MC0xOTE55bm05pen5rCR5Li75Li75LmJ6Z2p5ZG95pe25pyfJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDU2LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMicKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAzMiwKICAgICAgICAgICAgcGFyZW50X2lkOiAzLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn5paw5rCR5Li75Li75LmJ6Z2p5ZG95pe25pyfJywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ25ld19kZW1vY3JhdGljX3Jldm9sdXRpb24nLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAnMTkxOS0xOTQ55bm05paw5rCR5Li75Li75LmJ6Z2p5ZG95pe25pyfJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDc4LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAyLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMicKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGNhdGVnb3J5X2lkOiAzMywKICAgICAgICAgICAgcGFyZW50X2lkOiAzLAogICAgICAgICAgICBjYXRlZ29yeV9uYW1lOiAn56S+5Lya5Li75LmJ6Z2p5ZG95ZKM5bu66K6+5pe25pyfJywKICAgICAgICAgICAgY2F0ZWdvcnlfY29kZTogJ3NvY2lhbGlzdF9jb25zdHJ1Y3Rpb24nLAogICAgICAgICAgICBjYXRlZ29yeV9kZXNjOiAnMTk0OeW5tOS7peadpeekvuS8muS4u+S5iemdqeWRveWSjOW7uuiuvuaXtuacnycsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiA0NCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICAgIHRoaXMudG90YWwgPSB0aGlzLmxpc3QubGVuZ3RoCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlCgogICAgICAgIC8vIOabtOaWsOeItuWIhuexu+mAiemhue+8iOWPquaYvuekuuWPr+S7peS9nOS4uueItue6p+eahOWIhuexu++8iQogICAgICAgIHRoaXMucGFyZW50T3B0aW9ucyA9IHRoaXMubGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnBhcmVudF9pZCA9PT0gMCB8fCBpdGVtLnBhcmVudF9pZCA9PT0gMSB8fCBpdGVtLnBhcmVudF9pZCA9PT0gMiB8fCBpdGVtLnBhcmVudF9pZCA9PT0gMykKICAgICAgfSwgNTAwKQogICAgfSwKICAgIGhhbmRsZUZpbHRlcigpIHsKICAgICAgdGhpcy5saXN0UXVlcnkucGFnZSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICByZXNldFRlbXAoKSB7CiAgICAgIHRoaXMudGVtcCA9IHsKICAgICAgICBjYXRlZ29yeV9pZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICBjYXRlZ29yeV9uYW1lOiAnJywKICAgICAgICBjYXRlZ29yeV9jb2RlOiAnJywKICAgICAgICBjYXRlZ29yeV9kZXNjOiAnJywKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgIHNvcnRfb3JkZXI6IDAKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUNyZWF0ZSgpIHsKICAgICAgdGhpcy5yZXNldFRlbXAoKQogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICdjcmVhdGUnCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICB9KQogICAgfSwKICAgIGNyZWF0ZURhdGEoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfliJvlu7rmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy50ZW1wID0gT2JqZWN0LmFzc2lnbih7fSwgcm93KQogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICd1cGRhdGUnCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICB9KQogICAgfSwKICAgIHVwZGF0ZURhdGEoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmm7TmlrDmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6K+l5YiG57G75ZCX77yfJywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5Yqf77yI5qih5ouf77yJJywKICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["categories.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "categories.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索分类名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加分类\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"category_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"category_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"分类名称\" prop=\"category_name\" min-width=\"200\" />\n      <el-table-column label=\"分类编码\" prop=\"category_code\" min-width=\"150\" />\n      <el-table-column label=\"分类描述\" prop=\"category_desc\" min-width=\"200\" />\n      <el-table-column label=\"题目数量\" prop=\"question_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父分类\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父分类\" style=\"width: 100%\">\n            <el-option label=\"顶级分类\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.category_id\"\n              :label=\"item.category_name\"\n              :value=\"item.category_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"分类名称\" prop=\"category_name\">\n          <el-input v-model=\"temp.category_name\" />\n        </el-form-item>\n        <el-form-item label=\"分类编码\" prop=\"category_code\">\n          <el-input v-model=\"temp.category_code\" />\n        </el-form-item>\n        <el-form-item label=\"分类描述\" prop=\"category_desc\">\n          <el-input v-model=\"temp.category_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionCategories',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        category_id: undefined,\n        parent_id: 0,\n        category_name: '',\n        category_code: '',\n        category_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑分类',\n        create: '添加分类'\n      },\n      rules: {\n        category_name: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],\n        category_code: [{ required: true, message: '分类编码不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  computed: {\n    treeData() {\n      // 将平铺数据转换为树形结构\n      const tree = []\n      const map = {}\n\n      // 先创建所有节点的映射\n      this.list.forEach(item => {\n        map[item.category_id] = { ...item, children: [] }\n      })\n\n      // 构建树形结构\n      this.list.forEach(item => {\n        if (item.parent_id === 0) {\n          // 顶级分类\n          tree.push(map[item.category_id])\n        } else {\n          // 子分类\n          if (map[item.parent_id]) {\n            map[item.parent_id].children.push(map[item.category_id])\n          }\n        }\n      })\n\n      return tree\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n\n      // 模拟考研政治分类数据\n      setTimeout(() => {\n        this.list = [\n          // 一级分类\n          {\n            category_id: 1,\n            parent_id: 0,\n            category_name: '马克思主义基本原理',\n            category_code: 'marxism',\n            category_desc: '马克思主义基本原理概论',\n            question_count: 245,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 2,\n            parent_id: 0,\n            category_name: '毛泽东思想和中国特色社会主义理论体系概论',\n            category_code: 'maoism',\n            category_desc: '毛泽东思想和中国特色社会主义理论体系概论',\n            question_count: 312,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 3,\n            parent_id: 0,\n            category_name: '中国近现代史纲要',\n            category_code: 'history',\n            category_desc: '中国近现代史纲要',\n            question_count: 198,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 4,\n            parent_id: 0,\n            category_name: '思想道德与法治',\n            category_code: 'morality',\n            category_desc: '思想道德与法治',\n            question_count: 167,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 5,\n            parent_id: 0,\n            category_name: '形势与政策',\n            category_code: 'policy',\n            category_desc: '形势与政策以及当代世界经济与政治',\n            question_count: 123,\n            status: 'active',\n            sort_order: 5,\n            created_at: '2024-01-01'\n          },\n          // 马克思主义基本原理的二级分类\n          {\n            category_id: 11,\n            parent_id: 1,\n            category_name: '马克思主义哲学',\n            category_code: 'marxism_philosophy',\n            category_desc: '马克思主义哲学基本原理',\n            question_count: 78,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 12,\n            parent_id: 1,\n            category_name: '马克思主义政治经济学',\n            category_code: 'marxism_economics',\n            category_desc: '马克思主义政治经济学原理',\n            question_count: 45,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 13,\n            parent_id: 1,\n            category_name: '科学社会主义',\n            category_code: 'scientific_socialism',\n            category_desc: '科学社会主义基本原理',\n            question_count: 33,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          },\n          // 马克思主义哲学的三级分类\n          {\n            category_id: 111,\n            parent_id: 11,\n            category_name: '唯物论',\n            category_code: 'materialism',\n            category_desc: '马克思主义唯物论',\n            question_count: 35,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 112,\n            parent_id: 11,\n            category_name: '辩证法',\n            category_code: 'dialectics',\n            category_desc: '马克思主义辩证法',\n            question_count: 42,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 113,\n            parent_id: 11,\n            category_name: '认识论',\n            category_code: 'epistemology',\n            category_desc: '马克思主义认识论',\n            question_count: 38,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 114,\n            parent_id: 11,\n            category_name: '历史观',\n            category_code: 'historical_materialism',\n            category_desc: '马克思主义历史观',\n            question_count: 32,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-03'\n          },\n          // 唯物论的四级分类\n          {\n            category_id: 1111,\n            parent_id: 111,\n            category_name: '物质概念',\n            category_code: 'matter_concept',\n            category_desc: '物质的哲学概念',\n            question_count: 12,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1112,\n            parent_id: 111,\n            category_name: '意识本质',\n            category_code: 'consciousness_essence',\n            category_desc: '意识的本质和特点',\n            question_count: 15,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1113,\n            parent_id: 111,\n            category_name: '物质与意识关系',\n            category_code: 'matter_consciousness_relation',\n            category_desc: '物质与意识的辩证关系',\n            question_count: 8,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-04'\n          },\n          // 辩证法的四级分类\n          {\n            category_id: 1121,\n            parent_id: 112,\n            category_name: '联系观',\n            category_code: 'connection_view',\n            category_desc: '马克思主义联系观',\n            question_count: 14,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1122,\n            parent_id: 112,\n            category_name: '发展观',\n            category_code: 'development_view',\n            category_desc: '马克思主义发展观',\n            question_count: 16,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1123,\n            parent_id: 112,\n            category_name: '矛盾规律',\n            category_code: 'contradiction_law',\n            category_desc: '对立统一规律',\n            question_count: 12,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-04'\n          },\n          // 毛泽东思想的二级分类\n          {\n            category_id: 21,\n            parent_id: 2,\n            category_name: '毛泽东思想',\n            category_code: 'mao_thought',\n            category_desc: '毛泽东思想的形成和发展',\n            question_count: 89,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 22,\n            parent_id: 2,\n            category_name: '邓小平理论',\n            category_code: 'deng_theory',\n            category_desc: '邓小平理论',\n            question_count: 67,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 23,\n            parent_id: 2,\n            category_name: '三个代表重要思想',\n            category_code: 'three_represents',\n            category_desc: '三个代表重要思想',\n            question_count: 47,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          },\n          // 中国近现代史的二级分类\n          {\n            category_id: 31,\n            parent_id: 3,\n            category_name: '旧民主主义革命时期',\n            category_code: 'old_democratic_revolution',\n            category_desc: '1840-1919年旧民主主义革命时期',\n            question_count: 56,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 32,\n            parent_id: 3,\n            category_name: '新民主主义革命时期',\n            category_code: 'new_democratic_revolution',\n            category_desc: '1919-1949年新民主主义革命时期',\n            question_count: 78,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 33,\n            parent_id: 3,\n            category_name: '社会主义革命和建设时期',\n            category_code: 'socialist_construction',\n            category_desc: '1949年以来社会主义革命和建设时期',\n            question_count: 44,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n\n        // 更新父分类选项（只显示可以作为父级的分类）\n        this.parentOptions = this.list.filter(item => item.parent_id === 0 || item.parent_id === 1 || item.parent_id === 2 || item.parent_id === 3)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        category_id: undefined,\n        parent_id: 0,\n        category_name: '',\n        category_code: '',\n        category_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该分类吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}