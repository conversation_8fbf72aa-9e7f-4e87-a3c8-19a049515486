const express = require('express');
const { query } = require('../config/database');
const { authenticateAdmin } = require('./auth');

const router = express.Router();

// Mock 分类数据
const categories = [
  {
    category_id: 1,
    parent_id: 0,
    category_name: '考研英语',
    category_desc: '考研英语题库',
    sort_order: 1,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    category_id: 2,
    parent_id: 1,
    category_name: '英语一',
    category_desc: '考研英语一',
    sort_order: 2,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    category_id: 3,
    parent_id: 1,
    category_name: '英语二',
    category_desc: '考研英语二',
    sort_order: 3,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    category_id: 4,
    parent_id: 2,
    category_name: '阅读理解',
    category_desc: '阅读理解题型',
    sort_order: 4,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    category_id: 5,
    parent_id: 2,
    category_name: '完型填空',
    category_desc: '完型填空题型',
    sort_order: 5,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    category_id: 6,
    parent_id: 2,
    category_name: '翻译',
    category_desc: '翻译题型',
    sort_order: 6,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    category_id: 7,
    parent_id: 2,
    category_name: '写作',
    category_desc: '写作题型',
    sort_order: 7,
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  }
];

// 构建分类树结构
function buildCategoryTree(categories, parentId = 0) {
  const tree = [];
  
  categories
    .filter(cat => cat.parent_id === parentId && cat.status === 'active')
    .sort((a, b) => a.sort_order - b.sort_order)
    .forEach(category => {
      const children = buildCategoryTree(categories, category.category_id);
      const categoryNode = {
        ...category,
        children: children.length > 0 ? children : []
      };
      tree.push(categoryNode);
    });
  
  return tree;
}

// 获取分类树
router.get('/tree', async (req, res) => {
  try {
    const tree = buildCategoryTree(categories);

    res.json({
      success: true,
      data: tree,
      message: '获取分类树成功'
    });

  } catch (error) {
    console.error('获取分类树错误:', error);
    res.status(500).json({
      success: false,
      message: '获取分类树失败'
    });
  }
});

// 获取所有分类（平铺列表）
router.get('/', async (req, res) => {
  try {
    const { status = 'active', parent_id } = req.query;

    let filteredCategories = categories.filter(cat => cat.status === status);

    if (parent_id !== undefined) {
      filteredCategories = filteredCategories.filter(cat => 
        cat.parent_id === parseInt(parent_id)
      );
    }

    // 按排序字段排序
    filteredCategories.sort((a, b) => a.sort_order - b.sort_order);

    res.json({
      success: true,
      data: filteredCategories,
      message: '获取分类列表成功'
    });

  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取分类列表失败'
    });
  }
});

// 获取单个分类详情
router.get('/:id', async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    const category = categories.find(cat => cat.category_id === categoryId);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 获取父分类信息
    let parentCategory = null;
    if (category.parent_id > 0) {
      parentCategory = categories.find(cat => cat.category_id === category.parent_id);
    }

    // 获取子分类
    const children = categories
      .filter(cat => cat.parent_id === categoryId && cat.status === 'active')
      .sort((a, b) => a.sort_order - b.sort_order);

    res.json({
      success: true,
      data: {
        ...category,
        parent_category: parentCategory,
        children: children
      },
      message: '获取分类详情成功'
    });

  } catch (error) {
    console.error('获取分类详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取分类详情失败'
    });
  }
});

// 创建分类（管理员）
router.post('/', authenticateAdmin, async (req, res) => {
  try {
    const {
      parent_id = 0,
      category_name,
      category_desc = '',
      sort_order = 0
    } = req.body;

    if (!category_name) {
      return res.status(400).json({
        success: false,
        message: '分类名称不能为空'
      });
    }

    // 检查同级分类名称是否重复
    const existingCategory = categories.find(cat => 
      cat.parent_id === parseInt(parent_id) && 
      cat.category_name === category_name &&
      cat.status === 'active'
    );

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: '同级分类中已存在相同名称的分类'
      });
    }

    // 如果有父分类，检查父分类是否存在
    if (parent_id > 0) {
      const parentCategory = categories.find(cat => 
        cat.category_id === parseInt(parent_id) && cat.status === 'active'
      );
      
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: '父分类不存在'
        });
      }
    }

    const newCategory = {
      category_id: categories.length + 1,
      parent_id: parseInt(parent_id),
      category_name,
      category_desc,
      sort_order: parseInt(sort_order),
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    categories.push(newCategory);

    res.json({
      success: true,
      data: newCategory,
      message: '创建分类成功'
    });

  } catch (error) {
    console.error('创建分类错误:', error);
    res.status(500).json({
      success: false,
      message: '创建分类失败'
    });
  }
});

// 更新分类（管理员）
router.put('/:id', authenticateAdmin, async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    const {
      category_name,
      category_desc,
      sort_order,
      status
    } = req.body;

    const categoryIndex = categories.findIndex(cat => cat.category_id === categoryId);
    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    const category = categories[categoryIndex];

    // 检查分类名称是否重复（排除自己）
    if (category_name && category_name !== category.category_name) {
      const existingCategory = categories.find(cat => 
        cat.parent_id === category.parent_id && 
        cat.category_name === category_name &&
        cat.category_id !== categoryId &&
        cat.status === 'active'
      );

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: '同级分类中已存在相同名称的分类'
        });
      }
    }

    // 更新分类信息
    if (category_name) category.category_name = category_name;
    if (category_desc !== undefined) category.category_desc = category_desc;
    if (sort_order !== undefined) category.sort_order = parseInt(sort_order);
    if (status) category.status = status;
    category.updated_at = new Date().toISOString();

    res.json({
      success: true,
      data: category,
      message: '更新分类成功'
    });

  } catch (error) {
    console.error('更新分类错误:', error);
    res.status(500).json({
      success: false,
      message: '更新分类失败'
    });
  }
});

// 删除分类（管理员）
router.delete('/:id', authenticateAdmin, async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);

    const categoryIndex = categories.findIndex(cat => cat.category_id === categoryId);
    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 检查是否有子分类
    const hasChildren = categories.some(cat => 
      cat.parent_id === categoryId && cat.status === 'active'
    );

    if (hasChildren) {
      return res.status(400).json({
        success: false,
        message: '该分类下还有子分类，无法删除'
      });
    }

    // 软删除（将状态设为disabled）
    categories[categoryIndex].status = 'disabled';
    categories[categoryIndex].updated_at = new Date().toISOString();

    res.json({
      success: true,
      message: '删除分类成功'
    });

  } catch (error) {
    console.error('删除分类错误:', error);
    res.status(500).json({
      success: false,
      message: '删除分类失败'
    });
  }
});

module.exports = router;
