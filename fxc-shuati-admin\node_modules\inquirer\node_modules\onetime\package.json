{"_from": "onetime@^5.1.0", "_id": "onetime@5.1.2", "_inBundle": false, "_integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "_location": "/inquirer/onetime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "onetime@^5.1.0", "name": "onetime", "escapedName": "onetime", "rawSpec": "^5.1.0", "saveSpec": null, "fetchSpec": "^5.1.0"}, "_requiredBy": ["/inquirer/restore-cursor"], "_resolved": "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz", "_shasum": "d0e96ebb56b07476df1dd9c4806e5237985ca45e", "_spec": "onetime@^5.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\inquirer\\node_modules\\restore-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "bundleDependencies": false, "dependencies": {"mimic-fn": "^2.1.0"}, "deprecated": false, "description": "Ensure a function is only called once", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/onetime#readme", "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "license": "MIT", "name": "onetime", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "5.1.2"}