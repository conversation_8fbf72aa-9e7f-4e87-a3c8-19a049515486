{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue", "mtime": 1752630804729}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUXVlc3Rpb25VcGxvYWQnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cGxvYWRVcmw6ICcvYXBpL3F1ZXN0aW9ucy9pbXBvcnQnLCAvLyDlrp7pmYXpobnnm67kuK3nmoTkuIrkvKDmjqXlj6MKICAgICAgZmlsZUxpc3Q6IFtdLAogICAgICB1cGxvYWRpbmc6IGZhbHNlLAogICAgICBzaG93UHJvZ3Jlc3M6IGZhbHNlLAogICAgICB1cGxvYWRQcm9ncmVzczogMCwKICAgICAgdXBsb2FkU3RhdHVzOiAnJywKICAgICAgcHJvZ3Jlc3NUZXh0OiAnJywKICAgICAgaW1wb3J0UmVzdWx0OiBudWxsCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBkb3dubG9hZFRlbXBsYXRlKCkgewogICAgICAvLyDliJvlu7rmqKHmnb/mlbDmja4KICAgICAgY29uc3QgdGVtcGxhdGVEYXRhID0gWwogICAgICAgIHsKICAgICAgICAgICfpopjnm67nsbvlnosnOiAnc2luZ2xlX2Nob2ljZScsCiAgICAgICAgICAn6Zq+5bqm562J57qnJzogJ21lZGl1bScsCiAgICAgICAgICAn5YiG57G7SUQnOiAnMTExMScsCiAgICAgICAgICAn5qCH562+SUQnOiAnMSw5LDcnLAogICAgICAgICAgJ+mimOebruWGheWuuSc6ICfpqazlhYvmgJ3kuLvkuYnlk7LlrabnmoTln7rmnKzpl67popjmmK/vvIjvvIknLAogICAgICAgICAgJ+mAiemhuUEnOiAn54mp6LSo5ZKM5oSP6K+G55qE5YWz57O76Zeu6aKYJywKICAgICAgICAgICfpgInpoblCJzogJ+eQhuiuuuWSjOWunui3teeahOWFs+ezu+mXrumimCcsCiAgICAgICAgICAn6YCJ6aG5Qyc6ICfkuKrkurrlkoznpL7kvJrnmoTlhbPns7vpl67popgnLAogICAgICAgICAgJ+mAiemhuUQnOiAn6Ieq55Sx5ZKM5b+F54S255qE5YWz57O76Zeu6aKYJywKICAgICAgICAgICfpgInpoblFJzogJycsCiAgICAgICAgICAn6YCJ6aG5Ric6ICcnLAogICAgICAgICAgJ+ato+ehruetlOahiCc6ICdBJywKICAgICAgICAgICfpopjnm67op6PmnpAnOiAn6ams5YWL5oCd5Li75LmJ5ZOy5a2m55qE5Z+65pys6Zeu6aKY5piv54mp6LSo5ZKM5oSP6K+G55qE5YWz57O76Zeu6aKY77yM6L+Z5piv5ZOy5a2m55qE5qC55pys6Zeu6aKY44CC5a6D5YyF5ous5Lik5Liq5pa56Z2i77ya56ys5LiA77yM54mp6LSo5ZKM5oSP6K+G5L2V6ICF5Li656ys5LiA5oCn77yb56ys5LqM77yM54mp6LSo5ZKM5oSP6K+G5piv5ZCm5YW35pyJ5ZCM5LiA5oCn44CCJywKICAgICAgICAgICfliIblgLwnOiAnMicsCiAgICAgICAgICAn5o6S5bqPJzogJzEnLAogICAgICAgICAgJ+eKtuaAgSc6ICdhY3RpdmUnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICAn6aKY55uu57G75Z6LJzogJ3NpbmdsZV9jaG9pY2UnLAogICAgICAgICAgJ+mavuW6puetiee6pyc6ICdlYXN5JywKICAgICAgICAgICfliIbnsbtJRCc6ICcxMTExJywKICAgICAgICAgICfmoIfnrb5JRCc6ICcxLDksMTYnLAogICAgICAgICAgJ+mimOebruWGheWuuSc6ICfnianotKjnmoTllK/kuIDnibnmgKfmmK/vvIjvvIknLAogICAgICAgICAgJ+mAiemhuUEnOiAn6L+Q5Yqo5oCnJywKICAgICAgICAgICfpgInpoblCJzogJ+WuouinguWunuWcqOaApycsCiAgICAgICAgICAn6YCJ6aG5Qyc6ICflj6/nn6XmgKcnLAogICAgICAgICAgJ+mAiemhuUQnOiAn57ud5a+55oCnJywKICAgICAgICAgICfpgInpoblFJzogJycsCiAgICAgICAgICAn6YCJ6aG5Ric6ICcnLAogICAgICAgICAgJ+ato+ehruetlOahiCc6ICdCJywKICAgICAgICAgICfpopjnm67op6PmnpAnOiAn54mp6LSo55qE5ZSv5LiA54m55oCn5piv5a6i6KeC5a6e5Zyo5oCn44CC6L+Z5piv54mp6LSo5qaC5b+155qE5qC45b+D77yM5oyH54mp6LSo5LiN5L6d6LWW5LqO5Lq655qE5oSP6K+G6ICM5a2Y5Zyo77yM5bm26IO95Li65Lq655qE5oSP6K+G5omA5Y+N5pig44CCJywKICAgICAgICAgICfliIblgLwnOiAnMicsCiAgICAgICAgICAn5o6S5bqPJzogJzInLAogICAgICAgICAgJ+eKtuaAgSc6ICdhY3RpdmUnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICAn6aKY55uu57G75Z6LJzogJ211bHRpcGxlX2Nob2ljZScsCiAgICAgICAgICAn6Zq+5bqm562J57qnJzogJ21lZGl1bScsCiAgICAgICAgICAn5YiG57G7SUQnOiAnMTExMicsCiAgICAgICAgICAn5qCH562+SUQnOiAnMSw5LDcnLAogICAgICAgICAgJ+mimOebruWGheWuuSc6ICfmhI/or4bnmoTmnKzotKjmmK/vvIjvvIknLAogICAgICAgICAgJ+mAiemhuUEnOiAn5oSP6K+G5piv5Lq66ISR55qE5py66IO9JywKICAgICAgICAgICfpgInpoblCJzogJ+aEj+ivhuaYr+WuouinguWtmOWcqOeahOWPjeaYoCcsCiAgICAgICAgICAn6YCJ6aG5Qyc6ICfmhI/or4bmmK/npL7kvJrnmoTkuqfniaknLAogICAgICAgICAgJ+mAiemhuUQnOiAn5oSP6K+G5YW35pyJ5Li76KeC6IO95Yqo5oCnJywKICAgICAgICAgICfpgInpoblFJzogJycsCiAgICAgICAgICAn6YCJ6aG5Ric6ICcnLAogICAgICAgICAgJ+ato+ehruetlOahiCc6ICdBLEIsQyxEJywKICAgICAgICAgICfpopjnm67op6PmnpAnOiAn5oSP6K+G55qE5pys6LSo5YyF5ous77ya77yIMe+8ieaEj+ivhuaYr+S6uuiEkeeahOacuuiDve+8m++8iDLvvInmhI/or4bmmK/lrqLop4LlrZjlnKjnmoTlj43mmKDvvJvvvIgz77yJ5oSP6K+G5piv56S+5Lya55qE5Lqn54mp77yb77yINO+8ieaEj+ivhuWFt+acieS4u+inguiDveWKqOaAp+OAgui/meWbm+S4quaWuemdouaehOaIkOS6huaEj+ivhuacrOi0qOeahOWujOaVtOWGheWuueOAgicsCiAgICAgICAgICAn5YiG5YC8JzogJzInLAogICAgICAgICAgJ+aOkuW6jyc6ICczJywKICAgICAgICAgICfnirbmgIEnOiAnYWN0aXZlJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgJ+mimOebruexu+Weiyc6ICd0cnVlX2ZhbHNlJywKICAgICAgICAgICfpmr7luqbnrYnnuqcnOiAnZWFzeScsCiAgICAgICAgICAn5YiG57G7SUQnOiAnMTExMScsCiAgICAgICAgICAn5qCH562+SUQnOiAnMSw5JywKICAgICAgICAgICfpopjnm67lhoXlrrknOiAn6ams5YWL5oCd5Li75LmJ5ZOy5a2m6K6k5Li677yM5LiW55WM55qE55yf5q2j57uf5LiA5oCn5Zyo5LqO5a6D55qE54mp6LSo5oCn44CCJywKICAgICAgICAgICfpgInpoblBJzogJycsCiAgICAgICAgICAn6YCJ6aG5Qic6ICcnLAogICAgICAgICAgJ+mAiemhuUMnOiAnJywKICAgICAgICAgICfpgInpoblEJzogJycsCiAgICAgICAgICAn6YCJ6aG5RSc6ICcnLAogICAgICAgICAgJ+mAiemhuUYnOiAnJywKICAgICAgICAgICfmraPnoa7nrZTmoYgnOiAndHJ1ZScsCiAgICAgICAgICAn6aKY55uu6Kej5p6QJzogJ+ato+ehruOAgumprOWFi+aAneS4u+S5ieWTsuWtpuiupOS4uu+8jOS4lueVjOeahOecn+ato+e7n+S4gOaAp+WcqOS6juWug+eahOeJqei0qOaAp+OAgui/meaYr+mprOWFi+aAneS4u+S5ieS4gOWFg+iuuueahOWfuuacrOingueCue+8jOW8uuiwg+S4lueVjOS4h+eJqemDveaYr+eJqei0qOeahOS4jeWQjOihqOeOsOW9ouW8j+OAgicsCiAgICAgICAgICAn5YiG5YC8JzogJzInLAogICAgICAgICAgJ+aOkuW6jyc6ICc0JywKICAgICAgICAgICfnirbmgIEnOiAnYWN0aXZlJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgJ+mimOebruexu+Weiyc6ICdmaWxsX2JsYW5rJywKICAgICAgICAgICfpmr7luqbnrYnnuqcnOiAnZWFzeScsCiAgICAgICAgICAn5YiG57G7SUQnOiAnMTEyMycsCiAgICAgICAgICAn5qCH562+SUQnOiAnMSw5LDcnLAogICAgICAgICAgJ+mimOebruWGheWuuSc6ICfnn5vnm77nmoTln7rmnKzlsZ7mgKfmmK9fX19fX+WSjF9fX19f44CCJywKICAgICAgICAgICfpgInpoblBJzogJycsCiAgICAgICAgICAn6YCJ6aG5Qic6ICcnLAogICAgICAgICAgJ+mAiemhuUMnOiAnJywKICAgICAgICAgICfpgInpoblEJzogJycsCiAgICAgICAgICAn6YCJ6aG5RSc6ICcnLAogICAgICAgICAgJ+mAiemhuUYnOiAnJywKICAgICAgICAgICfmraPnoa7nrZTmoYgnOiAn5ZCM5LiA5oCn77yb5paX5LqJ5oCnJywKICAgICAgICAgICfpopjnm67op6PmnpAnOiAn55+b55u+55qE5Z+65pys5bGe5oCn5piv5ZCM5LiA5oCn5ZKM5paX5LqJ5oCn44CC5ZCM5LiA5oCn5piv5oyH55+b55u+5Y+M5pa555u45LqS5L6d5a2Y44CB55u45LqS6LSv6YCa55qE5oCn6LSo5ZKM6LaL5Yq/77yb5paX5LqJ5oCn5piv5oyH55+b55u+5Y+M5pa555u45LqS5o6S5pal44CB55u45LqS5a+556uL55qE5oCn6LSo5ZKM6LaL5Yq/44CCJywKICAgICAgICAgICfliIblgLwnOiAnMicsCiAgICAgICAgICAn5o6S5bqPJzogJzUnLAogICAgICAgICAgJ+eKtuaAgSc6ICdhY3RpdmUnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICAn6aKY55uu57G75Z6LJzogJ3Nob3J0X2Fuc3dlcicsCiAgICAgICAgICAn6Zq+5bqm562J57qnJzogJ2hhcmQnLAogICAgICAgICAgJ+WIhuexu0lEJzogJzExMycsCiAgICAgICAgICAn5qCH562+SUQnOiAnMSw5LDYnLAogICAgICAgICAgJ+mimOebruWGheWuuSc6ICfnroDov7Dlrp7ot7XmmK/orqTor4bln7rnoYDnmoTkuLvopoHooajnjrDjgIInLAogICAgICAgICAgJ+mAiemhuUEnOiAnJywKICAgICAgICAgICfpgInpoblCJzogJycsCiAgICAgICAgICAn6YCJ6aG5Qyc6ICcnLAogICAgICAgICAgJ+mAiemhuUQnOiAnJywKICAgICAgICAgICfpgInpoblFJzogJycsCiAgICAgICAgICAn6YCJ6aG5Ric6ICcnLAogICAgICAgICAgJ+ato+ehruetlOahiCc6ICflrp7ot7XmmK/orqTor4bnmoTln7rnoYDvvIzkuLvopoHooajnjrDlnKjlm5vkuKrmlrnpnaLvvJrvvIgx77yJ5a6e6Le15piv6K6k6K+G55qE5p2l5rqQ77yb77yIMu+8ieWunui3teaYr+iupOivhuWPkeWxleeahOWKqOWKm++8m++8iDPvvInlrp7ot7XmmK/mo4DpqozorqTor4bnnJ/nkIbmgKfnmoTllK/kuIDmoIflh4bvvJvvvIg077yJ5a6e6Le15piv6K6k6K+G55qE55uu55qE44CCJywKICAgICAgICAgICfpopjnm67op6PmnpAnOiAn6L+Z6YGT6aKY6ICD5p+l5a6e6Le15LiO6K6k6K+G55qE5YWz57O744CC6KaB5LuO5Zub5Liq5pa56Z2i5p2l5Zue562U77ya5p2l5rqQ44CB5Yqo5Yqb44CB5qCH5YeG44CB55uu55qE44CC5q+P5Liq5pa56Z2i6YO96KaB566A6KaB6K+05piO5YW25ZCr5LmJ44CCJywKICAgICAgICAgICfliIblgLwnOiAnNicsCiAgICAgICAgICAn5o6S5bqPJzogJzYnLAogICAgICAgICAgJ+eKtuaAgSc6ICdhY3RpdmUnCiAgICAgICAgfQogICAgICBdCgogICAgICAvLyDovazmjaLkuLpDU1bmoLzlvI/lubbkuIvovb0KICAgICAgdGhpcy5kb3dubG9hZENTVih0ZW1wbGF0ZURhdGEsICfpopjnm67lr7zlhaXmqKHmnb8uY3N2JykKICAgIH0sCgogICAgZG93bmxvYWRDU1YoZGF0YSwgZmlsZW5hbWUpIHsKICAgICAgY29uc3QgY3N2Q29udGVudCA9IHRoaXMuY29udmVydFRvQ1NWKGRhdGEpCiAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbJ1x1ZmVmZicgKyBjc3ZDb250ZW50XSwgeyB0eXBlOiAndGV4dC9jc3Y7Y2hhcnNldD11dGYtODsnIH0pCiAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJykKCiAgICAgIGlmIChsaW5rLmRvd25sb2FkICE9PSB1bmRlZmluZWQpIHsKICAgICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpCiAgICAgICAgbGluay5zZXRBdHRyaWJ1dGUoJ2hyZWYnLCB1cmwpCiAgICAgICAgbGluay5zZXRBdHRyaWJ1dGUoJ2Rvd25sb2FkJywgZmlsZW5hbWUpCiAgICAgICAgbGluay5zdHlsZS52aXNpYmlsaXR5ID0gJ2hpZGRlbicKICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspCiAgICAgICAgbGluay5jbGljaygpCiAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQogICAgICB9CiAgICB9LAoKICAgIGNvbnZlcnRUb0NTVihkYXRhKSB7CiAgICAgIGlmICghZGF0YSB8fCBkYXRhLmxlbmd0aCA9PT0gMCkgcmV0dXJuICcnCgogICAgICBjb25zdCBoZWFkZXJzID0gT2JqZWN0LmtleXMoZGF0YVswXSkKICAgICAgY29uc3QgY3N2SGVhZGVycyA9IGhlYWRlcnMuam9pbignLCcpCgogICAgICBjb25zdCBjc3ZSb3dzID0gZGF0YS5tYXAocm93ID0+IHsKICAgICAgICByZXR1cm4gaGVhZGVycy5tYXAoaGVhZGVyID0+IHsKICAgICAgICAgIGNvbnN0IHZhbHVlID0gcm93W2hlYWRlcl0gfHwgJycKICAgICAgICAgIHJldHVybiBgIiR7dmFsdWUudG9TdHJpbmcoKS5yZXBsYWNlKC8iL2csICciIicpfSJgCiAgICAgICAgfSkuam9pbignLCcpCiAgICAgIH0pCgogICAgICByZXR1cm4gW2NzdkhlYWRlcnMsIC4uLmNzdlJvd3NdLmpvaW4oJ1xuJykKICAgIH0sCgogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgaXNFeGNlbCA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fAogICAgICAgICAgICAgICAgICAgICBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwnCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwCgogICAgICBpZiAoIWlzRXhjZWwpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBFeGNlbOaWh+S7tiEnKQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICAgIGlmICghaXNMdDEwTSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzEwTUIhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQogICAgICByZXR1cm4gdHJ1ZQogICAgfSwKCiAgICBzdWJtaXRVcGxvYWQoKSB7CiAgICAgIGlmICh0aGlzLmZpbGVMaXN0Lmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5LiK5Lyg55qE5paH5Lu2JykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMuc2hvd1Byb2dyZXNzID0gdHJ1ZQogICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMAogICAgICB0aGlzLnVwbG9hZFN0YXR1cyA9ICcnCiAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+W8gOWni+S4iuS8oOaWh+S7ti4uLicKCiAgICAgIC8vIOaooeaLn+S4iuS8oOi/h+eoiwogICAgICB0aGlzLnNpbXVsYXRlVXBsb2FkKCkKICAgIH0sCgogICAgc2ltdWxhdGVVcGxvYWQoKSB7CiAgICAgIGNvbnN0IHRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgKz0gTWF0aC5yYW5kb20oKSAqIDE1CgogICAgICAgIGlmICh0aGlzLnVwbG9hZFByb2dyZXNzIDwgMzApIHsKICAgICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+ato+WcqOS4iuS8oOaWh+S7ti4uLicKICAgICAgICB9IGVsc2UgaWYgKHRoaXMudXBsb2FkUHJvZ3Jlc3MgPCA2MCkgewogICAgICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAn5q2j5Zyo6Kej5p6Q5paH5Lu25YaF5a65Li4uJwogICAgICAgIH0gZWxzZSBpZiAodGhpcy51cGxvYWRQcm9ncmVzcyA8IDkwKSB7CiAgICAgICAgICB0aGlzLnByb2dyZXNzVGV4dCA9ICfmraPlnKjlr7zlhaXpopjnm67mlbDmja4uLi4nCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAxMDAKICAgICAgICAgIHRoaXMucHJvZ3Jlc3NUZXh0ID0gJ+WvvOWFpeWujOaIkO+8gScKICAgICAgICAgIHRoaXMudXBsb2FkU3RhdHVzID0gJ3N1Y2Nlc3MnCiAgICAgICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlCgogICAgICAgICAgLy8g5qih5ouf5a+85YWl57uT5p6cCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSB7CiAgICAgICAgICAgICAgc3VjY2Vzc19jb3VudDogMjQ1LAogICAgICAgICAgICAgIGVycm9yX2NvdW50OiA1LAogICAgICAgICAgICAgIHNraXBfY291bnQ6IDEyLAogICAgICAgICAgICAgIHRvdGFsX2NvdW50OiAyNjIsCiAgICAgICAgICAgICAgZXJyb3JzOiBbCiAgICAgICAgICAgICAgICB7IHJvdzogMTUsIGZpZWxkOiAn6aKY55uu5YaF5a65JywgbWVzc2FnZTogJ+mimOebruWGheWuueS4jeiDveS4uuepuicgfSwKICAgICAgICAgICAgICAgIHsgcm93OiAyMywgZmllbGQ6ICfmraPnoa7nrZTmoYgnLCBtZXNzYWdlOiAn5q2j56Gu562U5qGI5qC85byP6ZSZ6K+vJyB9LAogICAgICAgICAgICAgICAgeyByb3c6IDQ1LCBmaWVsZDogJ+WIhuexu0lEJywgbWVzc2FnZTogJ+WIhuexu0lE5LiN5a2Y5ZyoJyB9LAogICAgICAgICAgICAgICAgeyByb3c6IDY3LCBmaWVsZDogJ+mimOebruexu+WeiycsIG1lc3NhZ2U6ICfkuI3mlK/mjIHnmoTpopjnm67nsbvlnosnIH0sCiAgICAgICAgICAgICAgICB7IHJvdzogODksIGZpZWxkOiAn6YCJ6aG5QScsIG1lc3NhZ2U6ICfljZXpgInpopjoh7PlsJHpnIDopoEy5Liq6YCJ6aG5JyB9CiAgICAgICAgICAgICAgXQogICAgICAgICAgICB9CiAgICAgICAgICB9LCA1MDApCgogICAgICAgICAgY2xlYXJJbnRlcnZhbCh0aW1lcikKICAgICAgICB9CiAgICAgIH0sIDIwMCkKICAgIH0sCgogICAgaGFuZGxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofku7bkuIrkvKDmiJDlip8nKQogICAgfSwKCiAgICBoYW5kbGVFcnJvcihlcnIsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pScpCiAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy5zaG93UHJvZ3Jlc3MgPSBmYWxzZQogICAgfSwKCiAgICBoYW5kbGVQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgLy8g5a6e6ZmF6aG555uu5Lit5Y+v5Lul5Zyo6L+Z6YeM5aSE55CG55yf5a6e55qE5LiK5Lyg6L+b5bqmCiAgICB9LAoKICAgIGNsZWFyRmlsZXMoKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKQogICAgICB0aGlzLmZpbGVMaXN0ID0gW10KICAgICAgdGhpcy5zaG93UHJvZ3Jlc3MgPSBmYWxzZQogICAgICB0aGlzLmltcG9ydFJlc3VsdCA9IG51bGwKICAgIH0sCgogICAgcmVzZXRJbXBvcnQoKSB7CiAgICAgIHRoaXMuY2xlYXJGaWxlcygpCiAgICAgIHRoaXMudXBsb2FkUHJvZ3Jlc3MgPSAwCiAgICAgIHRoaXMudXBsb2FkU3RhdHVzID0gJycKICAgICAgdGhpcy5wcm9ncmVzc1RleHQgPSAnJwogICAgfSwKCiAgICBnb1RvUXVlc3Rpb25MaXN0KCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3F1ZXN0aW9ucy9saXN0JykKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["upload.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "upload.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 导入说明 -->\n    <el-card class=\"instruction-card\">\n      <div slot=\"header\">\n        <span>批量导入题目</span>\n      </div>\n      <div class=\"instruction-content\">\n        <el-alert\n          title=\"导入说明\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        >\n          <div slot=\"default\">\n            <p>1. 请下载模板文件，按照模板格式填写题目数据</p>\n            <p>2. 支持的文件格式：Excel (.xlsx, .xls)</p>\n            <p>3. 单次最多导入1000道题目</p>\n            <p>4. 题目类型：single_choice(单选), multiple_choice(多选), true_false(判断), fill_blank(填空), short_answer(简答), essay(论述)</p>\n            <p>5. 难度等级：easy(简单), medium(中等), hard(困难)</p>\n          </div>\n        </el-alert>\n\n        <div class=\"template-download\">\n          <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\n            下载导入模板\n          </el-button>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 文件上传 -->\n    <el-card class=\"upload-card\">\n      <div slot=\"header\">\n        <span>上传文件</span>\n      </div>\n\n      <el-upload\n        ref=\"upload\"\n        class=\"upload-demo\"\n        drag\n        :action=\"uploadUrl\"\n        :before-upload=\"beforeUpload\"\n        :on-success=\"handleSuccess\"\n        :on-error=\"handleError\"\n        :on-progress=\"handleProgress\"\n        :file-list=\"fileList\"\n        :auto-upload=\"false\"\n        accept=\".xlsx,.xls\"\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件，且不超过10MB</div>\n      </el-upload>\n\n      <div class=\"upload-actions\">\n        <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploading\">\n          开始导入\n        </el-button>\n        <el-button @click=\"clearFiles\">清空文件</el-button>\n      </div>\n    </el-card>\n\n    <!-- 导入进度 -->\n    <el-card v-if=\"showProgress\" class=\"progress-card\">\n      <div slot=\"header\">\n        <span>导入进度</span>\n      </div>\n\n      <el-progress\n        :percentage=\"uploadProgress\"\n        :status=\"uploadStatus\"\n        :stroke-width=\"20\"\n      />\n\n      <div class=\"progress-info\">\n        <p>{{ progressText }}</p>\n      </div>\n    </el-card>\n\n    <!-- 导入结果 -->\n    <el-card v-if=\"importResult\" class=\"result-card\">\n      <div slot=\"header\">\n        <span>导入结果</span>\n      </div>\n\n      <div class=\"result-summary\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <div class=\"stat-item success\">\n              <div class=\"stat-number\">{{ importResult.success_count }}</div>\n              <div class=\"stat-label\">成功导入</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item error\">\n              <div class=\"stat-number\">{{ importResult.error_count }}</div>\n              <div class=\"stat-label\">导入失败</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item warning\">\n              <div class=\"stat-number\">{{ importResult.skip_count }}</div>\n              <div class=\"stat-label\">跳过重复</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item info\">\n              <div class=\"stat-number\">{{ importResult.total_count }}</div>\n              <div class=\"stat-label\">总计处理</div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 错误详情 -->\n      <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"error-details\">\n        <h4>错误详情：</h4>\n        <el-table\n          :data=\"importResult.errors\"\n          border\n          size=\"small\"\n          max-height=\"300\"\n        >\n          <el-table-column prop=\"row\" label=\"行号\" width=\"80\" />\n          <el-table-column prop=\"field\" label=\"字段\" width=\"120\" />\n          <el-table-column prop=\"message\" label=\"错误信息\" />\n        </el-table>\n      </div>\n\n      <div class=\"result-actions\">\n        <el-button type=\"primary\" @click=\"goToQuestionList\">查看题目列表</el-button>\n        <el-button @click=\"resetImport\">重新导入</el-button>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'QuestionUpload',\n  data() {\n    return {\n      uploadUrl: '/api/questions/import', // 实际项目中的上传接口\n      fileList: [],\n      uploading: false,\n      showProgress: false,\n      uploadProgress: 0,\n      uploadStatus: '',\n      progressText: '',\n      importResult: null\n    }\n  },\n  methods: {\n    downloadTemplate() {\n      // 创建模板数据\n      const templateData = [\n        {\n          '题目类型': 'single_choice',\n          '难度等级': 'medium',\n          '分类ID': '1111',\n          '标签ID': '1,9,7',\n          '题目内容': '马克思主义哲学的基本问题是（）',\n          '选项A': '物质和意识的关系问题',\n          '选项B': '理论和实践的关系问题',\n          '选项C': '个人和社会的关系问题',\n          '选项D': '自由和必然的关系问题',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'A',\n          '题目解析': '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。它包括两个方面：第一，物质和意识何者为第一性；第二，物质和意识是否具有同一性。',\n          '分值': '2',\n          '排序': '1',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'single_choice',\n          '难度等级': 'easy',\n          '分类ID': '1111',\n          '标签ID': '1,9,16',\n          '题目内容': '物质的唯一特性是（）',\n          '选项A': '运动性',\n          '选项B': '客观实在性',\n          '选项C': '可知性',\n          '选项D': '绝对性',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'B',\n          '题目解析': '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在，并能为人的意识所反映。',\n          '分值': '2',\n          '排序': '2',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'multiple_choice',\n          '难度等级': 'medium',\n          '分类ID': '1112',\n          '标签ID': '1,9,7',\n          '题目内容': '意识的本质是（）',\n          '选项A': '意识是人脑的机能',\n          '选项B': '意识是客观存在的反映',\n          '选项C': '意识是社会的产物',\n          '选项D': '意识具有主观能动性',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'A,B,C,D',\n          '题目解析': '意识的本质包括：（1）意识是人脑的机能；（2）意识是客观存在的反映；（3）意识是社会的产物；（4）意识具有主观能动性。这四个方面构成了意识本质的完整内容。',\n          '分值': '2',\n          '排序': '3',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'true_false',\n          '难度等级': 'easy',\n          '分类ID': '1111',\n          '标签ID': '1,9',\n          '题目内容': '马克思主义哲学认为，世界的真正统一性在于它的物质性。',\n          '选项A': '',\n          '选项B': '',\n          '选项C': '',\n          '选项D': '',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'true',\n          '题目解析': '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。这是马克思主义一元论的基本观点，强调世界万物都是物质的不同表现形式。',\n          '分值': '2',\n          '排序': '4',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'fill_blank',\n          '难度等级': 'easy',\n          '分类ID': '1123',\n          '标签ID': '1,9,7',\n          '题目内容': '矛盾的基本属性是_____和_____。',\n          '选项A': '',\n          '选项B': '',\n          '选项C': '',\n          '选项D': '',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': '同一性；斗争性',\n          '题目解析': '矛盾的基本属性是同一性和斗争性。同一性是指矛盾双方相互依存、相互贯通的性质和趋势；斗争性是指矛盾双方相互排斥、相互对立的性质和趋势。',\n          '分值': '2',\n          '排序': '5',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'short_answer',\n          '难度等级': 'hard',\n          '分类ID': '113',\n          '标签ID': '1,9,6',\n          '题目内容': '简述实践是认识基础的主要表现。',\n          '选项A': '',\n          '选项B': '',\n          '选项C': '',\n          '选项D': '',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': '实践是认识的基础，主要表现在四个方面：（1）实践是认识的来源；（2）实践是认识发展的动力；（3）实践是检验认识真理性的唯一标准；（4）实践是认识的目的。',\n          '题目解析': '这道题考查实践与认识的关系。要从四个方面来回答：来源、动力、标准、目的。每个方面都要简要说明其含义。',\n          '分值': '6',\n          '排序': '6',\n          '状态': 'active'\n        }\n      ]\n\n      // 转换为CSV格式并下载\n      this.downloadCSV(templateData, '题目导入模板.csv')\n    },\n\n    downloadCSV(data, filename) {\n      const csvContent = this.convertToCSV(data)\n      const blob = new Blob(['\\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })\n      const link = document.createElement('a')\n\n      if (link.download !== undefined) {\n        const url = URL.createObjectURL(blob)\n        link.setAttribute('href', url)\n        link.setAttribute('download', filename)\n        link.style.visibility = 'hidden'\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n      }\n    },\n\n    convertToCSV(data) {\n      if (!data || data.length === 0) return ''\n\n      const headers = Object.keys(data[0])\n      const csvHeaders = headers.join(',')\n\n      const csvRows = data.map(row => {\n        return headers.map(header => {\n          const value = row[header] || ''\n          return `\"${value.toString().replace(/\"/g, '\"\"')}\"`\n        }).join(',')\n      })\n\n      return [csvHeaders, ...csvRows].join('\\n')\n    },\n\n    beforeUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                     file.type === 'application/vnd.ms-excel'\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isExcel) {\n        this.$message.error('只能上传Excel文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n      return true\n    },\n\n    submitUpload() {\n      if (this.fileList.length === 0) {\n        this.$message.warning('请先选择要上传的文件')\n        return\n      }\n\n      this.uploading = true\n      this.showProgress = true\n      this.uploadProgress = 0\n      this.uploadStatus = ''\n      this.progressText = '开始上传文件...'\n\n      // 模拟上传过程\n      this.simulateUpload()\n    },\n\n    simulateUpload() {\n      const timer = setInterval(() => {\n        this.uploadProgress += Math.random() * 15\n\n        if (this.uploadProgress < 30) {\n          this.progressText = '正在上传文件...'\n        } else if (this.uploadProgress < 60) {\n          this.progressText = '正在解析文件内容...'\n        } else if (this.uploadProgress < 90) {\n          this.progressText = '正在导入题目数据...'\n        } else {\n          this.uploadProgress = 100\n          this.progressText = '导入完成！'\n          this.uploadStatus = 'success'\n          this.uploading = false\n\n          // 模拟导入结果\n          setTimeout(() => {\n            this.importResult = {\n              success_count: 245,\n              error_count: 5,\n              skip_count: 12,\n              total_count: 262,\n              errors: [\n                { row: 15, field: '题目内容', message: '题目内容不能为空' },\n                { row: 23, field: '正确答案', message: '正确答案格式错误' },\n                { row: 45, field: '分类ID', message: '分类ID不存在' },\n                { row: 67, field: '题目类型', message: '不支持的题目类型' },\n                { row: 89, field: '选项A', message: '单选题至少需要2个选项' }\n              ]\n            }\n          }, 500)\n\n          clearInterval(timer)\n        }\n      }, 200)\n    },\n\n    handleSuccess(response, file, fileList) {\n      this.$message.success('文件上传成功')\n    },\n\n    handleError(err, file, fileList) {\n      this.$message.error('文件上传失败')\n      this.uploading = false\n      this.showProgress = false\n    },\n\n    handleProgress(event, file, fileList) {\n      // 实际项目中可以在这里处理真实的上传进度\n    },\n\n    clearFiles() {\n      this.$refs.upload.clearFiles()\n      this.fileList = []\n      this.showProgress = false\n      this.importResult = null\n    },\n\n    resetImport() {\n      this.clearFiles()\n      this.uploadProgress = 0\n      this.uploadStatus = ''\n      this.progressText = ''\n    },\n\n    goToQuestionList() {\n      this.$router.push('/questions/list')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .instruction-card,\n  .upload-card,\n  .progress-card,\n  .result-card {\n    margin-bottom: 20px;\n  }\n\n  .instruction-content {\n    .template-download {\n      margin-top: 20px;\n      text-align: center;\n    }\n  }\n\n  .upload-demo {\n    margin-bottom: 20px;\n  }\n\n  .upload-actions {\n    text-align: center;\n    margin-top: 20px;\n  }\n\n  .progress-info {\n    margin-top: 20px;\n    text-align: center;\n\n    p {\n      margin: 0;\n      color: #606266;\n    }\n  }\n\n  .result-summary {\n    margin-bottom: 30px;\n\n    .stat-item {\n      text-align: center;\n      padding: 20px;\n      border-radius: 8px;\n\n      .stat-number {\n        font-size: 32px;\n        font-weight: bold;\n        margin-bottom: 8px;\n      }\n\n      .stat-label {\n        font-size: 14px;\n        color: #666;\n      }\n\n      &.success {\n        background: #f0f9ff;\n        .stat-number { color: #67c23a; }\n      }\n\n      &.error {\n        background: #fef0f0;\n        .stat-number { color: #f56c6c; }\n      }\n\n      &.warning {\n        background: #fdf6ec;\n        .stat-number { color: #e6a23c; }\n      }\n\n      &.info {\n        background: #f4f4f5;\n        .stat-number { color: #909399; }\n      }\n    }\n  }\n\n  .error-details {\n    margin-bottom: 30px;\n\n    h4 {\n      margin-bottom: 15px;\n      color: #f56c6c;\n    }\n  }\n\n  .result-actions {\n    text-align: center;\n  }\n}\n</style>\n"]}]}