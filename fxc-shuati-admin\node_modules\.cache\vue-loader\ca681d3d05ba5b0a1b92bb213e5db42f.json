{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue?vue&type=style&index=0&id=414b322e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue", "mtime": 1752627989997}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFwcC1jb250YWluZXIgewogIC5maWx0ZXItY2FyZCB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgLmZpbHRlci1jb250YWluZXIgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LXdyYXA6IHdyYXA7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIC5maWx0ZXItaXRlbSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICAgIH0KICAgIH0KICB9CiAgLmVsLXRhYmxlIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50OwoKICAgIC5lbC10YWJsZV9fYm9keS13cmFwcGVyIHsKICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsKICAgIH0KICB9CiAgLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICAgIHBhZGRpbmc6IDE1cHggMDsKICB9Cn0K"}, {"version": 3, "sources": ["menus.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "menus.vue", "sourceRoot": "src/views/system", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索菜单名称、路径\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加菜单\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"menu_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"menu_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"菜单名称\" prop=\"menu_name\" min-width=\"150\" />\n      <el-table-column label=\"菜单路径\" prop=\"menu_path\" min-width=\"180\" />\n      <el-table-column label=\"菜单图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.menu_icon\" :class=\"row.menu_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"组件路径\" prop=\"component\" min-width=\"180\" />\n      <el-table-column label=\"父菜单\" prop=\"parent_name\" min-width=\"120\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父菜单\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父菜单\" style=\"width: 100%\">\n            <el-option label=\"顶级菜单\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.menu_id\"\n              :label=\"item.menu_name\"\n              :value=\"item.menu_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"菜单名称\" prop=\"menu_name\">\n          <el-input v-model=\"temp.menu_name\" />\n        </el-form-item>\n        <el-form-item label=\"菜单路径\" prop=\"menu_path\">\n          <el-input v-model=\"temp.menu_path\" />\n        </el-form-item>\n        <el-form-item label=\"组件路径\" prop=\"component\">\n          <el-input v-model=\"temp.component\" />\n        </el-form-item>\n        <el-form-item label=\"菜单图标\" prop=\"menu_icon\">\n          <el-input v-model=\"temp.menu_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'MenuManagement',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑菜单',\n        create: '添加菜单'\n      },\n      rules: {\n        menu_name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],\n        menu_path: [{ required: true, message: '菜单路径不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  computed: {\n    treeData() {\n      // 将平铺数据转换为树形结构\n      const tree = []\n      const map = {}\n\n      // 先创建所有节点的映射\n      this.list.forEach(item => {\n        map[item.menu_id] = { ...item, children: [] }\n      })\n\n      // 构建树形结构\n      this.list.forEach(item => {\n        if (item.parent_id === 0) {\n          // 顶级菜单\n          tree.push(map[item.menu_id])\n        } else {\n          // 子菜单\n          if (map[item.parent_id]) {\n            map[item.parent_id].children.push(map[item.menu_id])\n          }\n        }\n      })\n\n      return tree\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟数据，实际项目中应该调用API\n      setTimeout(() => {\n        this.list = [\n          // 首页\n          {\n            menu_id: 1,\n            menu_name: '首页',\n            menu_path: '/dashboard',\n            component: 'Layout',\n            menu_icon: 'dashboard',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 2,\n            menu_name: '首页',\n            menu_path: '/dashboard/index',\n            component: 'dashboard/index',\n            menu_icon: 'dashboard',\n            parent_id: 1,\n            parent_name: '首页',\n            status: 'active',\n            sort_order: 1\n          },\n          // 系统管理\n          {\n            menu_id: 10,\n            menu_name: '系统管理',\n            menu_path: '/system',\n            component: 'Layout',\n            menu_icon: 'el-icon-setting',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 11,\n            menu_name: '用户管理',\n            menu_path: '/system/users',\n            component: 'users/list',\n            menu_icon: 'el-icon-user-solid',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 12,\n            menu_name: '角色管理',\n            menu_path: '/system/roles',\n            component: 'users/roles',\n            menu_icon: 'el-icon-s-custom',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 13,\n            menu_name: '权限管理',\n            menu_path: '/system/permissions',\n            component: 'users/permissions',\n            menu_icon: 'el-icon-key',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 14,\n            menu_name: '菜单管理',\n            menu_path: '/system/menus',\n            component: 'system/menus',\n            menu_icon: 'el-icon-menu',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 题库管理\n          {\n            menu_id: 20,\n            menu_name: '题库管理',\n            menu_path: '/questions',\n            component: 'Layout',\n            menu_icon: 'el-icon-document',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 21,\n            menu_name: '题目分类',\n            menu_path: '/questions/categories',\n            component: 'questions/categories',\n            menu_icon: 'el-icon-folder',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 22,\n            menu_name: '题目列表',\n            menu_path: '/questions/list',\n            component: 'questions/list',\n            menu_icon: 'el-icon-document-copy',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 23,\n            menu_name: '添加题目',\n            menu_path: '/questions/add',\n            component: 'questions/add',\n            menu_icon: 'el-icon-plus',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 24,\n            menu_name: '批量导入',\n            menu_path: '/questions/upload',\n            component: 'questions/upload',\n            menu_icon: 'el-icon-upload',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 学习数据\n          {\n            menu_id: 30,\n            menu_name: '学习数据',\n            menu_path: '/learning',\n            component: 'Layout',\n            menu_icon: 'el-icon-data-analysis',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 4\n          },\n          {\n            menu_id: 31,\n            menu_name: '收藏管理',\n            menu_path: '/learning/favorites',\n            component: 'learning/favorites',\n            menu_icon: 'el-icon-star-on',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 32,\n            menu_name: '错题管理',\n            menu_path: '/learning/errors',\n            component: 'learning/errors',\n            menu_icon: 'el-icon-warning',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 33,\n            menu_name: '练习记录',\n            menu_path: '/learning/records',\n            component: 'learning/records',\n            menu_icon: 'el-icon-tickets',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 3\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n        \n        // 更新父菜单选项\n        this.parentOptions = this.list.filter(item => item.parent_id === 0)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该菜单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}