const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, authenticateAdmin } = require('./auth');

const router = express.Router();

// Mock 数据 - 基于题型.md的数据结构（暂时使用，后续连接数据库时替换）
const questions = [
  {
    question_id: 1,
    question_type: 'single', // 单选题
    question_content: "What is the capital of France?",
    question_difficulty: 1,
    question_subject: "考研英语一",
    question_chapter: "阅读理解",
    question_status: "active",
    created_at: "2023-01-01",
    updated_at: "2023-01-01",
    options: [
      { option_id: 1, option_content: "Paris", option_order: 1, is_correct: 1 },
      { option_id: 2, option_content: "London", option_order: 2, is_correct: 0 },
      { option_id: 3, option_content: "Berlin", option_order: 3, is_correct: 0 },
      { option_id: 4, option_content: "Madrid", option_order: 4, is_correct: 0 }
    ],
    explanation: {
      explanation_id: 1,
      explanation_content: "Paris is the capital and most populous city of France."
    }
  },
  {
    question_id: 2,
    question_type: 'fill', // 填空题
    question_content: "The capital of France is __________.",
    question_difficulty: 2,
    question_subject: "考研英语二",
    question_chapter: "完型填空",
    question_status: "active",
    created_at: "2023-01-02",
    updated_at: "2023-01-02",
    answer: {
      answer_id: 1,
      answer_content: "Paris"
    },
    explanation: {
      explanation_id: 2,
      explanation_content: "Paris is the capital and most populous city of France."
    }
  },
  {
    question_id: 3,
    question_type: 'multiple', // 多选题
    question_content: "Which of the following cities are in France?",
    question_difficulty: 3,
    question_subject: "考研英语一",
    question_chapter: "阅读理解",
    question_status: "active",
    created_at: "2023-01-03",
    updated_at: "2023-01-03",
    options: [
      { option_id: 5, option_content: "Paris", option_order: 1, is_correct: 1 },
      { option_id: 6, option_content: "Lyon", option_order: 2, is_correct: 1 },
      { option_id: 7, option_content: "Berlin", option_order: 3, is_correct: 0 },
      { option_id: 8, option_content: "Marseille", option_order: 4, is_correct: 1 }
    ],
    explanation: {
      explanation_id: 3,
      explanation_content: "Paris, Lyon, and Marseille are all cities in France. Berlin is in Germany."
    }
  },
  {
    question_id: 4,
    question_type: 'judge', // 判断题
    question_content: "Climate change is primarily caused by human activities.",
    question_difficulty: 2,
    question_subject: "考研英语一",
    question_chapter: "阅读理解",
    question_status: "active",
    created_at: "2023-01-04",
    updated_at: "2023-01-04",
    options: [
      { option_id: 9, option_content: "True", option_order: 1, is_correct: 1 },
      { option_id: 10, option_content: "False", option_order: 2, is_correct: 0 }
    ],
    explanation: {
      explanation_id: 4,
      explanation_content: "Scientific consensus confirms that human activities are the dominant cause of climate change since the mid-20th century."
    }
  },
  {
    question_id: 5,
    question_type: 'single',
    question_content: "What is the main cause of rising sea levels?",
    question_difficulty: 3,
    question_subject: "考研英语一",
    question_chapter: "阅读理解",
    question_status: "active",
    created_at: "2023-01-04",
    updated_at: "2023-01-04",
    options: [
      { option_id: 11, option_content: "Thermal expansion of seawater", option_order: 1, is_correct: 1 },
      { option_id: 12, option_content: "Underwater earthquakes", option_order: 2, is_correct: 0 },
      { option_id: 13, option_content: "Ocean currents", option_order: 3, is_correct: 0 },
      { option_id: 14, option_content: "Tidal forces", option_order: 4, is_correct: 0 }
    ],
    explanation: {
      explanation_id: 5,
      explanation_content: "The primary cause of rising sea levels is thermal expansion of seawater due to global warming, along with melting ice sheets and glaciers."
    }
  }
];

// 组合题数据
const compositeQuestions = [
  {
    composite_id: 1,
    composite_title: "Climate Change Reading Comprehension",
    composite_content: `<p>Climate change is one of the most pressing issues facing our planet today. The Earth's average temperature has increased by about 1°C since pre-industrial times, primarily due to human activities such as burning fossil fuels, deforestation, and industrial processes that release greenhouse gases into the atmosphere.</p>
    <p>Scientists have observed various effects of climate change, including rising sea levels, more frequent and intense heat waves, changes in precipitation patterns, and more severe storms. These changes have significant impacts on ecosystems, agriculture, human health, and infrastructure.</p>
    <p>While there are natural factors that can influence climate, such as volcanic eruptions and changes in solar radiation, the scientific consensus is that human activities are the dominant cause of the observed warming since the mid-20th century.</p>`,
    composite_difficulty: 3,
    status: "active",
    created_at: "2023-01-04",
    updated_at: "2023-01-04"
  }
];

// 组合题关联表
const compositeQuestionLinks = [
  {
    link_id: 1,
    composite_id: 1,
    question_id: 4,
    question_order: 1,
    created_at: "2023-01-04",
    updated_at: "2023-01-04"
  },
  {
    link_id: 2,
    composite_id: 1,
    question_id: 5,
    question_order: 2,
    created_at: "2023-01-04",
    updated_at: "2023-01-04"
  }
];

// 获取题目列表（支持分页和筛选）
router.get('/', async (req, res) => {
  try {
    const {
      question_type,
      question_subject,
      question_chapter,
      question_difficulty,
      category_id,
      page = 1,
      limit = 20,
      random = false
    } = req.query;

    let filteredQuestions = [...questions];

    // 根据查询参数过滤题目
    if (question_type) {
      filteredQuestions = filteredQuestions.filter(q => q.question_type === question_type);
    }

    if (question_subject) {
      filteredQuestions = filteredQuestions.filter(q => q.question_subject === question_subject);
    }

    if (question_chapter) {
      filteredQuestions = filteredQuestions.filter(q => q.question_chapter === question_chapter);
    }

    if (question_difficulty) {
      filteredQuestions = filteredQuestions.filter(q => q.question_difficulty == question_difficulty);
    }

    // 随机排序
    if (random === 'true') {
      filteredQuestions = filteredQuestions.sort(() => Math.random() - 0.5);
    }

    // 分页处理
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedQuestions = filteredQuestions.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        questions: paginatedQuestions,
        pagination: {
          current_page: pageNum,
          per_page: pageSize,
          total: filteredQuestions.length,
          total_pages: Math.ceil(filteredQuestions.length / pageSize)
        }
      },
      message: '获取成功'
    });

  } catch (error) {
    console.error('获取题目列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取题目列表失败'
    });
  }
});

// 获取单个题目详情
router.get('/:id', async (req, res) => {
  try {
    const questionId = parseInt(req.params.id);
    const question = questions.find(q => q.question_id === questionId);

    if (!question) {
      return res.status(404).json({
        success: false,
        message: '题目不存在'
      });
    }

    // 增加查看次数（实际项目中应该更新数据库）
    // await query('UPDATE questions SET view_count = view_count + 1 WHERE question_id = ?', [questionId]);

    res.json({
      success: true,
      data: question,
      message: '获取成功'
    });

  } catch (error) {
    console.error('获取题目详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取题目详情失败'
    });
  }
});

// 提交答案
router.post('/:id/answer', authenticateToken, async (req, res) => {
  try {
    const questionId = parseInt(req.params.id);
    const { user_answer, answer_time = 0 } = req.body;
    const userId = req.user.user_id;

    const question = questions.find(q => q.question_id === questionId);
    if (!question) {
      return res.status(404).json({
        success: false,
        message: '题目不存在'
      });
    }

    // 判断答案是否正确
    let isCorrect = false;
    let correctAnswer = '';

    if (question.question_type === 'single' || question.question_type === 'judge') {
      const correctOption = question.options.find(opt => opt.is_correct === 1);
      correctAnswer = correctOption ? correctOption.option_content : '';
      isCorrect = user_answer === correctAnswer;
    } else if (question.question_type === 'multiple') {
      const correctOptions = question.options.filter(opt => opt.is_correct === 1);
      correctAnswer = correctOptions.map(opt => opt.option_content).sort().join(',');
      const userAnswerArray = Array.isArray(user_answer) ? user_answer.sort().join(',') : user_answer;
      isCorrect = userAnswerArray === correctAnswer;
    } else if (question.question_type === 'fill' || question.question_type === 'essay') {
      correctAnswer = question.answer ? question.answer.answer_content : '';
      isCorrect = user_answer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();
    }

    // 在实际项目中，这里应该保存到数据库
    // await query('INSERT INTO user_answer_records (user_id, question_id, user_answer, is_correct, answer_time) VALUES (?, ?, ?, ?, ?)',
    //   [userId, questionId, JSON.stringify(user_answer), isCorrect, answer_time]);

    // 如果答错了，添加到错题本
    if (!isCorrect) {
      // await query('INSERT INTO user_errors (user_id, question_id, user_answer) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE error_count = error_count + 1, user_answer = VALUES(user_answer), last_error_time = NOW()',
      //   [userId, questionId, JSON.stringify(user_answer)]);
    }

    res.json({
      success: true,
      data: {
        is_correct: isCorrect,
        correct_answer: correctAnswer,
        explanation: question.explanation,
        user_answer: user_answer
      },
      message: isCorrect ? '回答正确' : '回答错误'
    });

  } catch (error) {
    console.error('提交答案错误:', error);
    res.status(500).json({
      success: false,
      message: '提交答案失败'
    });
  }
});

// 创建题目（管理员）
router.post('/', authenticateAdmin, async (req, res) => {
  try {
    const {
      category_id,
      question_type,
      question_content,
      question_difficulty = 1,
      question_subject = '',
      question_chapter = '',
      options = [],
      answer_content = '',
      explanation_content = ''
    } = req.body;

    // 验证必填字段
    if (!question_content || !question_type) {
      return res.status(400).json({
        success: false,
        message: '题目内容和题目类型不能为空'
      });
    }

    // 在实际项目中，这里应该保存到数据库
    const newQuestionId = questions.length + 1;
    const newQuestion = {
      question_id: newQuestionId,
      category_id: category_id || 1,
      question_type,
      question_content,
      question_difficulty,
      question_subject,
      question_chapter,
      question_status: 'active',
      view_count: 0,
      correct_count: 0,
      wrong_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // 添加选项
    if (options.length > 0) {
      newQuestion.options = options.map((opt, index) => ({
        option_id: questions.length * 10 + index + 1,
        option_content: opt.option_content,
        option_order: opt.option_order || index + 1,
        is_correct: opt.is_correct || 0
      }));
    }

    // 添加答案（填空题、简答题）
    if (answer_content) {
      newQuestion.answer = {
        answer_id: newQuestionId,
        answer_content
      };
    }

    // 添加解析
    if (explanation_content) {
      newQuestion.explanation = {
        explanation_id: newQuestionId,
        explanation_content
      };
    }

    questions.push(newQuestion);

    res.json({
      success: true,
      data: newQuestion,
      message: '创建题目成功'
    });

  } catch (error) {
    console.error('创建题目错误:', error);
    res.status(500).json({
      success: false,
      message: '创建题目失败'
    });
  }
});

module.exports = router;