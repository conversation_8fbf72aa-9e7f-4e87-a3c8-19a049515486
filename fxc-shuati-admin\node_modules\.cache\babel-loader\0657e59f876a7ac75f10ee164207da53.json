{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\add.vue", "mtime": 1752629908677}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "saving", "questionForm", "question_type", "difficulty", "category_id", "tags", "question_content", "options", "content", "correct_answer", "explanation", "score", "sort_order", "status", "categoryOptions", "tagOptions", "categoryProps", "value", "label", "children", "emitPath", "rules", "required", "message", "trigger", "created", "loadCategoryOptions", "loadTagOptions", "methods", "category_name", "tag_id", "tag_name", "addOption", "length", "push", "removeOption", "index", "splice", "handleSave", "_this", "$refs", "validate", "valid", "setTimeout", "$notify", "title", "type", "duration", "$router", "$message", "error", "handleCancel", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "then"], "sources": ["src/views/questions/add.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"form-card\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>添加题目</span>\n        <div>\n          <el-button @click=\"handleCancel\">取消</el-button>\n          <el-button type=\"primary\" @click=\"handleSave\" :loading=\"saving\">保存</el-button>\n        </div>\n      </div>\n\n      <el-form\n        ref=\"questionForm\"\n        :model=\"questionForm\"\n        :rules=\"rules\"\n        label-width=\"120px\"\n        class=\"question-form\"\n      >\n        <!-- 基本信息 -->\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"题目类型\" prop=\"question_type\">\n              <el-select v-model=\"questionForm.question_type\" placeholder=\"请选择题目类型\" style=\"width: 100%\">\n                <el-option label=\"单选题\" value=\"single_choice\" />\n                <el-option label=\"多选题\" value=\"multiple_choice\" />\n                <el-option label=\"判断题\" value=\"true_false\" />\n                <el-option label=\"填空题\" value=\"fill_blank\" />\n                <el-option label=\"简答题\" value=\"short_answer\" />\n                <el-option label=\"论述题\" value=\"essay\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"难度等级\" prop=\"difficulty\">\n              <el-select v-model=\"questionForm.difficulty\" placeholder=\"请选择难度等级\" style=\"width: 100%\">\n                <el-option label=\"简单\" value=\"easy\" />\n                <el-option label=\"中等\" value=\"medium\" />\n                <el-option label=\"困难\" value=\"hard\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"题目分类\" prop=\"category_id\">\n              <el-cascader\n                v-model=\"questionForm.category_id\"\n                :options=\"categoryOptions\"\n                :props=\"categoryProps\"\n                placeholder=\"请选择题目分类\"\n                style=\"width: 100%\"\n                clearable\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"题目标签\" prop=\"tags\">\n              <el-select\n                v-model=\"questionForm.tags\"\n                multiple\n                placeholder=\"请选择题目标签\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"tag in tagOptions\"\n                  :key=\"tag.tag_id\"\n                  :label=\"tag.tag_name\"\n                  :value=\"tag.tag_id\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <!-- 题目内容 -->\n        <el-form-item label=\"题目内容\" prop=\"question_content\">\n          <el-input\n            v-model=\"questionForm.question_content\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入题目内容\"\n          />\n        </el-form-item>\n\n        <!-- 选择题选项 -->\n        <div v-if=\"questionForm.question_type === 'single_choice' || questionForm.question_type === 'multiple_choice'\">\n          <el-form-item label=\"选项设置\">\n            <div class=\"options-container\">\n              <div\n                v-for=\"(option, index) in questionForm.options\"\n                :key=\"index\"\n                class=\"option-item\"\n              >\n                <el-input\n                  v-model=\"option.content\"\n                  :placeholder=\"`选项 ${String.fromCharCode(65 + index)}`\"\n                  class=\"option-input\"\n                />\n                <el-button\n                  v-if=\"questionForm.options.length > 2\"\n                  type=\"danger\"\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  @click=\"removeOption(index)\"\n                />\n              </div>\n              <el-button\n                v-if=\"questionForm.options.length < 6\"\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                size=\"mini\"\n                @click=\"addOption\"\n              >\n                添加选项\n              </el-button>\n            </div>\n          </el-form-item>\n\n          <el-form-item label=\"正确答案\" prop=\"correct_answer\">\n            <el-checkbox-group v-if=\"questionForm.question_type === 'multiple_choice'\" v-model=\"questionForm.correct_answer\">\n              <el-checkbox\n                v-for=\"(option, index) in questionForm.options\"\n                :key=\"index\"\n                :label=\"String.fromCharCode(65 + index)\"\n              >\n                {{ String.fromCharCode(65 + index) }}\n              </el-checkbox>\n            </el-checkbox-group>\n            <el-radio-group v-else v-model=\"questionForm.correct_answer\">\n              <el-radio\n                v-for=\"(option, index) in questionForm.options\"\n                :key=\"index\"\n                :label=\"String.fromCharCode(65 + index)\"\n              >\n                {{ String.fromCharCode(65 + index) }}\n              </el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </div>\n\n        <!-- 判断题答案 -->\n        <el-form-item v-if=\"questionForm.question_type === 'true_false'\" label=\"正确答案\" prop=\"correct_answer\">\n          <el-radio-group v-model=\"questionForm.correct_answer\">\n            <el-radio label=\"true\">正确</el-radio>\n            <el-radio label=\"false\">错误</el-radio>\n          </el-radio-group>\n        </el-form-item>\n\n        <!-- 填空题答案 -->\n        <el-form-item v-if=\"questionForm.question_type === 'fill_blank'\" label=\"参考答案\" prop=\"correct_answer\">\n          <el-input\n            v-model=\"questionForm.correct_answer\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入参考答案，多个答案用分号分隔\"\n          />\n        </el-form-item>\n\n        <!-- 简答题/论述题答案 -->\n        <el-form-item v-if=\"questionForm.question_type === 'short_answer' || questionForm.question_type === 'essay'\" label=\"参考答案\" prop=\"correct_answer\">\n          <el-input\n            v-model=\"questionForm.correct_answer\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入参考答案\"\n          />\n        </el-form-item>\n\n        <!-- 解析 -->\n        <el-form-item label=\"题目解析\">\n          <el-input\n            v-model=\"questionForm.explanation\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入题目解析（可选）\"\n          />\n        </el-form-item>\n\n        <!-- 其他设置 -->\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"分值\">\n              <el-input-number v-model=\"questionForm.score\" :min=\"1\" :max=\"100\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"排序\">\n              <el-input-number v-model=\"questionForm.sort_order\" :min=\"0\" :max=\"9999\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\">\n              <el-select v-model=\"questionForm.status\" style=\"width: 100%\">\n                <el-option label=\"启用\" value=\"active\" />\n                <el-option label=\"禁用\" value=\"disabled\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'QuestionAdd',\n  data() {\n    return {\n      saving: false,\n      questionForm: {\n        question_type: '',\n        difficulty: 'medium',\n        category_id: [],\n        tags: [],\n        question_content: '',\n        options: [\n          { content: '' },\n          { content: '' }\n        ],\n        correct_answer: '',\n        explanation: '',\n        score: 5,\n        sort_order: 0,\n        status: 'active'\n      },\n      categoryOptions: [],\n      tagOptions: [],\n      categoryProps: {\n        value: 'category_id',\n        label: 'category_name',\n        children: 'children',\n        emitPath: false\n      },\n      rules: {\n        question_type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],\n        difficulty: [{ required: true, message: '请选择难度等级', trigger: 'change' }],\n        category_id: [{ required: true, message: '请选择题目分类', trigger: 'change' }],\n        question_content: [{ required: true, message: '请输入题目内容', trigger: 'blur' }],\n        correct_answer: [{ required: true, message: '请设置正确答案', trigger: 'change' }]\n      }\n    }\n  },\n  created() {\n    this.loadCategoryOptions()\n    this.loadTagOptions()\n  },\n  methods: {\n    loadCategoryOptions() {\n      // 模拟分类数据\n      this.categoryOptions = [\n        {\n          category_id: 1,\n          category_name: '马克思主义基本原理',\n          children: [\n            {\n              category_id: 11,\n              category_name: '马克思主义哲学',\n              children: [\n                { category_id: 111, category_name: '唯物论' },\n                { category_id: 112, category_name: '辩证法' },\n                { category_id: 113, category_name: '认识论' }\n              ]\n            },\n            { category_id: 12, category_name: '马克思主义政治经济学' },\n            { category_id: 13, category_name: '科学社会主义' }\n          ]\n        },\n        {\n          category_id: 2,\n          category_name: '毛泽东思想和中国特色社会主义理论体系概论',\n          children: [\n            { category_id: 21, category_name: '毛泽东思想' },\n            { category_id: 22, category_name: '邓小平理论' },\n            { category_id: 23, category_name: '三个代表重要思想' }\n          ]\n        },\n        {\n          category_id: 3,\n          category_name: '中国近现代史纲要',\n          children: [\n            { category_id: 31, category_name: '旧民主主义革命时期' },\n            { category_id: 32, category_name: '新民主主义革命时期' },\n            { category_id: 33, category_name: '社会主义革命和建设时期' }\n          ]\n        },\n        {\n          category_id: 4,\n          category_name: '思想道德与法治'\n        },\n        {\n          category_id: 5,\n          category_name: '形势与政策'\n        }\n      ]\n    },\n    loadTagOptions() {\n      // 模拟标签数据\n      this.tagOptions = [\n        { tag_id: 1, tag_name: '马克思主义基本原理' },\n        { tag_id: 2, tag_name: '毛泽东思想' },\n        { tag_id: 3, tag_name: '中国近现代史纲要' },\n        { tag_id: 4, tag_name: '思想道德与法治' },\n        { tag_id: 5, tag_name: '形势与政策' },\n        { tag_id: 6, tag_name: '重点难点' },\n        { tag_id: 7, tag_name: '高频考点' },\n        { tag_id: 8, tag_name: '易错题' }\n      ]\n    },\n    addOption() {\n      if (this.questionForm.options.length < 6) {\n        this.questionForm.options.push({ content: '' })\n      }\n    },\n    removeOption(index) {\n      if (this.questionForm.options.length > 2) {\n        this.questionForm.options.splice(index, 1)\n        // 重置答案选择\n        this.questionForm.correct_answer = ''\n      }\n    },\n    handleSave() {\n      this.$refs.questionForm.validate((valid) => {\n        if (valid) {\n          this.saving = true\n\n          // 模拟保存\n          setTimeout(() => {\n            this.saving = false\n            this.$notify({\n              title: '成功',\n              message: '题目添加成功',\n              type: 'success',\n              duration: 2000\n            })\n\n            // 跳转到题目列表\n            this.$router.push('/questions/list')\n          }, 1000)\n        } else {\n          this.$message.error('请完善题目信息')\n        }\n      })\n    },\n    handleCancel() {\n      this.$confirm('确定要取消添加题目吗？未保存的数据将丢失。', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$router.push('/questions/list')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .form-card {\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .question-form {\n      padding: 20px 0;\n\n      .options-container {\n        .option-item {\n          display: flex;\n          align-items: center;\n          margin-bottom: 10px;\n\n          .option-input {\n            flex: 1;\n            margin-right: 10px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8MA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,YAAA;QACAC,aAAA;QACAC,UAAA;QACAC,WAAA;QACAC,IAAA;QACAC,gBAAA;QACAC,OAAA,GACA;UAAAC,OAAA;QAAA,GACA;UAAAA,OAAA;QAAA,EACA;QACAC,cAAA;QACAC,WAAA;QACAC,KAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACAC,eAAA;MACAC,UAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,KAAA;QACAnB,aAAA;UAAAoB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACArB,UAAA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACApB,WAAA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAlB,gBAAA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAf,cAAA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,mBAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAF,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAAZ,eAAA,IACA;QACAV,WAAA;QACAyB,aAAA;QACAV,QAAA,GACA;UACAf,WAAA;UACAyB,aAAA;UACAV,QAAA,GACA;YAAAf,WAAA;YAAAyB,aAAA;UAAA,GACA;YAAAzB,WAAA;YAAAyB,aAAA;UAAA,GACA;YAAAzB,WAAA;YAAAyB,aAAA;UAAA;QAEA,GACA;UAAAzB,WAAA;UAAAyB,aAAA;QAAA,GACA;UAAAzB,WAAA;UAAAyB,aAAA;QAAA;MAEA,GACA;QACAzB,WAAA;QACAyB,aAAA;QACAV,QAAA,GACA;UAAAf,WAAA;UAAAyB,aAAA;QAAA,GACA;UAAAzB,WAAA;UAAAyB,aAAA;QAAA,GACA;UAAAzB,WAAA;UAAAyB,aAAA;QAAA;MAEA,GACA;QACAzB,WAAA;QACAyB,aAAA;QACAV,QAAA,GACA;UAAAf,WAAA;UAAAyB,aAAA;QAAA,GACA;UAAAzB,WAAA;UAAAyB,aAAA;QAAA,GACA;UAAAzB,WAAA;UAAAyB,aAAA;QAAA;MAEA,GACA;QACAzB,WAAA;QACAyB,aAAA;MACA,GACA;QACAzB,WAAA;QACAyB,aAAA;MACA,EACA;IACA;IACAF,cAAA,WAAAA,eAAA;MACA;MACA,KAAAZ,UAAA,IACA;QAAAe,MAAA;QAAAC,QAAA;MAAA,GACA;QAAAD,MAAA;QAAAC,QAAA;MAAA,GACA;QAAAD,MAAA;QAAAC,QAAA;MAAA,GACA;QAAAD,MAAA;QAAAC,QAAA;MAAA,GACA;QAAAD,MAAA;QAAAC,QAAA;MAAA,GACA;QAAAD,MAAA;QAAAC,QAAA;MAAA,GACA;QAAAD,MAAA;QAAAC,QAAA;MAAA,GACA;QAAAD,MAAA;QAAAC,QAAA;MAAA,EACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,SAAA/B,YAAA,CAAAM,OAAA,CAAA0B,MAAA;QACA,KAAAhC,YAAA,CAAAM,OAAA,CAAA2B,IAAA;UAAA1B,OAAA;QAAA;MACA;IACA;IACA2B,YAAA,WAAAA,aAAAC,KAAA;MACA,SAAAnC,YAAA,CAAAM,OAAA,CAAA0B,MAAA;QACA,KAAAhC,YAAA,CAAAM,OAAA,CAAA8B,MAAA,CAAAD,KAAA;QACA;QACA,KAAAnC,YAAA,CAAAQ,cAAA;MACA;IACA;IACA6B,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAvC,YAAA,CAAAwC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,KAAA,CAAAvC,MAAA;;UAEA;UACA2C,UAAA;YACAJ,KAAA,CAAAvC,MAAA;YACAuC,KAAA,CAAAK,OAAA;cACAC,KAAA;cACAtB,OAAA;cACAuB,IAAA;cACAC,QAAA;YACA;;YAEA;YACAR,KAAA,CAAAS,OAAA,CAAAd,IAAA;UACA;QACA;UACAK,KAAA,CAAAU,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAT,IAAA;MACA,GAAAU,IAAA;QACAJ,MAAA,CAAAJ,OAAA,CAAAd,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}