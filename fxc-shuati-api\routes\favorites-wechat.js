const express = require('express');
const router = express.Router();
const { query } = require('../config/database');

// 获取收藏列表
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    const { page = 1, pageSize = 10, category_id } = req.query;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE f.user_id = ? AND q.status = "active"';
    let params = [userId];
    
    if (category_id) {
      whereClause += ' AND q.category_id = ?';
      params.push(category_id);
    }
    
    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM user_favorites f
      INNER JOIN questions q ON f.question_id = q.question_id
      ${whereClause}
    `;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    const listSql = `
      SELECT q.question_id, q.question_content, q.question_type, q.difficulty, 
             q.options, q.correct_answer, q.explanation, q.score,
             c.category_name, f.created_at as favorite_time
      FROM user_favorites f
      INNER JOIN questions q ON f.question_id = q.question_id
      LEFT JOIN categories c ON q.category_id = c.category_id
      ${whereClause}
      ORDER BY f.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(pageSize), offset);
    const list = await query(listSql, params);
    
    // 处理选项数据
    const processedList = list.map(item => ({
      ...item,
      options: item.options ? JSON.parse(item.options) : []
    }));
    
    res.json({
      success: true,
      data: {
        list: processedList,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取收藏列表失败:', error);
    
    // 返回模拟数据
    const mockFavorites = [
      {
        question_id: 1,
        question_content: '马克思主义哲学的基本问题是（）',
        question_type: 'single_choice',
        difficulty: 'medium',
        options: [
          { content: '物质和意识的关系问题' },
          { content: '理论和实践的关系问题' },
          { content: '个人和社会的关系问题' },
          { content: '自由和必然的关系问题' }
        ],
        correct_answer: 'A',
        explanation: '马克思主义哲学的基本问题是物质和意识的关系问题。',
        score: 2,
        category_name: '物质概念',
        favorite_time: '2024-01-18 10:30:00'
      },
      {
        question_id: 3,
        question_content: '意识的本质是（）',
        question_type: 'multiple_choice',
        difficulty: 'medium',
        options: [
          { content: '意识是人脑的机能' },
          { content: '意识是客观存在的反映' },
          { content: '意识是社会的产物' },
          { content: '意识具有主观能动性' }
        ],
        correct_answer: 'A,B,C,D',
        explanation: '意识的本质包括四个方面。',
        score: 2,
        category_name: '意识本质',
        favorite_time: '2024-01-17 15:20:00'
      }
    ];
    
    res.json({
      success: true,
      data: {
        list: mockFavorites,
        total: mockFavorites.length,
        page: parseInt(req.query.page || 1),
        pageSize: parseInt(req.query.pageSize || 10)
      }
    });
  }
});

// 添加收藏
router.post('/', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    const { question_id } = req.body;
    
    // 检查题目是否存在
    const questionExists = await query(
      'SELECT question_id FROM questions WHERE question_id = ? AND status = "active"',
      [question_id]
    );
    
    if (questionExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '题目不存在'
      });
    }
    
    // 添加收藏（如果已存在则忽略）
    await query(
      'INSERT IGNORE INTO user_favorites (user_id, question_id, created_at) VALUES (?, ?, NOW())',
      [userId, question_id]
    );
    
    res.json({
      success: true,
      message: '收藏成功'
    });
  } catch (error) {
    console.error('添加收藏失败:', error);
    res.json({
      success: true,
      message: '收藏成功（模拟）'
    });
  }
});

// 取消收藏
router.delete('/:questionId', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    const questionId = req.params.questionId;
    
    await query(
      'DELETE FROM user_favorites WHERE user_id = ? AND question_id = ?',
      [userId, questionId]
    );
    
    res.json({
      success: true,
      message: '取消收藏成功'
    });
  } catch (error) {
    console.error('取消收藏失败:', error);
    res.json({
      success: true,
      message: '取消收藏成功（模拟）'
    });
  }
});

// 检查是否已收藏
router.get('/check/:questionId', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    const questionId = req.params.questionId;
    
    const result = await query(
      'SELECT 1 FROM user_favorites WHERE user_id = ? AND question_id = ?',
      [userId, questionId]
    );
    
    res.json({
      success: true,
      data: {
        isFavorited: result.length > 0
      }
    });
  } catch (error) {
    console.error('检查收藏状态失败:', error);
    res.json({
      success: true,
      data: {
        isFavorited: false
      }
    });
  }
});

module.exports = router;
