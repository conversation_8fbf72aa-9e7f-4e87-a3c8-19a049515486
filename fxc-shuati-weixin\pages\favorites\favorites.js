// favorites.js
const { api } = require('../../utils/api.js')
const { formatTime, getQuestionTypeText, getDifficultyText, showError, showSuccess } = require('../../utils/util.js')

Page({
  data: {
    searchKeyword: '',
    selectedType: '',
    typeOptions: [
      { label: '全部', value: '' },
      { label: '单选题', value: 'single' },
      { label: '多选题', value: 'multiple' },
      { label: '判断题', value: 'judge' },
      { label: '填空题', value: 'fill' },
      { label: '简答题', value: 'essay' }
    ],
    favoriteList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    console.log('收藏页面加载')
    this.loadFavoriteList()
  },

  onShow() {
    console.log('收藏页面显示')
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 刷新数据
  refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      favoriteList: []
    })
    this.loadFavoriteList()
  },

  // 加载收藏列表
  loadFavoriteList() {
    if (this.data.loading) return

    this.setData({ loading: true })

    // 使用mock数据
    const mockData = [
      {
        favorite_id: 1,
        question_id: 1,
        created_at: '2023-12-01T00:00:00.000Z',
        question: {
          question_id: 1,
          question_type: 'single',
          question_content: 'What is the capital of France?',
          question_difficulty: 1,
          question_subject: '考研英语一',
          question_chapter: '阅读理解'
        }
      },
      {
        favorite_id: 2,
        question_id: 3,
        created_at: '2023-12-02T00:00:00.000Z',
        question: {
          question_id: 3,
          question_type: 'multiple',
          question_content: 'Which of the following cities are in France?',
          question_difficulty: 3,
          question_subject: '考研英语一',
          question_chapter: '阅读理解'
        }
      }
    ]

    // 处理数据
    const processedData = mockData.map(item => ({
      ...item,
      timeText: formatTime(item.created_at, 'MM-DD HH:mm'),
      question: {
        ...item.question,
        typeText: getQuestionTypeText(item.question.question_type),
        difficultyText: getDifficultyText(item.question.question_difficulty)
      }
    }))

    setTimeout(() => {
      const { page, favoriteList } = this.data
      const newList = page === 1 ? processedData : [...favoriteList, ...processedData]
      
      this.setData({
        favoriteList: newList,
        loading: false,
        hasMore: processedData.length === this.data.pageSize
      })

      if (page === 1) {
        wx.stopPullDownRefresh()
      }
    }, 500)

    // 实际API调用示例
    // const params = {
    //   page: this.data.page,
    //   pageSize: this.data.pageSize,
    //   keyword: this.data.searchKeyword,
    //   type: this.data.selectedType
    // }
    
    // api.getFavorites(params).then(res => {
    //   const processedData = res.data.map(item => ({
    //     ...item,
    //     timeText: formatTime(item.created_at, 'MM-DD HH:mm'),
    //     question: {
    //       ...item.question,
    //       typeText: getQuestionTypeText(item.question.question_type),
    //       difficultyText: getDifficultyText(item.question.question_difficulty)
    //     }
    //   }))
    
    //   const { page, favoriteList } = this.data
    //   const newList = page === 1 ? processedData : [...favoriteList, ...processedData]
    
    //   this.setData({
    //     favoriteList: newList,
    //     loading: false,
    //     hasMore: processedData.length === this.data.pageSize
    //   })
    
    //   if (page === 1) {
    //     wx.stopPullDownRefresh()
    //   }
    // }).catch(err => {
    //   console.error('获取收藏列表失败', err)
    //   this.setData({ loading: false })
    //   if (page === 1) {
    //     wx.stopPullDownRefresh()
    //   }
    // })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 执行搜索
  onSearch() {
    this.refreshData()
  },

  // 选择题目类型
  selectType(e) {
    const value = e.currentTarget.dataset.value
    this.setData({
      selectedType: value
    })
    this.refreshData()
  },

  // 跳转到答题页面
  goToQuestion(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages/question/question?questionId=${item.question_id}&isFavorite=true`
    })
  },

  // 取消收藏
  removeFavorite(e) {
    const item = e.currentTarget.dataset.item
    
    wx.showModal({
      title: '确认取消',
      content: '确定要取消收藏这道题目吗？',
      success: (res) => {
        if (res.confirm) {
          // 从列表中移除
          const favoriteList = this.data.favoriteList.filter(fav => fav.favorite_id !== item.favorite_id)
          this.setData({
            favoriteList
          })
          
          showSuccess('取消收藏成功')
          
          // 实际API调用
          // api.removeFavorite(item.question_id).then(() => {
          //   showSuccess('取消收藏成功')
          // }).catch(err => {
          //   console.error('取消收藏失败', err)
          //   // 恢复列表
          //   this.loadFavoriteList()
          // })
        }
      }
    })
  },

  // 清空所有收藏
  clearAllFavorites() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有收藏吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            favoriteList: []
          })
          showSuccess('清空成功')
        }
      }
    })
  },

  // 练习所有收藏
  practiceAllFavorites() {
    const { favoriteList } = this.data
    if (favoriteList.length === 0) {
      showError('暂无收藏题目')
      return
    }

    const questionIds = favoriteList.map(item => item.question_id).join(',')
    wx.navigateTo({
      url: `/pages/question/question?questionIds=${questionIds}&isFavoriteAll=true&title=收藏练习`
    })
  },

  // 跳转到练习页面
  goToPractice() {
    wx.switchTab({
      url: '/pages/practice/practice'
    })
  },

  // 加载更多
  loadMore() {
    this.setData({
      page: this.data.page + 1
    })
    this.loadFavoriteList()
  }
})
