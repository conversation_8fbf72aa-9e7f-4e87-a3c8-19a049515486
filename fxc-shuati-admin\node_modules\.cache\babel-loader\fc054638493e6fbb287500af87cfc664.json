{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue", "mtime": 1752629413914}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcyc7CmltcG9ydCB7IGZvcm1hdERhdGUgfSBmcm9tICdAL3V0aWxzJzsKaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24nOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1F1ZXN0aW9uVGFncycsCiAgY29tcG9uZW50czogewogICAgUGFnaW5hdGlvbjogUGFnaW5hdGlvbgogIH0sCiAgZGlyZWN0aXZlczogewogICAgd2F2ZXM6IHdhdmVzCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHN0YXR1czogJycKICAgICAgfSwKICAgICAgdGVtcDogewogICAgICAgIHRhZ19pZDogdW5kZWZpbmVkLAogICAgICAgIHRhZ19uYW1lOiAnJywKICAgICAgICB0YWdfY29sb3I6ICcjNDA5RUZGJywKICAgICAgICB0YWdfZGVzYzogJycsCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICBzb3J0X29yZGVyOiAwCiAgICAgIH0sCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nU3RhdHVzOiAnJywKICAgICAgdGV4dE1hcDogewogICAgICAgIHVwZGF0ZTogJ+e8lui+keagh+etvicsCiAgICAgICAgY3JlYXRlOiAn5re75Yqg5qCH562+JwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIHRhZ19uYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5qCH562+5ZCN56ew5LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKCiAgICAgIC8vIOaooeaLn+iAg+eglOaUv+ayu+agh+etvuaVsOaNrgogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5saXN0ID0gW3sKICAgICAgICAgIHRhZ19pZDogMSwKICAgICAgICAgIHRhZ19uYW1lOiAn6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGJywKICAgICAgICAgIHRhZ19jb2xvcjogJyNFNzRDM0MnLAogICAgICAgICAgdGFnX2Rlc2M6ICfpqazlhYvmgJ3kuLvkuYnln7rmnKzljp/nkIbmpoLorrrnm7jlhbPpopjnm64nLAogICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE1NiwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBzb3J0X29yZGVyOiAxLAogICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgfSwgewogICAgICAgICAgdGFnX2lkOiAyLAogICAgICAgICAgdGFnX25hbWU6ICfmr5vms73kuJzmgJ3mg7MnLAogICAgICAgICAgdGFnX2NvbG9yOiAnI0YzOUMxMicsCiAgICAgICAgICB0YWdfZGVzYzogJ+avm+azveS4nOaAneaDs+WSjOS4reWbveeJueiJsuekvuS8muS4u+S5ieeQhuiuuuS9k+ezu+amguiuuicsCiAgICAgICAgICBxdWVzdGlvbl9jb3VudDogMjAzLAogICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgIHNvcnRfb3JkZXI6IDIsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMScKICAgICAgICB9LCB7CiAgICAgICAgICB0YWdfaWQ6IDMsCiAgICAgICAgICB0YWdfbmFtZTogJ+S4reWbvei/keeOsOS7o+WPsue6suimgScsCiAgICAgICAgICB0YWdfY29sb3I6ICcjMjdBRTYwJywKICAgICAgICAgIHRhZ19kZXNjOiAn5Lit5Zu96L+R546w5Luj5Y+y57qy6KaB55u45YWz6aKY55uuJywKICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxNzgsCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgc29ydF9vcmRlcjogMywKICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgIH0sIHsKICAgICAgICAgIHRhZ19pZDogNCwKICAgICAgICAgIHRhZ19uYW1lOiAn5oCd5oOz6YGT5b635LiO5rOV5rK7JywKICAgICAgICAgIHRhZ19jb2xvcjogJyMzNDk4REInLAogICAgICAgICAgdGFnX2Rlc2M6ICfmgJ3mg7PpgZPlvrfkuI7ms5Xmsrvnm7jlhbPpopjnm64nLAogICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDEzNCwKICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICBzb3J0X29yZGVyOiA0LAogICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgfSwgewogICAgICAgICAgdGFnX2lkOiA1LAogICAgICAgICAgdGFnX25hbWU6ICflvaLlir/kuI7mlL/nrZYnLAogICAgICAgICAgdGFnX2NvbG9yOiAnIzlCNTlCNicsCiAgICAgICAgICB0YWdfZGVzYzogJ+W9ouWKv+S4juaUv+etluS7peWPiuW9k+S7o+S4lueVjOe7j+a1juS4juaUv+ayuycsCiAgICAgICAgICBxdWVzdGlvbl9jb3VudDogODksCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgc29ydF9vcmRlcjogNSwKICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgIH0sIHsKICAgICAgICAgIHRhZ19pZDogNiwKICAgICAgICAgIHRhZ19uYW1lOiAn6YeN54K56Zq+54K5JywKICAgICAgICAgIHRhZ19jb2xvcjogJyNFNjdFMjInLAogICAgICAgICAgdGFnX2Rlc2M6ICfmoIforrDkuLrph43ngrnpmr7ngrnnmoTpopjnm64nLAogICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDY3LAogICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgIHNvcnRfb3JkZXI6IDYsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMicKICAgICAgICB9LCB7CiAgICAgICAgICB0YWdfaWQ6IDcsCiAgICAgICAgICB0YWdfbmFtZTogJ+mrmOmikeiAg+eCuScsCiAgICAgICAgICB0YWdfY29sb3I6ICcjQzAzOTJCJywKICAgICAgICAgIHRhZ19kZXNjOiAn5Y6G5bm06ICD6K+V5Lit55qE6auY6aKR6ICD54K5JywKICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxNDUsCiAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgc29ydF9vcmRlcjogNywKICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAyJwogICAgICAgIH0sIHsKICAgICAgICAgIHRhZ19pZDogOCwKICAgICAgICAgIHRhZ19uYW1lOiAn5piT6ZSZ6aKYJywKICAgICAgICAgIHRhZ19jb2xvcjogJyM4RTQ0QUQnLAogICAgICAgICAgdGFnX2Rlc2M6ICflrabnlJ/lrrnmmJPlh7rplJnnmoTpopjnm64nLAogICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDk4LAogICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgIHNvcnRfb3JkZXI6IDgsCiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMicKICAgICAgICB9XTsKICAgICAgICBfdGhpcy50b3RhbCA9IF90aGlzLmxpc3QubGVuZ3RoOwogICAgICAgIF90aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0sIDUwMCk7CiAgICB9LAogICAgaGFuZGxlRmlsdGVyOiBmdW5jdGlvbiBoYW5kbGVGaWx0ZXIoKSB7CiAgICAgIHRoaXMubGlzdFF1ZXJ5LnBhZ2UgPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICByZXNldFRlbXA6IGZ1bmN0aW9uIHJlc2V0VGVtcCgpIHsKICAgICAgdGhpcy50ZW1wID0gewogICAgICAgIHRhZ19pZDogdW5kZWZpbmVkLAogICAgICAgIHRhZ19uYW1lOiAnJywKICAgICAgICB0YWdfY29sb3I6ICcjNDA5RUZGJywKICAgICAgICB0YWdfZGVzYzogJycsCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICBzb3J0X29yZGVyOiAwCiAgICAgIH07CiAgICB9LAogICAgaGFuZGxlQ3JlYXRlOiBmdW5jdGlvbiBoYW5kbGVDcmVhdGUoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0VGVtcCgpOwogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICdjcmVhdGUnOwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi4kcmVmc1snZGF0YUZvcm0nXS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGNyZWF0ZURhdGE6IGZ1bmN0aW9uIGNyZWF0ZURhdGEoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgX3RoaXMzLiRub3RpZnkoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfliJvlu7rmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzMy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLnRlbXAgPSBPYmplY3QuYXNzaWduKHt9LCByb3cpOwogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICd1cGRhdGUnOwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC4kcmVmc1snZGF0YUZvcm0nXS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIHVwZGF0ZURhdGE6IGZ1bmN0aW9uIHVwZGF0ZURhdGEoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgX3RoaXM1LiRub3RpZnkoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmm7TmlrDmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzNS5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTor6XmoIfnrb7lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM2LiRub3RpZnkoewogICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKn++8iOaooeaLn++8iScsCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["waves", "formatDate", "Pagination", "name", "components", "directives", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "status", "temp", "tag_id", "undefined", "tag_name", "tag_color", "tag_desc", "sort_order", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "setTimeout", "question_count", "created_at", "length", "handleFilter", "resetTemp", "handleCreate", "_this2", "$nextTick", "$refs", "clearValidate", "createData", "_this3", "validate", "valid", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this4", "Object", "assign", "updateData", "_this5", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "then"], "sources": ["src/views/questions/tags.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索标签名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加标签\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"tag_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"标签名称\" prop=\"tag_name\" min-width=\"150\" />\n      <el-table-column label=\"标签颜色\" width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :color=\"row.tag_color\" style=\"color: white;\">\n            {{ row.tag_name }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"标签描述\" prop=\"tag_desc\" min-width=\"200\" />\n      <el-table-column label=\"题目数量\" prop=\"question_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ formatDate(row.created_at) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"标签名称\" prop=\"tag_name\">\n          <el-input v-model=\"temp.tag_name\" />\n        </el-form-item>\n        <el-form-item label=\"标签颜色\" prop=\"tag_color\">\n          <el-color-picker v-model=\"temp.tag_color\" />\n        </el-form-item>\n        <el-form-item label=\"标签描述\" prop=\"tag_desc\">\n          <el-input v-model=\"temp.tag_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionTags',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      temp: {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑标签',\n        create: '添加标签'\n      },\n      rules: {\n        tag_name: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟考研政治标签数据\n      setTimeout(() => {\n        this.list = [\n          {\n            tag_id: 1,\n            tag_name: '马克思主义基本原理',\n            tag_color: '#E74C3C',\n            tag_desc: '马克思主义基本原理概论相关题目',\n            question_count: 156,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 2,\n            tag_name: '毛泽东思想',\n            tag_color: '#F39C12',\n            tag_desc: '毛泽东思想和中国特色社会主义理论体系概论',\n            question_count: 203,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 3,\n            tag_name: '中国近现代史纲要',\n            tag_color: '#27AE60',\n            tag_desc: '中国近现代史纲要相关题目',\n            question_count: 178,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 4,\n            tag_name: '思想道德与法治',\n            tag_color: '#3498DB',\n            tag_desc: '思想道德与法治相关题目',\n            question_count: 134,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 5,\n            tag_name: '形势与政策',\n            tag_color: '#9B59B6',\n            tag_desc: '形势与政策以及当代世界经济与政治',\n            question_count: 89,\n            status: 'active',\n            sort_order: 5,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 6,\n            tag_name: '重点难点',\n            tag_color: '#E67E22',\n            tag_desc: '标记为重点难点的题目',\n            question_count: 67,\n            status: 'active',\n            sort_order: 6,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 7,\n            tag_name: '高频考点',\n            tag_color: '#C0392B',\n            tag_desc: '历年考试中的高频考点',\n            question_count: 145,\n            status: 'active',\n            sort_order: 7,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 8,\n            tag_name: '易错题',\n            tag_color: '#8E44AD',\n            tag_desc: '学生容易出错的题目',\n            question_count: 98,\n            status: 'active',\n            sort_order: 8,\n            created_at: '2024-01-02'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该标签吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n    \n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA,OAAAA,KAAA;AACA,SAAAC,UAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,IAAA;QACAC,MAAA,EAAAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAN,MAAA;QACAO,UAAA;MACA;MACAC,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAT,QAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAzB,WAAA;;MAEA;MACA0B,UAAA;QACAD,KAAA,CAAA3B,IAAA,IACA;UACAS,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,GACA;UACArB,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,GACA;UACArB,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,GACA;UACArB,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,GACA;UACArB,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,GACA;UACArB,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,GACA;UACArB,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,GACA;UACArB,MAAA;UACAE,QAAA;UACAC,SAAA;UACAC,QAAA;UACAgB,cAAA;UACAtB,MAAA;UACAO,UAAA;UACAgB,UAAA;QACA,EACA;QACAH,KAAA,CAAA1B,KAAA,GAAA0B,KAAA,CAAA3B,IAAA,CAAA+B,MAAA;QACAJ,KAAA,CAAAzB,WAAA;MACA;IACA;IACA8B,YAAA,WAAAA,aAAA;MACA,KAAA7B,SAAA,CAAAC,IAAA;MACA,KAAAqB,OAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACA,KAAAzB,IAAA;QACAC,MAAA,EAAAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAN,MAAA;QACAO,UAAA;MACA;IACA;IACAoB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAAjB,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAG,OAAA;YACAC,KAAA;YACAtB,OAAA;YACAuB,IAAA;YACAC,QAAA;UACA;UACAN,MAAA,CAAAzB,iBAAA;QACA;MACA;IACA;IACAgC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAzC,IAAA,GAAA0C,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAAhC,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAa,MAAA,CAAAZ,KAAA,aAAAC,aAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAW,MAAA,CAAAV,OAAA;YACAC,KAAA;YACAtB,OAAA;YACAuB,IAAA;YACAC,QAAA;UACA;UACAO,MAAA,CAAAtC,iBAAA;QACA;MACA;IACA;IACAuC,YAAA,WAAAA,aAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAb,IAAA;MACA,GAAAc,IAAA;QACAJ,MAAA,CAAAZ,OAAA;UACAC,KAAA;UACAtB,OAAA;UACAuB,IAAA;UACAC,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}