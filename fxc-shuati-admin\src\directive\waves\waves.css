.waves-ripple {
  position: absolute;
  border-radius: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  background-clip: padding-box;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  opacity: 1;
}

.waves-ripple.waves-anim {
  -webkit-animation: waves-ripple 0.65s ease-out;
  animation: waves-ripple 0.65s ease-out;
}

@-webkit-keyframes waves-ripple {
  to {
    -webkit-transform: scale(2);
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes waves-ripple {
  to {
    -webkit-transform: scale(2);
    transform: scale(2);
    opacity: 0;
  }
}
