# 刷题小程序

## 子项目

- fxc-shuati：微信小程序，原生开发（参考：https://developers.weixin.qq.com/miniprogram/dev/component/）
- fxc-shuati-admin：管理后台，基于vue2 + elementUI
- fxc-shuati-api：接口，基于 express + mysql

## 环境

node版本：v14.12.0

先用 mock 数据，暂时不连数据库

## 功能

** 后台管理（fxc-shuati-admin）**

```
1. 管理员登录与退出：默认账号 admin，密码 admin123
2. 微信小程序用户登录（默认的微信openid登录即可）后，可做会员管理
3. 题库管理：题目无限极分类、题库上传
4. 其他功能：题目收藏、错题管理
5. 打通权限系统：用户、角色、菜单
```

** 微信小程序（fxc-shuati-weixin）**

```
1. 微信小程序用户登录（默认的微信openid登录即可）
2. 刷题：支持上一题下一题、左右滑动，带答题计时，错题本，收藏本，难度
3. 底部导航：首页、题库、我的
```

** API接口（fxc-shuati-api）**

```
1.数据库
地址：*************
用户名：fxc-shuati
数据库名：fxc-shuati
密码：haf6LeMjHLKefKs7
```