<template>
  <div class="app-container">
    <div class="placeholder-content">
      <el-card>
        <div slot="header">
          <span>错题管理</span>
        </div>
        <div class="placeholder-body">
          <el-alert
            title="功能开发中"
            type="info"
            description="错题管理功能正在开发中，敬请期待..."
            show-icon
            :closable="false"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorsList',
  data() {
    return {}
  }
}
</script>

<style scoped>
.placeholder-content {
  min-height: 400px;
}
.placeholder-body {
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}
</style>
