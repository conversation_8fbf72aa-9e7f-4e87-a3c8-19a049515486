# 刷题系统

基于题型.md和需求.md创建的完整刷题系统，包含微信小程序、后台管理系统和API接口。

## 项目结构

```
fanxiaochang/
├── fxc-shuati-weixin/     # 微信小程序 (原生开发)
├── fxc-shuati-admin/      # 后台管理系统 (Vue2 + Element UI)
├── fxc-shuati-api/        # API接口服务 (Node.js + Express + MySQL)
├── 题型.md                # 题型需求文档
├── 需求.md                # 功能需求文档
├── start.bat              # 一键启动脚本
└── README.md              # 项目说明文档
```

## 技术栈

### 微信小程序 (fxc-shuati-weixin)
- **框架**: 微信小程序原生框架
- **样式**: WXSS + CSS Grid/Flexbox
- **数据**: 本地存储 + API接口

### 后台管理系统 (fxc-shuati-admin)
- **框架**: Vue 2.x
- **UI组件**: Element UI
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios

### API接口服务 (fxc-shuati-api)
- **运行环境**: Node.js v14.20.0
- **框架**: Express.js
- **数据库**: MySQL
- **认证**: JWT
- **密码加密**: bcryptjs

## 环境要求

- Node.js v14.20.0
- MySQL 5.7+
- 微信开发者工具

## 数据库配置

- **地址**: 101.43.125.38
- **用户名**: fxc-shuati
- **数据库名**: fxc-shuati
- **密码**: haf6LeMjHLKefKs7

## 快速开始

### 方式一：使用一键启动脚本（推荐）

1. 确保已安装 Node.js v14.20.0
2. 双击运行 `start.bat` 脚本
3. 脚本会自动：
   - 初始化数据库
   - 启动API服务（端口3000）
   - 启动后台管理系统（端口9528）

### 方式二：手动启动

#### 1. 初始化数据库
```bash
cd fxc-shuati-api
npm install
npm run init-db
```

#### 2. 启动API服务
```bash
cd fxc-shuati-api
npm run dev
```

#### 3. 启动后台管理系统
```bash
cd fxc-shuati-admin
npm install
npm run dev
```

#### 4. 启动微信小程序
1. 打开微信开发者工具
2. 导入项目：选择 `fxc-shuati-weixin` 目录
3. 填写AppID（可使用测试号）
4. 点击编译运行

## 默认账号

### 后台管理系统
- **用户名**: admin
- **密码**: 123456

## 功能特性

### 题目类型支持
- ✅ 单选题
- ✅ 多选题
- ✅ 判断题
- ✅ 填空题
- ✅ 简答题
- ✅ 组合题（阅读理解等）

### 核心功能
- ✅ 用户认证与授权
- ✅ 题目分类管理
- ✅ 智能推荐
- ✅ 收藏与错题本
- ✅ 学习进度统计
- ✅ 答题计时
- ✅ 答案解析
- ✅ 手势操作（滑动切题）

### 管理功能
- ✅ 用户管理
- ✅ 题库管理
- ✅ 分类管理
- ✅ 数据统计
- ✅ 内容审核

## 数据库设计

主要数据表：
- `users` - 用户表
- `admins` - 管理员表
- `categories` - 分类表
- `questions` - 题目表
- `options` - 选项表
- `answers` - 答案表
- `explanations` - 解析表
- `composite_questions` - 组合题表
- `user_favorites` - 收藏表
- `user_errors` - 错题表
- `user_answer_records` - 答题记录表

详细的数据库结构请参考 `fxc-shuati-api/config/init.sql`

## 部署说明

### 开发环境
1. 启动API服务：`cd fxc-shuati-api && npm run dev`
2. 启动管理后台：`cd fxc-shuati-admin && npm run dev`
3. 启动小程序：使用HBuilderX或uni-app CLI

### 生产环境
1. API服务：使用PM2或Docker部署
2. 管理后台：构建后部署到Web服务器
3. 小程序：提交微信小程序审核

## 环境要求

- Node.js >= 14.0.0
- MySQL >= 5.7
- 微信开发者工具
- HBuilderX（推荐）

## 配置说明

### API接口配置
```bash
# 环境变量配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=fxc_shuati
JWT_SECRET=your_jwt_secret
PORT=3000
```

### 管理后台配置
```javascript
// src/utils/request.js
baseURL: 'http://localhost:3000/api'
```

### 小程序配置
```javascript
// 在相关页面中配置API地址
const API_BASE = 'http://localhost:3000/api'
```

## 开发规范

### 代码规范
- 使用ES6+语法
- 遵循Vue.js官方风格指南
- 统一使用2空格缩进
- 组件名使用PascalCase
- 变量名使用camelCase

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 许可证

MIT License

## 联系方式

- 开发者：fanxiaochang
- 项目地址：[GitHub仓库地址]
- 问题反馈：[Issues地址]

## 更新日志

### v1.0.0 (2023-12-01)
- 🎉 项目初始化
- ✨ 完成基础功能开发
- 📱 支持微信小程序
- 💻 管理后台上线
- 🔧 API接口完善

---

**注意：** 本项目为学习和演示目的，如需商业使用请确保遵循相关法律法规。
