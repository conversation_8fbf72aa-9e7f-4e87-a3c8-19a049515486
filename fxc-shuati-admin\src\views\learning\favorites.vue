<template>
  <div class="app-container">
    <el-card class="content-card">
      <div slot="header" class="card-header">
        <span>收藏管理</span>
      </div>
      <div class="placeholder-body">
        <el-alert
          title="功能开发中"
          type="info"
          description="收藏管理功能正在开发中，敬请期待..."
          show-icon
          :closable="false"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'FavoritesList',
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .content-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .placeholder-body {
      padding: 40px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
    }
  }
}
</style>
