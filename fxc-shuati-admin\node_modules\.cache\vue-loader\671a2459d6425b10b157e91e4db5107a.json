{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\Breadcrumb\\index.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBwYXRoVG9SZWdleHAgZnJvbSAncGF0aC10by1yZWdleHAnCgpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxldmVsTGlzdDogbnVsbAogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZSgpIHsKICAgICAgdGhpcy5nZXRCcmVhZGNydW1iKCkKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldEJyZWFkY3J1bWIoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QnJlYWRjcnVtYigpIHsKICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUKICAgICAgbGV0IG1hdGNoZWQgPSB0aGlzLiRyb3V0ZS5tYXRjaGVkLmZpbHRlcihpdGVtID0+IGl0ZW0ubWV0YSAmJiBpdGVtLm1ldGEudGl0bGUpCiAgICAgIGNvbnN0IGZpcnN0ID0gbWF0Y2hlZFswXQoKICAgICAgaWYgKCF0aGlzLmlzRGFzaGJvYXJkKGZpcnN0KSkgewogICAgICAgIG1hdGNoZWQgPSBbeyBwYXRoOiAnL2Rhc2hib2FyZCcsIG1ldGE6IHsgdGl0bGU6ICdEYXNoYm9hcmQnIH19XS5jb25jYXQobWF0Y2hlZCkKICAgICAgfQoKICAgICAgdGhpcy5sZXZlbExpc3QgPSBtYXRjaGVkLmZpbHRlcihpdGVtID0+IGl0ZW0ubWV0YSAmJiBpdGVtLm1ldGEudGl0bGUgJiYgaXRlbS5tZXRhLmJyZWFkY3J1bWIgIT09IGZhbHNlKQogICAgfSwKICAgIGlzRGFzaGJvYXJkKHJvdXRlKSB7CiAgICAgIGNvbnN0IG5hbWUgPSByb3V0ZSAmJiByb3V0ZS5uYW1lCiAgICAgIGlmICghbmFtZSkgewogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICAgIHJldHVybiBuYW1lLnRyaW0oKS50b0xvY2FsZUxvd2VyQ2FzZSgpID09PSAnRGFzaGJvYXJkJy50b0xvY2FsZUxvd2VyQ2FzZSgpCiAgICB9LAogICAgcGF0aENvbXBpbGUocGF0aCkgewogICAgICAvLyBUbyBzb2x2ZSB0aGlzIHByb2JsZW0gaHR0cHM6Ly9naXRodWIuY29tL1BhbkppYUNoZW4vdnVlLWVsZW1lbnQtYWRtaW4vaXNzdWVzLzU2MQogICAgICBjb25zdCB7IHBhcmFtcyB9ID0gdGhpcy4kcm91dGUKICAgICAgdmFyIHRvUGF0aCA9IHBhdGhUb1JlZ2V4cC5jb21waWxlKHBhdGgpCiAgICAgIHJldHVybiB0b1BhdGgocGFyYW1zKQogICAgfSwKICAgIGhhbmRsZUxpbmsoaXRlbSkgewogICAgICBjb25zdCB7IHJlZGlyZWN0LCBwYXRoIH0gPSBpdGVtCiAgICAgIGlmIChyZWRpcmVjdCkgewogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHJlZGlyZWN0KQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHRoaXMucGF0aENvbXBpbGUocGF0aCkpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n    <transition-group name=\"breadcrumb\">\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\n      </el-breadcrumb-item>\n    </transition-group>\n  </el-breadcrumb>\n</template>\n\n<script>\nimport pathToRegexp from 'path-to-regexp'\n\nexport default {\n  data() {\n    return {\n      levelList: null\n    }\n  },\n  watch: {\n    $route() {\n      this.getBreadcrumb()\n    }\n  },\n  created() {\n    this.getBreadcrumb()\n  },\n  methods: {\n    getBreadcrumb() {\n      // only show routes with meta.title\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\n      const first = matched[0]\n\n      if (!this.isDashboard(first)) {\n        matched = [{ path: '/dashboard', meta: { title: 'Dashboard' }}].concat(matched)\n      }\n\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\n    },\n    isDashboard(route) {\n      const name = route && route.name\n      if (!name) {\n        return false\n      }\n      return name.trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()\n    },\n    pathCompile(path) {\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\n      const { params } = this.$route\n      var toPath = pathToRegexp.compile(path)\n      return toPath(params)\n    },\n    handleLink(item) {\n      const { redirect, path } = item\n      if (redirect) {\n        this.$router.push(redirect)\n        return\n      }\n      this.$router.push(this.pathCompile(path))\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-breadcrumb.el-breadcrumb {\n  display: inline-block;\n  font-size: 14px;\n  line-height: 50px;\n  margin-left: 8px;\n\n  .no-redirect {\n    color: #97a8be;\n    cursor: text;\n  }\n}\n</style>\n"]}]}