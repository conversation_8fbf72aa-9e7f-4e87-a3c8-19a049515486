{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue", "mtime": 1752631093713}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcycKaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1F1ZXN0aW9uVGFncycsCiAgY29tcG9uZW50czogeyBQYWdpbmF0aW9uIH0sCiAgZGlyZWN0aXZlczogeyB3YXZlcyB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJsZUtleTogMCwKICAgICAgbGlzdDogW10sCiAgICAgIHRvdGFsOiAwLAogICAgICBsaXN0TG9hZGluZzogdHJ1ZSwKICAgICAgbGlzdFF1ZXJ5OiB7CiAgICAgICAgcGFnZTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAga2V5d29yZDogJycsCiAgICAgICAgc3RhdHVzOiAnJwogICAgICB9LAogICAgICB0ZW1wOiB7CiAgICAgICAgdGFnX2lkOiB1bmRlZmluZWQsCiAgICAgICAgdGFnX25hbWU6ICcnLAogICAgICAgIHRhZ19jb2xvcjogJyM0MDlFRkYnLAogICAgICAgIHRhZ19kZXNjOiAnJywKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgIHNvcnRfb3JkZXI6IDAKICAgICAgfSwKICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dTdGF0dXM6ICcnLAogICAgICB0ZXh0TWFwOiB7CiAgICAgICAgdXBkYXRlOiAn57yW6L6R5qCH562+JywKICAgICAgICBjcmVhdGU6ICfmt7vliqDmoIfnrb4nCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdGFnX25hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5qCH562+5ZCN56ew5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH1dCiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWUKICAgICAgCiAgICAgIC8vIOaooeaLn+iAg+eglOaUv+ayu+agh+etvuaVsOaNrgogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLmxpc3QgPSBbCiAgICAgICAgICAvLyDlrabnp5HmoIfnrb4KICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiAxLAogICAgICAgICAgICB0YWdfbmFtZTogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhicsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyNFNzRDM0MnLAogICAgICAgICAgICB0YWdfZGVzYzogJ+mprOWFi+aAneS4u+S5ieWfuuacrOWOn+eQhuamguiuuuebuOWFs+mimOebricsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAyNDUsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDEsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiAyLAogICAgICAgICAgICB0YWdfbmFtZTogJ+avm+azveS4nOaAneaDsycsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyNGMzlDMTInLAogICAgICAgICAgICB0YWdfZGVzYzogJ+avm+azveS4nOaAneaDs+WSjOS4reWbveeJueiJsuekvuS8muS4u+S5ieeQhuiuuuS9k+ezu+amguiuuicsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAzMTIsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDIsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiAzLAogICAgICAgICAgICB0YWdfbmFtZTogJ+S4reWbvei/keeOsOS7o+WPsue6suimgScsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyMyN0FFNjAnLAogICAgICAgICAgICB0YWdfZGVzYzogJ+S4reWbvei/keeOsOS7o+WPsue6suimgeebuOWFs+mimOebricsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxOTgsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDMsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiA0LAogICAgICAgICAgICB0YWdfbmFtZTogJ+aAneaDs+mBk+W+t+S4juazleayuycsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyMzNDk4REInLAogICAgICAgICAgICB0YWdfZGVzYzogJ+aAneaDs+mBk+W+t+S4juazleayu+ebuOWFs+mimOebricsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxNjcsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDQsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiA1LAogICAgICAgICAgICB0YWdfbmFtZTogJ+W9ouWKv+S4juaUv+etlicsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyM5QjU5QjYnLAogICAgICAgICAgICB0YWdfZGVzYzogJ+W9ouWKv+S4juaUv+etluS7peWPiuW9k+S7o+S4lueVjOe7j+a1juS4juaUv+ayuycsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxMjMsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDUsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAxJwogICAgICAgICAgfSwKICAgICAgICAgIC8vIOmHjeimgeaAp+agh+etvgogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDYsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6YeN54K56Zq+54K5JywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnI0U2N0UyMicsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn5qCH6K6w5Li66YeN54K56Zq+54K555qE6aKY55uuJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE1NiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogNiwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDcsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6auY6aKR6ICD54K5JywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnI0MwMzkyQicsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn5Y6G5bm06ICD6K+V5Lit55qE6auY6aKR6ICD54K5JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDIzNCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogNywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDgsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5piT6ZSZ6aKYJywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnIzhFNDRBRCcsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn5a2m55Sf5a655piT5Ye66ZSZ55qE6aKY55uuJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE4OSwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogOCwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9LAogICAgICAgICAgLy8g5LiT6aKY5qCH562+CiAgICAgICAgICB7CiAgICAgICAgICAgIHRhZ19pZDogOSwKICAgICAgICAgICAgdGFnX25hbWU6ICflk7Llrabljp/nkIYnLAogICAgICAgICAgICB0YWdfY29sb3I6ICcjMkMzRTUwJywKICAgICAgICAgICAgdGFnX2Rlc2M6ICfpqazlhYvmgJ3kuLvkuYnlk7Llrabln7rmnKzljp/nkIYnLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogMTQ1LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiA5LAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMycKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHRhZ19pZDogMTAsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5pS/5rK757uP5rWO5a2mJywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnIzE2QTA4NScsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn6ams5YWL5oCd5Li75LmJ5pS/5rK757uP5rWO5a2m5Y6f55CGJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDc4LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxMCwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDMnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDExLAogICAgICAgICAgICB0YWdfbmFtZTogJ+enkeWtpuekvuS8muS4u+S5iScsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyNEMzU0MDAnLAogICAgICAgICAgICB0YWdfZGVzYzogJ+enkeWtpuekvuS8muS4u+S5ieWfuuacrOWOn+eQhicsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiA2NywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMTEsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAzJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiAxMiwKICAgICAgICAgICAgdGFnX25hbWU6ICfmlrDmsJHkuLvkuLvkuYnpnanlkb0nLAogICAgICAgICAgICB0YWdfY29sb3I6ICcjOEI0NTEzJywKICAgICAgICAgICAgdGFnX2Rlc2M6ICfmlrDmsJHkuLvkuLvkuYnpnanlkb3nkIborronLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogODksCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDEyLAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMycKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHRhZ19pZDogMTMsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn56S+5Lya5Li75LmJ5bu66K6+JywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnI0ZGNjM0NycsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn56S+5Lya5Li75LmJ6Z2p5ZG95ZKM5bu66K6+JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDExMiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMTMsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAzJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiAxNCwKICAgICAgICAgICAgdGFnX25hbWU6ICfmlLnpnanlvIDmlL4nLAogICAgICAgICAgICB0YWdfY29sb3I6ICcjNDE2OUUxJywKICAgICAgICAgICAgdGFnX2Rlc2M6ICfmlLnpnanlvIDmlL7lkoznjrDku6PljJblu7rorr4nLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogMTM0LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxNCwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDMnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDE1LAogICAgICAgICAgICB0YWdfbmFtZTogJ+aWsOaXtuS7oycsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyNGRjE0OTMnLAogICAgICAgICAgICB0YWdfZGVzYzogJ+aWsOaXtuS7o+S4reWbveeJueiJsuekvuS8muS4u+S5iScsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiA5OCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMTUsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAzJwogICAgICAgICAgfSwKICAgICAgICAgIC8vIOW5tOS7veagh+etvgogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDE2LAogICAgICAgICAgICB0YWdfbmFtZTogJzIwMjPlubTnnJ/popgnLAogICAgICAgICAgICB0YWdfY29sb3I6ICcjMzJDRDMyJywKICAgICAgICAgICAgdGFnX2Rlc2M6ICcyMDIz5bm06ICD56CU5pS/5rK755yf6aKYJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDQ1LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxNiwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDQnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDE3LAogICAgICAgICAgICB0YWdfbmFtZTogJzIwMjLlubTnnJ/popgnLAogICAgICAgICAgICB0YWdfY29sb3I6ICcjMjBCMkFBJywKICAgICAgICAgICAgdGFnX2Rlc2M6ICcyMDIy5bm06ICD56CU5pS/5rK755yf6aKYJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDQ1LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiAxNywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDQnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDE4LAogICAgICAgICAgICB0YWdfbmFtZTogJ+aooeaLn+mimCcsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyM3Nzg4OTknLAogICAgICAgICAgICB0YWdfZGVzYzogJ+aooeaLn+iAg+ivlemimOebricsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAyNjcsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDE4LAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wNCcKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMubGlzdC5sZW5ndGgKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2UKICAgICAgfSwgNTAwKQogICAgfSwKICAgIGhhbmRsZUZpbHRlcigpIHsKICAgICAgdGhpcy5saXN0UXVlcnkucGFnZSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICByZXNldFRlbXAoKSB7CiAgICAgIHRoaXMudGVtcCA9IHsKICAgICAgICB0YWdfaWQ6IHVuZGVmaW5lZCwKICAgICAgICB0YWdfbmFtZTogJycsCiAgICAgICAgdGFnX2NvbG9yOiAnIzQwOUVGRicsCiAgICAgICAgdGFnX2Rlc2M6ICcnLAogICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgc29ydF9vcmRlcjogMAogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ3JlYXRlKCkgewogICAgICB0aGlzLnJlc2V0VGVtcCgpCiAgICAgIHRoaXMuZGlhbG9nU3RhdHVzID0gJ2NyZWF0ZScKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgY3JlYXRlRGF0YSgpIHsKICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgbWVzc2FnZTogJ+WIm+W7uuaIkOWKn++8iOaooeaLn++8iScsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnRlbXAgPSBPYmplY3QuYXNzaWduKHt9LCByb3cpCiAgICAgIHRoaXMuZGlhbG9nU3RhdHVzID0gJ3VwZGF0ZScKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgdXBkYXRlRGF0YSgpIHsKICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgbWVzc2FnZTogJ+abtOaWsOaIkOWKn++8iOaooeaLn++8iScsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTor6XmoIfnrb7lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICB9KQogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["tags.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "tags.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索标签名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加标签\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"tag_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"标签名称\" prop=\"tag_name\" min-width=\"150\" />\n      <el-table-column label=\"标签颜色\" width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :color=\"row.tag_color\" style=\"color: white;\">\n            {{ row.tag_name }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"标签描述\" prop=\"tag_desc\" min-width=\"200\" />\n      <el-table-column label=\"题目数量\" prop=\"question_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"标签名称\" prop=\"tag_name\">\n          <el-input v-model=\"temp.tag_name\" />\n        </el-form-item>\n        <el-form-item label=\"标签颜色\" prop=\"tag_color\">\n          <el-color-picker v-model=\"temp.tag_color\" />\n        </el-form-item>\n        <el-form-item label=\"标签描述\" prop=\"tag_desc\">\n          <el-input v-model=\"temp.tag_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionTags',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      temp: {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑标签',\n        create: '添加标签'\n      },\n      rules: {\n        tag_name: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟考研政治标签数据\n      setTimeout(() => {\n        this.list = [\n          // 学科标签\n          {\n            tag_id: 1,\n            tag_name: '马克思主义基本原理',\n            tag_color: '#E74C3C',\n            tag_desc: '马克思主义基本原理概论相关题目',\n            question_count: 245,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 2,\n            tag_name: '毛泽东思想',\n            tag_color: '#F39C12',\n            tag_desc: '毛泽东思想和中国特色社会主义理论体系概论',\n            question_count: 312,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 3,\n            tag_name: '中国近现代史纲要',\n            tag_color: '#27AE60',\n            tag_desc: '中国近现代史纲要相关题目',\n            question_count: 198,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 4,\n            tag_name: '思想道德与法治',\n            tag_color: '#3498DB',\n            tag_desc: '思想道德与法治相关题目',\n            question_count: 167,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 5,\n            tag_name: '形势与政策',\n            tag_color: '#9B59B6',\n            tag_desc: '形势与政策以及当代世界经济与政治',\n            question_count: 123,\n            status: 'active',\n            sort_order: 5,\n            created_at: '2024-01-01'\n          },\n          // 重要性标签\n          {\n            tag_id: 6,\n            tag_name: '重点难点',\n            tag_color: '#E67E22',\n            tag_desc: '标记为重点难点的题目',\n            question_count: 156,\n            status: 'active',\n            sort_order: 6,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 7,\n            tag_name: '高频考点',\n            tag_color: '#C0392B',\n            tag_desc: '历年考试中的高频考点',\n            question_count: 234,\n            status: 'active',\n            sort_order: 7,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 8,\n            tag_name: '易错题',\n            tag_color: '#8E44AD',\n            tag_desc: '学生容易出错的题目',\n            question_count: 189,\n            status: 'active',\n            sort_order: 8,\n            created_at: '2024-01-02'\n          },\n          // 专题标签\n          {\n            tag_id: 9,\n            tag_name: '哲学原理',\n            tag_color: '#2C3E50',\n            tag_desc: '马克思主义哲学基本原理',\n            question_count: 145,\n            status: 'active',\n            sort_order: 9,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 10,\n            tag_name: '政治经济学',\n            tag_color: '#16A085',\n            tag_desc: '马克思主义政治经济学原理',\n            question_count: 78,\n            status: 'active',\n            sort_order: 10,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 11,\n            tag_name: '科学社会主义',\n            tag_color: '#D35400',\n            tag_desc: '科学社会主义基本原理',\n            question_count: 67,\n            status: 'active',\n            sort_order: 11,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 12,\n            tag_name: '新民主主义革命',\n            tag_color: '#8B4513',\n            tag_desc: '新民主主义革命理论',\n            question_count: 89,\n            status: 'active',\n            sort_order: 12,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 13,\n            tag_name: '社会主义建设',\n            tag_color: '#FF6347',\n            tag_desc: '社会主义革命和建设',\n            question_count: 112,\n            status: 'active',\n            sort_order: 13,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 14,\n            tag_name: '改革开放',\n            tag_color: '#4169E1',\n            tag_desc: '改革开放和现代化建设',\n            question_count: 134,\n            status: 'active',\n            sort_order: 14,\n            created_at: '2024-01-03'\n          },\n          {\n            tag_id: 15,\n            tag_name: '新时代',\n            tag_color: '#FF1493',\n            tag_desc: '新时代中国特色社会主义',\n            question_count: 98,\n            status: 'active',\n            sort_order: 15,\n            created_at: '2024-01-03'\n          },\n          // 年份标签\n          {\n            tag_id: 16,\n            tag_name: '2023年真题',\n            tag_color: '#32CD32',\n            tag_desc: '2023年考研政治真题',\n            question_count: 45,\n            status: 'active',\n            sort_order: 16,\n            created_at: '2024-01-04'\n          },\n          {\n            tag_id: 17,\n            tag_name: '2022年真题',\n            tag_color: '#20B2AA',\n            tag_desc: '2022年考研政治真题',\n            question_count: 45,\n            status: 'active',\n            sort_order: 17,\n            created_at: '2024-01-04'\n          },\n          {\n            tag_id: 18,\n            tag_name: '模拟题',\n            tag_color: '#778899',\n            tag_desc: '模拟考试题目',\n            question_count: 267,\n            status: 'active',\n            sort_order: 18,\n            created_at: '2024-01-04'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该标签吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n    \n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}