{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\tags.vue", "mtime": 1752629413914}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcycKaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdRdWVzdGlvblRhZ3MnLAogIGNvbXBvbmVudHM6IHsgUGFnaW5hdGlvbiB9LAogIGRpcmVjdGl2ZXM6IHsgd2F2ZXMgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHN0YXR1czogJycKICAgICAgfSwKICAgICAgdGVtcDogewogICAgICAgIHRhZ19pZDogdW5kZWZpbmVkLAogICAgICAgIHRhZ19uYW1lOiAnJywKICAgICAgICB0YWdfY29sb3I6ICcjNDA5RUZGJywKICAgICAgICB0YWdfZGVzYzogJycsCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICBzb3J0X29yZGVyOiAwCiAgICAgIH0sCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nU3RhdHVzOiAnJywKICAgICAgdGV4dE1hcDogewogICAgICAgIHVwZGF0ZTogJ+e8lui+keagh+etvicsCiAgICAgICAgY3JlYXRlOiAn5re75Yqg5qCH562+JwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIHRhZ19uYW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+agh+etvuWQjeensOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9XQogICAgICB9CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlCiAgICAgIAogICAgICAvLyDmqKHmi5/ogIPnoJTmlL/msrvmoIfnrb7mlbDmja4KICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gWwogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDEsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CGJywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnI0U3NEMzQycsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn6ams5YWL5oCd5Li75LmJ5Z+65pys5Y6f55CG5qaC6K6655u45YWz6aKY55uuJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE1NiwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMSwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDIsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5q+b5rO95Lic5oCd5oOzJywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnI0YzOUMxMicsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn5q+b5rO95Lic5oCd5oOz5ZKM5Lit5Zu954m56Imy56S+5Lya5Li75LmJ55CG6K665L2T57O75qaC6K66JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDIwMywKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMiwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDMsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5Lit5Zu96L+R546w5Luj5Y+y57qy6KaBJywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnIzI3QUU2MCcsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn5Lit5Zu96L+R546w5Luj5Y+y57qy6KaB55u45YWz6aKY55uuJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDE3OCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogMywKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDQsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5oCd5oOz6YGT5b635LiO5rOV5rK7JywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnIzM0OThEQicsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn5oCd5oOz6YGT5b635LiO5rOV5rK755u45YWz6aKY55uuJywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDEzNCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogNCwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0YWdfaWQ6IDUsCiAgICAgICAgICAgIHRhZ19uYW1lOiAn5b2i5Yq/5LiO5pS/562WJywKICAgICAgICAgICAgdGFnX2NvbG9yOiAnIzlCNTlCNicsCiAgICAgICAgICAgIHRhZ19kZXNjOiAn5b2i5Yq/5LiO5pS/562W5Lul5Y+K5b2T5Luj5LiW55WM57uP5rWO5LiO5pS/5rK7JywKICAgICAgICAgICAgcXVlc3Rpb25fY291bnQ6IDg5LAogICAgICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgICAgICBzb3J0X29yZGVyOiA1LAogICAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0wMScKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHRhZ19pZDogNiwKICAgICAgICAgICAgdGFnX25hbWU6ICfph43ngrnpmr7ngrknLAogICAgICAgICAgICB0YWdfY29sb3I6ICcjRTY3RTIyJywKICAgICAgICAgICAgdGFnX2Rlc2M6ICfmoIforrDkuLrph43ngrnpmr7ngrnnmoTpopjnm64nLAogICAgICAgICAgICBxdWVzdGlvbl9jb3VudDogNjcsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDYsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAyJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiA3LAogICAgICAgICAgICB0YWdfbmFtZTogJ+mrmOmikeiAg+eCuScsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyNDMDM5MkInLAogICAgICAgICAgICB0YWdfZGVzYzogJ+WOhuW5tOiAg+ivleS4reeahOmrmOmikeiAg+eCuScsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiAxNDUsCiAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgICAgIHNvcnRfb3JkZXI6IDcsCiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTAyJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGFnX2lkOiA4LAogICAgICAgICAgICB0YWdfbmFtZTogJ+aYk+mUmemimCcsCiAgICAgICAgICAgIHRhZ19jb2xvcjogJyM4RTQ0QUQnLAogICAgICAgICAgICB0YWdfZGVzYzogJ+WtpueUn+WuueaYk+WHuumUmeeahOmimOebricsCiAgICAgICAgICAgIHF1ZXN0aW9uX2NvdW50OiA5OCwKICAgICAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICAgICAgc29ydF9vcmRlcjogOCwKICAgICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDInCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICAgIHRoaXMudG90YWwgPSB0aGlzLmxpc3QubGVuZ3RoCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlCiAgICAgIH0sIDUwMCkKICAgIH0sCiAgICBoYW5kbGVGaWx0ZXIoKSB7CiAgICAgIHRoaXMubGlzdFF1ZXJ5LnBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgcmVzZXRUZW1wKCkgewogICAgICB0aGlzLnRlbXAgPSB7CiAgICAgICAgdGFnX2lkOiB1bmRlZmluZWQsCiAgICAgICAgdGFnX25hbWU6ICcnLAogICAgICAgIHRhZ19jb2xvcjogJyM0MDlFRkYnLAogICAgICAgIHRhZ19kZXNjOiAnJywKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgIHNvcnRfb3JkZXI6IDAKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUNyZWF0ZSgpIHsKICAgICAgdGhpcy5yZXNldFRlbXAoKQogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICdjcmVhdGUnCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICB9KQogICAgfSwKICAgIGNyZWF0ZURhdGEoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfliJvlu7rmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy50ZW1wID0gT2JqZWN0LmFzc2lnbih7fSwgcm93KQogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICd1cGRhdGUnCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICB9KQogICAgfSwKICAgIHVwZGF0ZURhdGEoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmm7TmlrDmiJDlip/vvIjmqKHmi5/vvIknLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6K+l5qCH562+5ZCX77yfJywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5Yqf77yI5qih5ouf77yJJywKICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["tags.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "tags.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索标签名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加标签\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"tag_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"标签名称\" prop=\"tag_name\" min-width=\"150\" />\n      <el-table-column label=\"标签颜色\" width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :color=\"row.tag_color\" style=\"color: white;\">\n            {{ row.tag_name }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"标签描述\" prop=\"tag_desc\" min-width=\"200\" />\n      <el-table-column label=\"题目数量\" prop=\"question_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ formatDate(row.created_at) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"标签名称\" prop=\"tag_name\">\n          <el-input v-model=\"temp.tag_name\" />\n        </el-form-item>\n        <el-form-item label=\"标签颜色\" prop=\"tag_color\">\n          <el-color-picker v-model=\"temp.tag_color\" />\n        </el-form-item>\n        <el-form-item label=\"标签描述\" prop=\"tag_desc\">\n          <el-input v-model=\"temp.tag_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionTags',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      temp: {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑标签',\n        create: '添加标签'\n      },\n      rules: {\n        tag_name: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟考研政治标签数据\n      setTimeout(() => {\n        this.list = [\n          {\n            tag_id: 1,\n            tag_name: '马克思主义基本原理',\n            tag_color: '#E74C3C',\n            tag_desc: '马克思主义基本原理概论相关题目',\n            question_count: 156,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 2,\n            tag_name: '毛泽东思想',\n            tag_color: '#F39C12',\n            tag_desc: '毛泽东思想和中国特色社会主义理论体系概论',\n            question_count: 203,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 3,\n            tag_name: '中国近现代史纲要',\n            tag_color: '#27AE60',\n            tag_desc: '中国近现代史纲要相关题目',\n            question_count: 178,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 4,\n            tag_name: '思想道德与法治',\n            tag_color: '#3498DB',\n            tag_desc: '思想道德与法治相关题目',\n            question_count: 134,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 5,\n            tag_name: '形势与政策',\n            tag_color: '#9B59B6',\n            tag_desc: '形势与政策以及当代世界经济与政治',\n            question_count: 89,\n            status: 'active',\n            sort_order: 5,\n            created_at: '2024-01-01'\n          },\n          {\n            tag_id: 6,\n            tag_name: '重点难点',\n            tag_color: '#E67E22',\n            tag_desc: '标记为重点难点的题目',\n            question_count: 67,\n            status: 'active',\n            sort_order: 6,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 7,\n            tag_name: '高频考点',\n            tag_color: '#C0392B',\n            tag_desc: '历年考试中的高频考点',\n            question_count: 145,\n            status: 'active',\n            sort_order: 7,\n            created_at: '2024-01-02'\n          },\n          {\n            tag_id: 8,\n            tag_name: '易错题',\n            tag_color: '#8E44AD',\n            tag_desc: '学生容易出错的题目',\n            question_count: 98,\n            status: 'active',\n            sort_order: 8,\n            created_at: '2024-01-02'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        tag_id: undefined,\n        tag_name: '',\n        tag_color: '#409EFF',\n        tag_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该标签吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n    \n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}