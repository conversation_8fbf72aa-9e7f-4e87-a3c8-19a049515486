/**favorites.wxss**/

.container {
  padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-section {
  margin: 20rpx;
  margin-bottom: 10rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 25rpx;
  padding: 10rpx 20rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
}

.search-btn {
  background-color: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
}

/* 筛选条件 */
.filter-section {
  margin: 20rpx;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-item.active {
  background-color: #ff6b6b;
  color: #fff;
}

/* 收藏列表 */
.favorite-list {
  padding: 0 20rpx;
}

.favorite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.favorite-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

.question-info {
  flex: 1;
  margin-right: 20rpx;
}

.question-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.question-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  font-size: 22rpx;
  color: #666;
}

.question-type {
  background-color: #e6f3ff;
  color: #409EFF;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.question-difficulty {
  color: #f56c6c;
}

.favorite-time {
  color: #999;
}

.favorite-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.practice-btn,
.remove-btn {
  width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
  border: none;
  border-radius: 30rpx;
  padding: 0;
  line-height: 60rpx;
}

.practice-btn {
  background-color: #409EFF;
  color: #fff;
}

.remove-btn {
  background-color: #ff6b6b;
  color: #fff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 50rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.go-practice-btn {
  width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #409EFF;
  color: #fff;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-btn {
  background-color: #f8f9fa;
  color: #666;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .favorite-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .question-info {
    margin-right: 0;
    margin-bottom: 20rpx;
    width: 100%;
  }
  
  .favorite-actions {
    flex-direction: row;
    width: 100%;
    justify-content: flex-end;
  }
}

/* 动画效果 */
.favorite-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
