{"_from": "commander@2.17.x", "_id": "commander@2.17.1", "_inBundle": false, "_integrity": "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==", "_location": "/html-minifier/commander", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "commander@2.17.x", "name": "commander", "escapedName": "commander", "rawSpec": "2.17.x", "saveSpec": null, "fetchSpec": "2.17.x"}, "_requiredBy": ["/html-minifier"], "_resolved": "https://registry.npmmirror.com/commander/-/commander-2.17.1.tgz", "_shasum": "bd77ab7de6de94205ceacc72f1716d29f20a77bf", "_spec": "commander@2.17.x", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\html-minifier", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "the complete solution for node.js command-line programs", "devDependencies": {"@types/node": "^10.5.7", "eslint": "^5.3.0", "should": "^13.2.3", "sinon": "^6.1.4", "standard": "^11.0.1", "typescript": "^2.9.2"}, "files": ["index.js", "typings/index.d.ts"], "homepage": "https://github.com/tj/commander.js#readme", "keywords": ["commander", "command", "option", "parser"], "license": "MIT", "main": "index", "name": "commander", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "version": "2.17.1"}