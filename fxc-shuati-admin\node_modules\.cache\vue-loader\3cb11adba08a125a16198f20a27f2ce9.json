{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue?vue&type=style&index=0&id=25336f9d&scoped=true&lang=css", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue", "mtime": 1752568894799}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wbGFjZWhvbGRlci1jb250ZW50IHsKICBtaW4taGVpZ2h0OiA0MDBweDsKfQoucGxhY2Vob2xkZXItYm9keSB7CiAgcGFkZGluZzogMjBweCAwOwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtaW4taGVpZ2h0OiAzMDBweDsKfQo="}, {"version": 3, "sources": ["upload.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "upload.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"placeholder-content\">\n      <el-card>\n        <div slot=\"header\">\n          <span>批量导入</span>\n        </div>\n        <div class=\"placeholder-body\">\n          <el-alert\n            title=\"功能开发中\"\n            type=\"info\"\n            description=\"批量导入功能正在开发中，敬请期待...\"\n            show-icon\n            :closable=\"false\"\n          />\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'QuestionUpload',\n  data() {\n    return {}\n  }\n}\n</script>\n\n<style scoped>\n.placeholder-content {\n  min-height: 400px;\n}\n.placeholder-body {\n  padding: 20px 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300px;\n}\n</style>\n"]}]}