<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索权限名称、编码"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.permission_type"
          placeholder="权限类型"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="菜单" value="menu" />
          <el-option label="按钮" value="button" />
          <el-option label="接口" value="api" />
        </el-select>
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加权限
        </el-button>
      </div>
    </el-card>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      row-key="permission_id"
      default-expand-all
    >
      <el-table-column label="ID" prop="permission_id" align="center" width="80" />
      <el-table-column label="权限名称" prop="permission_name" min-width="150" />
      <el-table-column label="权限编码" prop="permission_code" min-width="180" />
      <el-table-column label="权限类型" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="typeTagMap[row.permission_type]">
            {{ typeTextMap[row.permission_type] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="权限URL" prop="permission_url" min-width="180" />
      <el-table-column label="权限图标" width="100" align="center">
        <template slot-scope="{row}">
          <i v-if="row.permission_icon" :class="row.permission_icon" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="父权限" prop="parent_name" min-width="150" />
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort_order" width="80" align="center" />
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 450px; margin-left:50px;"
      >
        <el-form-item label="父权限" prop="parent_id">
          <el-select v-model="temp.parent_id" placeholder="请选择父权限" style="width: 100%">
            <el-option label="顶级权限" :value="0" />
            <el-option
              v-for="item in parentOptions"
              :key="item.permission_id"
              :label="item.permission_name"
              :value="item.permission_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权限名称" prop="permission_name">
          <el-input v-model="temp.permission_name" />
        </el-form-item>
        <el-form-item label="权限编码" prop="permission_code">
          <el-input v-model="temp.permission_code" />
        </el-form-item>
        <el-form-item label="权限类型" prop="permission_type">
          <el-select v-model="temp.permission_type" placeholder="请选择权限类型" style="width: 100%">
            <el-option label="菜单" value="menu" />
            <el-option label="按钮" value="button" />
            <el-option label="接口" value="api" />
          </el-select>
        </el-form-item>
        <el-form-item label="权限URL" prop="permission_url">
          <el-input v-model="temp.permission_url" />
        </el-form-item>
        <el-form-item label="权限图标" prop="permission_icon">
          <el-input v-model="temp.permission_icon" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="temp.sort_order" :min="0" :max="999" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPermissionList, createPermission, updatePermission, deletePermission } from '@/api/users'
import waves from '@/directive/waves'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'PermissionList',
  components: { Pagination },
  directives: { waves },
  filters: {
    parseTime
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 50,
        keyword: '',
        permission_type: '',
        status: ''
      },
      typeTextMap: {
        menu: '菜单',
        button: '按钮',
        api: '接口'
      },
      typeTagMap: {
        menu: 'primary',
        button: 'success',
        api: 'warning'
      },
      parentOptions: [],
      temp: {
        permission_id: undefined,
        parent_id: 0,
        permission_name: '',
        permission_code: '',
        permission_type: 'menu',
        permission_url: '',
        permission_icon: '',
        status: 'active',
        sort_order: 0
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑权限',
        create: '添加权限'
      },
      rules: {
        permission_name: [{ required: true, message: '权限名称不能为空', trigger: 'blur' }],
        permission_code: [{ required: true, message: '权限编码不能为空', trigger: 'blur' }],
        permission_type: [{ required: true, message: '权限类型不能为空', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getPermissionList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.listLoading = false
        
        // 更新父权限选项
        this.parentOptions = this.list.filter(item => item.permission_type === 'menu')
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        permission_id: undefined,
        parent_id: 0,
        permission_name: '',
        permission_code: '',
        permission_type: 'menu',
        permission_url: '',
        permission_icon: '',
        status: 'active',
        sort_order: 0
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createPermission(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updatePermission(tempData.permission_id, tempData).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该权限吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePermission(row.permission_id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-card {
    margin-bottom: 20px;
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .filter-item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  .el-table {
    margin-bottom: 20px;
    width: 100% !important;

    .el-table__body-wrapper {
      width: 100% !important;
    }
  }
  .pagination-container {
    padding: 15px 0;
  }
}
</style>
