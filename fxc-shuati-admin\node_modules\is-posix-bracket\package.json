{"_from": "is-posix-bracket@^0.1.0", "_id": "is-posix-bracket@0.1.1", "_inBundle": false, "_integrity": "sha512-Yu68oeXJ7LeWNmZ3Zov/xg/oDBnBK2RNxwYY1ilNJX+tKKZqgPK+qOn/Gs9jEu66KDY9Netf5XLKNGzas/vPfQ==", "_location": "/is-posix-bracket", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-posix-bracket@^0.1.0", "name": "is-posix-bracket", "escapedName": "is-posix-bracket", "rawSpec": "^0.1.0", "saveSpec": null, "fetchSpec": "^0.1.0"}, "_requiredBy": ["/babel-jest/expand-brackets"], "_resolved": "https://registry.npmmirror.com/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz", "_shasum": "3334dc79774368e92f016e6fbc0a88f5cd6e6bc4", "_spec": "is-posix-bracket@^0.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-jest\\node_modules\\expand-brackets", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-posix-bracket/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Returns true if the given string is a POSIX bracket expression (POSIX character class).", "devDependencies": {"gulp-format-md": "^0.1.7", "mocha": "^2.4.5"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-posix-bracket", "keywords": ["braces", "brackets", "character", "character-class", "class", "expression", "posix", "regex", "regexp", "regular"], "license": "MIT", "main": "index.js", "name": "is-posix-bracket", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-posix-bracket.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "is-extglob", "is-glob", "micromatch"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "version": "0.1.1"}