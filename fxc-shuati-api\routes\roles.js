const express = require('express');
const { query, transaction } = require('../config/database');
const router = express.Router();

// 获取角色列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, keyword = '', status = '' } = req.query;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (keyword) {
      whereClause += ' AND (role_name LIKE ? OR role_code LIKE ? OR role_desc LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }
    
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM roles ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    const listSql = `
      SELECT r.role_id, r.role_name, r.role_code, r.role_desc, r.status, r.sort_order, r.created_at,
             COUNT(DISTINCT ar.admin_id) as admin_count,
             COUNT(DISTINCT rp.permission_id) as permission_count
      FROM roles r
      LEFT JOIN admin_roles ar ON r.role_id = ar.role_id
      LEFT JOIN role_permissions rp ON r.role_id = rp.role_id
      ${whereClause}
      GROUP BY r.role_id
      ORDER BY r.sort_order ASC, r.created_at DESC
      LIMIT ? OFFSET ?
    `;
    params.push(parseInt(pageSize), offset);
    
    const list = await query(listSql, params);
    
    res.json({
      success: true,
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取角色列表失败:', error);

    // 数据库连接失败时返回模拟数据
    const mockData = [
      {
        role_id: 1,
        role_name: '超级管理员',
        role_code: 'super_admin',
        role_desc: '系统超级管理员，拥有所有权限',
        admin_count: 1,
        permission_count: 50,
        status: 'active',
        sort_order: 1,
        created_at: '2024-01-01 00:00:00'
      },
      {
        role_id: 2,
        role_name: '管理员',
        role_code: 'admin',
        role_desc: '普通管理员，拥有大部分管理权限',
        admin_count: 3,
        permission_count: 35,
        status: 'active',
        sort_order: 2,
        created_at: '2024-01-02 00:00:00'
      },
      {
        role_id: 3,
        role_name: '编辑员',
        role_code: 'editor',
        role_desc: '内容编辑员，负责题目和内容管理',
        admin_count: 5,
        permission_count: 20,
        status: 'active',
        sort_order: 3,
        created_at: '2024-01-03 00:00:00'
      },
      {
        role_id: 4,
        role_name: '审核员',
        role_code: 'reviewer',
        role_desc: '内容审核员，负责审核题目和内容',
        admin_count: 2,
        permission_count: 15,
        status: 'active',
        sort_order: 4,
        created_at: '2024-01-04 00:00:00'
      }
    ];

    res.json({
      success: true,
      message: '获取角色列表成功（模拟数据）',
      data: {
        list: mockData,
        total: mockData.length,
        page: parseInt(req.query.page || 1),
        pageSize: parseInt(req.query.pageSize || 10)
      }
    });
  }
});

// 获取角色详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT r.role_id, r.role_name, r.role_code, r.role_desc, r.status, r.sort_order, r.created_at,
             COUNT(DISTINCT ar.admin_id) as admin_count,
             COUNT(DISTINCT rp.permission_id) as permission_count
      FROM roles r
      LEFT JOIN admin_roles ar ON r.role_id = ar.role_id
      LEFT JOIN role_permissions rp ON r.role_id = rp.role_id
      WHERE r.role_id = ?
      GROUP BY r.role_id
    `;
    
    const result = await query(sql, [id]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    res.json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('获取角色详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色详情失败'
    });
  }
});

// 创建角色
router.post('/', async (req, res) => {
  try {
    const { role_name, role_code, role_desc, status = 'active', sort_order = 0 } = req.body;
    
    if (!role_name || !role_code) {
      return res.status(400).json({
        success: false,
        message: '角色名称和角色编码不能为空'
      });
    }
    
    // 检查角色名称和编码是否已存在
    const existCheck = await query('SELECT role_id FROM roles WHERE role_name = ? OR role_code = ?', [role_name, role_code]);
    if (existCheck.length > 0) {
      return res.status(400).json({
        success: false,
        message: '角色名称或角色编码已存在'
      });
    }
    
    const insertSql = `
      INSERT INTO roles (role_name, role_code, role_desc, status, sort_order)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const result = await query(insertSql, [role_name, role_code, role_desc || '', status, sort_order]);
    
    res.json({
      success: true,
      message: '创建角色成功',
      data: {
        role_id: result.insertId
      }
    });
  } catch (error) {
    console.error('创建角色失败:', error);
    res.status(500).json({
      success: false,
      message: '创建角色失败'
    });
  }
});

// 更新角色
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { role_name, role_code, role_desc, status, sort_order } = req.body;
    
    // 检查角色是否存在
    const existCheck = await query('SELECT role_id FROM roles WHERE role_id = ?', [id]);
    if (existCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    // 检查角色名称和编码是否被其他角色使用
    if (role_name || role_code) {
      let checkSql = 'SELECT role_id FROM roles WHERE role_id != ? AND (';
      let checkParams = [id];
      let checkConditions = [];
      
      if (role_name) {
        checkConditions.push('role_name = ?');
        checkParams.push(role_name);
      }
      
      if (role_code) {
        checkConditions.push('role_code = ?');
        checkParams.push(role_code);
      }
      
      checkSql += checkConditions.join(' OR ') + ')';
      
      const nameCodeCheck = await query(checkSql, checkParams);
      if (nameCodeCheck.length > 0) {
        return res.status(400).json({
          success: false,
          message: '角色名称或角色编码已被使用'
        });
      }
    }
    
    let updateFields = [];
    let updateValues = [];
    
    if (role_name !== undefined) {
      updateFields.push('role_name = ?');
      updateValues.push(role_name);
    }
    if (role_code !== undefined) {
      updateFields.push('role_code = ?');
      updateValues.push(role_code);
    }
    if (role_desc !== undefined) {
      updateFields.push('role_desc = ?');
      updateValues.push(role_desc);
    }
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }
    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateValues.push(sort_order);
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有要更新的字段'
      });
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    const updateSql = `UPDATE roles SET ${updateFields.join(', ')} WHERE role_id = ?`;
    await query(updateSql, updateValues);
    
    res.json({
      success: true,
      message: '更新角色成功'
    });
  } catch (error) {
    console.error('更新角色失败:', error);
    res.status(500).json({
      success: false,
      message: '更新角色失败'
    });
  }
});

// 删除角色
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查是否为系统预设角色
    if (id === '1' || id === '2') {
      return res.status(400).json({
        success: false,
        message: '不能删除系统预设角色'
      });
    }
    
    const result = await query('DELETE FROM roles WHERE role_id = ?', [id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    res.json({
      success: true,
      message: '删除角色成功'
    });
  } catch (error) {
    console.error('删除角色失败:', error);
    res.status(500).json({
      success: false,
      message: '删除角色失败'
    });
  }
});

// 获取角色权限
router.get('/:id/permissions', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查角色是否存在
    const roleCheck = await query('SELECT role_id FROM roles WHERE role_id = ?', [id]);
    if (roleCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    // 获取角色已有权限
    const permissionSql = `
      SELECT permission_id FROM role_permissions WHERE role_id = ?
    `;
    
    const permissions = await query(permissionSql, [id]);
    const permissionIds = permissions.map(p => p.permission_id);
    
    res.json({
      success: true,
      data: {
        role_id: parseInt(id),
        permissions: permissionIds
      }
    });
  } catch (error) {
    console.error('获取角色权限失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色权限失败'
    });
  }
});

// 更新角色权限
router.put('/:id/permissions', async (req, res) => {
  try {
    const { id } = req.params;
    const { permissions = [] } = req.body;
    
    // 检查角色是否存在
    const roleCheck = await query('SELECT role_id FROM roles WHERE role_id = ?', [id]);
    if (roleCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      });
    }
    
    await transaction(async (connection) => {
      // 删除原有权限
      await connection.execute('DELETE FROM role_permissions WHERE role_id = ?', [id]);
      
      // 添加新权限
      if (permissions.length > 0) {
        const permissionValues = permissions.map(permissionId => [id, permissionId]);
        const insertSql = 'INSERT INTO role_permissions (role_id, permission_id) VALUES ?';
        await connection.query(insertSql, [permissionValues]);
      }
    });
    
    res.json({
      success: true,
      message: '更新角色权限成功'
    });
  } catch (error) {
    console.error('更新角色权限失败:', error);
    res.status(500).json({
      success: false,
      message: '更新角色权限失败'
    });
  }
});

module.exports = router;
