const express = require('express');
const { query } = require('../config/database');
const router = express.Router();

// 获取练习记录列表
router.get('/records', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, user_id = '', start_date = '', end_date = '' } = req.query;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (user_id) {
      whereClause += ' AND uar.user_id = ?';
      params.push(user_id);
    }
    
    if (start_date) {
      whereClause += ' AND DATE(uar.created_at) >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND DATE(uar.created_at) <= ?';
      params.push(end_date);
    }
    
    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM user_answer_records uar
      LEFT JOIN users u ON uar.user_id = u.user_id
      ${whereClause}
    `;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    const listSql = `
      SELECT uar.record_id, uar.user_id, uar.question_id, uar.user_answer, 
             uar.is_correct, uar.answer_time, uar.created_at,
             u.nickname, u.avatar_url,
             q.question_content, q.question_type
      FROM user_answer_records uar
      LEFT JOIN users u ON uar.user_id = u.user_id
      LEFT JOIN questions q ON uar.question_id = q.question_id
      ${whereClause}
      ORDER BY uar.created_at DESC
      LIMIT ? OFFSET ?
    `;
    params.push(parseInt(pageSize), offset);
    
    const list = await query(listSql, params);
    
    res.json({
      success: true,
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取练习记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取练习记录失败'
    });
  }
});

// 获取练习记录详情
router.get('/records/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT uar.record_id, uar.user_id, uar.question_id, uar.user_answer, 
             uar.is_correct, uar.answer_time, uar.created_at,
             u.nickname, u.avatar_url, u.phone, u.email,
             q.question_content, q.question_type, q.question_difficulty,
             q.question_subject, q.question_chapter
      FROM user_answer_records uar
      LEFT JOIN users u ON uar.user_id = u.user_id
      LEFT JOIN questions q ON uar.question_id = q.question_id
      WHERE uar.record_id = ?
    `;
    
    const result = await query(sql, [id]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '练习记录不存在'
      });
    }
    
    res.json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('获取练习记录详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取练习记录详情失败'
    });
  }
});

// 删除练习记录
router.delete('/records/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query('DELETE FROM user_answer_records WHERE record_id = ?', [id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '练习记录不存在'
      });
    }
    
    res.json({
      success: true,
      message: '删除练习记录成功'
    });
  } catch (error) {
    console.error('删除练习记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除练习记录失败'
    });
  }
});

// 批量删除练习记录
router.delete('/records', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的记录ID列表'
      });
    }
    
    const placeholders = ids.map(() => '?').join(',');
    const sql = `DELETE FROM user_answer_records WHERE record_id IN (${placeholders})`;
    
    const result = await query(sql, ids);
    
    res.json({
      success: true,
      message: `成功删除 ${result.affectedRows} 条记录`
    });
  } catch (error) {
    console.error('批量删除练习记录失败:', error);
    res.status(500).json({
      success: false,
      message: '批量删除练习记录失败'
    });
  }
});

// 获取练习统计
router.get('/stats', async (req, res) => {
  try {
    const stats = {};
    
    // 总练习次数
    const totalResult = await query('SELECT COUNT(*) as total FROM user_answer_records');
    stats.total_records = totalResult[0].total;
    
    // 总正确次数
    const correctResult = await query('SELECT COUNT(*) as total FROM user_answer_records WHERE is_correct = 1');
    stats.correct_records = correctResult[0].total;
    
    // 平均正确率
    stats.average_accuracy = stats.total_records > 0 ? Math.round((stats.correct_records / stats.total_records) * 100) : 0;
    
    // 今日练习次数
    const todayResult = await query(`
      SELECT COUNT(*) as total 
      FROM user_answer_records 
      WHERE DATE(created_at) = CURDATE()
    `);
    stats.today_records = todayResult[0].total;
    
    // 活跃用户数（最近7天有练习记录）
    const activeUsersResult = await query(`
      SELECT COUNT(DISTINCT user_id) as total 
      FROM user_answer_records 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    `);
    stats.active_users = activeUsersResult[0].total;
    
    // 最近7天练习趋势
    const trendResult = await query(`
      SELECT DATE(created_at) as date, 
             COUNT(*) as total_count,
             COUNT(CASE WHEN is_correct = 1 THEN 1 END) as correct_count
      FROM user_answer_records
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date
    `);
    stats.practice_trend = trendResult;
    
    // 题目类型分布
    const typeResult = await query(`
      SELECT q.question_type, COUNT(*) as count
      FROM user_answer_records uar
      LEFT JOIN questions q ON uar.question_id = q.question_id
      WHERE q.question_type IS NOT NULL
      GROUP BY q.question_type
    `);
    stats.type_distribution = typeResult;
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取练习统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取练习统计失败'
    });
  }
});

// 获取练习分析
router.get('/analysis', async (req, res) => {
  try {
    const { user_id = '', start_date = '', end_date = '' } = req.query;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (user_id) {
      whereClause += ' AND uar.user_id = ?';
      params.push(user_id);
    }
    
    if (start_date) {
      whereClause += ' AND DATE(uar.created_at) >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND DATE(uar.created_at) <= ?';
      params.push(end_date);
    }
    
    // 按科目统计
    const subjectSql = `
      SELECT q.question_subject, 
             COUNT(*) as total_count,
             COUNT(CASE WHEN uar.is_correct = 1 THEN 1 END) as correct_count,
             ROUND(COUNT(CASE WHEN uar.is_correct = 1 THEN 1 END) / COUNT(*) * 100, 2) as accuracy_rate
      FROM user_answer_records uar
      LEFT JOIN questions q ON uar.question_id = q.question_id
      ${whereClause} AND q.question_subject IS NOT NULL
      GROUP BY q.question_subject
      ORDER BY total_count DESC
    `;
    
    const subjectStats = await query(subjectSql, params);
    
    // 按章节统计
    const chapterSql = `
      SELECT q.question_chapter, 
             COUNT(*) as total_count,
             COUNT(CASE WHEN uar.is_correct = 1 THEN 1 END) as correct_count,
             ROUND(COUNT(CASE WHEN uar.is_correct = 1 THEN 1 END) / COUNT(*) * 100, 2) as accuracy_rate
      FROM user_answer_records uar
      LEFT JOIN questions q ON uar.question_id = q.question_id
      ${whereClause} AND q.question_chapter IS NOT NULL
      GROUP BY q.question_chapter
      ORDER BY total_count DESC
      LIMIT 10
    `;
    
    const chapterStats = await query(chapterSql, params);
    
    // 按难度统计
    const difficultySql = `
      SELECT q.question_difficulty, 
             COUNT(*) as total_count,
             COUNT(CASE WHEN uar.is_correct = 1 THEN 1 END) as correct_count,
             ROUND(COUNT(CASE WHEN uar.is_correct = 1 THEN 1 END) / COUNT(*) * 100, 2) as accuracy_rate
      FROM user_answer_records uar
      LEFT JOIN questions q ON uar.question_id = q.question_id
      ${whereClause} AND q.question_difficulty IS NOT NULL
      GROUP BY q.question_difficulty
      ORDER BY q.question_difficulty
    `;
    
    const difficultyStats = await query(difficultySql, params);
    
    res.json({
      success: true,
      data: {
        subject_stats: subjectStats,
        chapter_stats: chapterStats,
        difficulty_stats: difficultyStats
      }
    });
  } catch (error) {
    console.error('获取练习分析失败:', error);
    res.status(500).json({
      success: false,
      message: '获取练习分析失败'
    });
  }
});

module.exports = router;
