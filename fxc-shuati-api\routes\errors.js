const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Mock 错题数据
const errors = [
  {
    error_id: 1,
    user_id: 1,
    question_id: 2,
    user_answer: 'London',
    error_count: 2,
    last_error_time: '2023-01-02T10:30:00.000Z',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-02T10:30:00.000Z'
  },
  {
    error_id: 2,
    user_id: 1,
    question_id: 3,
    user_answer: '["Paris", "Berlin"]',
    error_count: 1,
    last_error_time: '2023-01-03T15:20:00.000Z',
    created_at: '2023-01-03T15:20:00.000Z',
    updated_at: '2023-01-03T15:20:00.000Z'
  }
];

// Mock 题目数据（简化版）
const questions = [
  {
    question_id: 2,
    question_type: 'fill',
    question_content: "The capital of France is __________.",
    question_difficulty: 2,
    question_subject: "考研英语二",
    question_chapter: "完型填空",
    answer: {
      answer_content: "Paris"
    },
    explanation: {
      explanation_content: "Paris is the capital and most populous city of France."
    }
  },
  {
    question_id: 3,
    question_type: 'multiple',
    question_content: "Which of the following cities are in France?",
    question_difficulty: 3,
    question_subject: "考研英语一",
    question_chapter: "阅读理解",
    options: [
      { option_content: "Paris", is_correct: 1 },
      { option_content: "Lyon", is_correct: 1 },
      { option_content: "Berlin", is_correct: 0 },
      { option_content: "Marseille", is_correct: 1 }
    ],
    explanation: {
      explanation_content: "Paris, Lyon, and Marseille are all cities in France. Berlin is in Germany."
    }
  }
];

// 获取用户错题列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { page = 1, limit = 20, question_type = '', question_subject = '' } = req.query;

    // 获取用户的错题
    let userErrors = errors.filter(e => e.user_id === userId);

    // 获取错题的题目详情
    let errorQuestions = userErrors.map(error => {
      const question = questions.find(q => q.question_id === error.question_id);
      return {
        error_id: error.error_id,
        question_id: error.question_id,
        user_answer: error.user_answer,
        error_count: error.error_count,
        last_error_time: error.last_error_time,
        created_at: error.created_at,
        question: question || null
      };
    }).filter(item => item.question !== null);

    // 根据题目类型筛选
    if (question_type) {
      errorQuestions = errorQuestions.filter(item => 
        item.question && item.question.question_type === question_type
      );
    }

    // 根据科目筛选
    if (question_subject) {
      errorQuestions = errorQuestions.filter(item => 
        item.question && item.question.question_subject === question_subject
      );
    }

    // 按最后错误时间倒序排列
    errorQuestions.sort((a, b) => new Date(b.last_error_time) - new Date(a.last_error_time));

    // 分页处理
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedErrors = errorQuestions.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        errors: paginatedErrors,
        pagination: {
          current_page: pageNum,
          per_page: pageSize,
          total: errorQuestions.length,
          total_pages: Math.ceil(errorQuestions.length / pageSize)
        }
      },
      message: '获取错题列表成功'
    });

  } catch (error) {
    console.error('获取错题列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取错题列表失败'
    });
  }
});

// 添加错题记录
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { question_id, user_answer } = req.body;

    if (!question_id || user_answer === undefined) {
      return res.status(400).json({
        success: false,
        message: '题目ID和用户答案不能为空'
      });
    }

    // 检查题目是否存在
    const question = questions.find(q => q.question_id === parseInt(question_id));
    if (!question) {
      return res.status(404).json({
        success: false,
        message: '题目不存在'
      });
    }

    // 检查是否已经存在错题记录
    const existingErrorIndex = errors.findIndex(e => 
      e.user_id === userId && e.question_id === parseInt(question_id)
    );

    if (existingErrorIndex !== -1) {
      // 更新错题记录
      errors[existingErrorIndex].user_answer = JSON.stringify(user_answer);
      errors[existingErrorIndex].error_count += 1;
      errors[existingErrorIndex].last_error_time = new Date().toISOString();
      errors[existingErrorIndex].updated_at = new Date().toISOString();

      res.json({
        success: true,
        data: errors[existingErrorIndex],
        message: '更新错题记录成功'
      });
    } else {
      // 创建新的错题记录
      const newError = {
        error_id: errors.length + 1,
        user_id: userId,
        question_id: parseInt(question_id),
        user_answer: JSON.stringify(user_answer),
        error_count: 1,
        last_error_time: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      errors.push(newError);

      res.json({
        success: true,
        data: newError,
        message: '添加错题记录成功'
      });
    }

  } catch (error) {
    console.error('添加错题记录错误:', error);
    res.status(500).json({
      success: false,
      message: '添加错题记录失败'
    });
  }
});

// 删除错题记录
router.delete('/:question_id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const questionId = parseInt(req.params.question_id);

    // 查找错题记录
    const errorIndex = errors.findIndex(e => 
      e.user_id === userId && e.question_id === questionId
    );

    if (errorIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '错题记录不存在'
      });
    }

    // 删除错题记录
    errors.splice(errorIndex, 1);

    res.json({
      success: true,
      message: '删除错题记录成功'
    });

  } catch (error) {
    console.error('删除错题记录错误:', error);
    res.status(500).json({
      success: false,
      message: '删除错题记录失败'
    });
  }
});

// 清空错题本
router.delete('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;

    // 删除用户的所有错题记录
    const initialLength = errors.length;
    for (let i = errors.length - 1; i >= 0; i--) {
      if (errors[i].user_id === userId) {
        errors.splice(i, 1);
      }
    }

    const deletedCount = initialLength - errors.length;

    res.json({
      success: true,
      data: { deleted_count: deletedCount },
      message: `清空错题本成功，共删除${deletedCount}条记录`
    });

  } catch (error) {
    console.error('清空错题本错误:', error);
    res.status(500).json({
      success: false,
      message: '清空错题本失败'
    });
  }
});

// 获取错题统计
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.user_id;

    const userErrors = errors.filter(e => e.user_id === userId);

    // 按题目类型统计
    const typeStats = {};
    // 按科目统计
    const subjectStats = {};
    // 按难度统计
    const difficultyStats = {};

    userErrors.forEach(error => {
      const question = questions.find(q => q.question_id === error.question_id);
      if (question) {
        // 题目类型统计
        typeStats[question.question_type] = (typeStats[question.question_type] || 0) + 1;
        
        // 科目统计
        subjectStats[question.question_subject] = (subjectStats[question.question_subject] || 0) + 1;
        
        // 难度统计
        difficultyStats[question.question_difficulty] = (difficultyStats[question.question_difficulty] || 0) + 1;
      }
    });

    const stats = {
      total_errors: userErrors.length,
      total_error_count: userErrors.reduce((sum, error) => sum + error.error_count, 0),
      type_stats: typeStats,
      subject_stats: subjectStats,
      difficulty_stats: difficultyStats,
      recent_errors: userErrors
        .sort((a, b) => new Date(b.last_error_time) - new Date(a.last_error_time))
        .slice(0, 5)
        .map(error => ({
          question_id: error.question_id,
          error_count: error.error_count,
          last_error_time: error.last_error_time
        }))
    };

    res.json({
      success: true,
      data: stats,
      message: '获取错题统计成功'
    });

  } catch (error) {
    console.error('获取错题统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取错题统计失败'
    });
  }
});

module.exports = router;
