{"_from": "icss-utils@^4.1.1", "_id": "icss-utils@4.1.1", "_inBundle": false, "_integrity": "sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA==", "_location": "/icss-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "icss-utils@^4.1.1", "name": "icss-utils", "escapedName": "icss-utils", "rawSpec": "^4.1.1", "saveSpec": null, "fetchSpec": "^4.1.1"}, "_requiredBy": ["/css-loader", "/postcss-modules-local-by-default", "/postcss-modules-values"], "_resolved": "https://registry.npmmirror.com/icss-utils/-/icss-utils-4.1.1.tgz", "_shasum": "21170b53789ee27447c2f47dd683081403f9a467", "_spec": "icss-utils@^4.1.1", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader", "author": {"name": "<PERSON>"}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": 6}}]]}, "bugs": {"url": "https://github.com/css-modules/icss-utils/issues"}, "bundleDependencies": false, "dependencies": {"postcss": "^7.0.14"}, "deprecated": false, "description": "ICSS utils for postcss ast", "devDependencies": {"@babel/cli": "^7.1.0", "@babel/core": "^7.1.0", "@babel/preset-env": "^7.1.0", "babel-eslint": "^10.0.1", "babel-jest": "^24.1.0", "eslint": "^5.14.1", "husky": "^1.3.1", "jest": "^24.1.0", "lint-staged": "^8.1.4", "prettier": "^1.16.4"}, "engines": {"node": ">= 6"}, "eslintConfig": {"parser": "babel-es<PERSON>", "parserOptions": {"sourceType": "module"}, "env": {"es6": true, "jest": true}, "extends": "eslint:recommended"}, "files": ["lib"], "homepage": "https://github.com/css-modules/icss-utils#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "keywords": ["css", "modules", "icss", "postcss"], "license": "ISC", "lint-staged": {"*.js": ["prettier --write", "eslint", "git add"]}, "main": "lib/index.js", "name": "icss-utils", "repository": {"type": "git", "url": "git+https://github.com/css-modules/icss-utils.git"}, "scripts": {"build": "babel --out-dir lib src", "lint": "eslint . --ignore-path .gitignore", "prepublish": "yarn test && yarn run build", "pretest": "npm run lint", "test": "npm run test:only", "test:only": "jest"}, "version": "4.1.1"}