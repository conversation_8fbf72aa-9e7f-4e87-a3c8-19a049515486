{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\AppMain.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXBwTWFpbicsCiAgY29tcHV0ZWQ6IHsKICAgIGtleTogZnVuY3Rpb24ga2V5KCkgewogICAgICByZXR1cm4gdGhpcy4kcm91dGUucGF0aDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "computed", "key", "$route", "path"], "sources": ["src/layout/components/AppMain.vue"], "sourcesContent": ["<template>\n  <section class=\"app-main\">\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <router-view :key=\"key\" />\n    </transition>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'AppMain',\n  computed: {\n    key() {\n      return this.$route.path\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-main {\n  /*50 = navbar  */\n  min-height: calc(100vh - 50px);\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n.fixed-header+.app-main {\n  padding-top: 50px;\n}\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 15px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;AASA;EACAA,IAAA;EACAC,QAAA;IACAC,GAAA,WAAAA,IAAA;MACA,YAAAC,MAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}