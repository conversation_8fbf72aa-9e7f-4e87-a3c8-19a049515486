{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\utils\\validate.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\utils\\validate.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIjsKLyoqCiAqIENyZWF0ZWQgYnkgUGFuSmlhQ2hlbiBvbiAxNi8xMS8xOC4KICovCgovKioKICogQHBhcmFtIHtzdHJpbmd9IHBhdGgKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gaXNFeHRlcm5hbChwYXRoKSB7CiAgcmV0dXJuIC9eKGh0dHBzPzp8bWFpbHRvOnx0ZWw6KS8udGVzdChwYXRoKTsKfQoKLyoqCiAqIEBwYXJhbSB7c3RyaW5nfSBzdHIKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpleHBvcnQgZnVuY3Rpb24gdmFsaWRVc2VybmFtZShzdHIpIHsKICB2YXIgdmFsaWRfbWFwID0gWydhZG1pbicsICdlZGl0b3InXTsKICByZXR1cm4gdmFsaWRfbWFwLmluZGV4T2Yoc3RyLnRyaW0oKSkgPj0gMDsKfQ=="}, {"version": 3, "names": ["isExternal", "path", "test", "validUsername", "str", "valid_map", "indexOf", "trim"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/utils/validate.js"], "sourcesContent": ["/**\n * Created by PanJiaChen on 16/11/18.\n */\n\n/**\n * @param {string} path\n * @returns {Boolean}\n */\nexport function isExternal(path) {\n  return /^(https?:|mailto:|tel:)/.test(path)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUsername(str) {\n  const valid_map = ['admin', 'editor']\n  return valid_map.indexOf(str.trim()) >= 0\n}\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACC,IAAI,CAACD,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACC,OAAO,CAACF,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C", "ignoreList": []}]}