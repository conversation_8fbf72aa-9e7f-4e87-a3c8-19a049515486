{"_from": "https-browserify@^1.0.0", "_id": "https-browserify@1.0.0", "_inBundle": false, "_integrity": "sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg==", "_location": "/https-browserify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "https-browserify@^1.0.0", "name": "https-browserify", "escapedName": "https-browserify", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmmirror.com/https-browserify/-/https-browserify-1.0.0.tgz", "_shasum": "ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73", "_spec": "https-browserify@^1.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\node-libs-browser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/https-browserify/issues"}, "bundleDependencies": false, "deprecated": false, "description": "https module compatability for browserify", "devDependencies": {"standard": "^9.0.2"}, "homepage": "https://github.com/substack/https-browserify", "keywords": ["browser", "browserify", "https"], "license": "MIT", "main": "index.js", "name": "https-browserify", "repository": {"type": "git", "url": "git://github.com/substack/https-browserify.git"}, "scripts": {"test": "standard"}, "version": "1.0.0"}