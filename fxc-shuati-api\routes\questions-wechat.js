const express = require('express');
const router = express.Router();
const { query } = require('../config/database');

// 获取题目列表（小程序版本）
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      pageSize = 10, 
      category_id, 
      difficulty, 
      question_type,
      tag_id,
      random = false 
    } = req.query;
    
    const offset = (page - 1) * pageSize;
    let whereClause = 'WHERE q.status = "active"';
    let params = [];
    
    if (category_id) {
      whereClause += ' AND q.category_id = ?';
      params.push(category_id);
    }
    
    if (difficulty) {
      whereClause += ' AND q.difficulty = ?';
      params.push(difficulty);
    }
    
    if (question_type) {
      whereClause += ' AND q.question_type = ?';
      params.push(question_type);
    }
    
    if (tag_id) {
      whereClause += ' AND EXISTS (SELECT 1 FROM question_tags qt WHERE qt.question_id = q.question_id AND qt.tag_id = ?)';
      params.push(tag_id);
    }
    
    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM questions q ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    let orderBy = random === 'true' ? 'ORDER BY RAND()' : 'ORDER BY q.sort_order ASC, q.created_at DESC';
    const listSql = `
      SELECT q.question_id, q.question_content, q.question_type, q.difficulty, 
             q.options, q.correct_answer, q.explanation, q.score,
             c.category_name
      FROM questions q
      LEFT JOIN categories c ON q.category_id = c.category_id
      ${whereClause}
      ${orderBy}
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(pageSize), offset);
    const list = await query(listSql, params);
    
    // 处理选项数据
    const processedList = list.map(item => ({
      ...item,
      options: item.options ? JSON.parse(item.options) : []
    }));
    
    res.json({
      success: true,
      data: {
        list: processedList,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取题目列表失败:', error);
    
    // 返回模拟数据
    const mockQuestions = [
      {
        question_id: 1,
        question_content: '马克思主义哲学的基本问题是（）',
        question_type: 'single_choice',
        difficulty: 'medium',
        options: [
          { content: '物质和意识的关系问题' },
          { content: '理论和实践的关系问题' },
          { content: '个人和社会的关系问题' },
          { content: '自由和必然的关系问题' }
        ],
        correct_answer: 'A',
        explanation: '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。',
        score: 2,
        category_name: '物质概念'
      },
      {
        question_id: 2,
        question_content: '物质的唯一特性是（）',
        question_type: 'single_choice',
        difficulty: 'easy',
        options: [
          { content: '运动性' },
          { content: '客观实在性' },
          { content: '可知性' },
          { content: '绝对性' }
        ],
        correct_answer: 'B',
        explanation: '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在。',
        score: 2,
        category_name: '物质概念'
      },
      {
        question_id: 3,
        question_content: '意识的本质是（）',
        question_type: 'multiple_choice',
        difficulty: 'medium',
        options: [
          { content: '意识是人脑的机能' },
          { content: '意识是客观存在的反映' },
          { content: '意识是社会的产物' },
          { content: '意识具有主观能动性' }
        ],
        correct_answer: 'A,B,C,D',
        explanation: '意识的本质包括：意识是人脑的机能、是客观存在的反映、是社会的产物、具有主观能动性。',
        score: 2,
        category_name: '意识本质'
      },
      {
        question_id: 4,
        question_content: '马克思主义哲学认为，世界的真正统一性在于它的物质性。',
        question_type: 'true_false',
        difficulty: 'easy',
        options: [],
        correct_answer: 'true',
        explanation: '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。',
        score: 2,
        category_name: '物质概念'
      },
      {
        question_id: 5,
        question_content: '矛盾的基本属性是_____和_____。',
        question_type: 'fill_blank',
        difficulty: 'easy',
        options: [],
        correct_answer: '同一性；斗争性',
        explanation: '矛盾的基本属性是同一性和斗争性。',
        score: 2,
        category_name: '矛盾规律'
      }
    ];
    
    res.json({
      success: true,
      data: {
        list: mockQuestions,
        total: mockQuestions.length,
        page: parseInt(req.query.page || 1),
        pageSize: parseInt(req.query.pageSize || 10)
      }
    });
  }
});

// 获取题目详情
router.get('/:id', async (req, res) => {
  try {
    const questionId = req.params.id;
    
    const questionSql = `
      SELECT q.*, c.category_name,
             GROUP_CONCAT(DISTINCT t.tag_name) as tags
      FROM questions q
      LEFT JOIN categories c ON q.category_id = c.category_id
      LEFT JOIN question_tags qt ON q.question_id = qt.question_id
      LEFT JOIN tags t ON qt.tag_id = t.tag_id
      WHERE q.question_id = ? AND q.status = 'active'
      GROUP BY q.question_id
    `;
    
    const result = await query(questionSql, [questionId]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '题目不存在'
      });
    }
    
    const question = {
      ...result[0],
      options: result[0].options ? JSON.parse(result[0].options) : [],
      tags: result[0].tags ? result[0].tags.split(',') : []
    };
    
    res.json({
      success: true,
      data: question
    });
  } catch (error) {
    console.error('获取题目详情失败:', error);
    
    // 返回模拟数据
    res.json({
      success: true,
      data: {
        question_id: parseInt(req.params.id),
        question_content: '马克思主义哲学的基本问题是（）',
        question_type: 'single_choice',
        difficulty: 'medium',
        options: [
          { content: '物质和意识的关系问题' },
          { content: '理论和实践的关系问题' },
          { content: '个人和社会的关系问题' },
          { content: '自由和必然的关系问题' }
        ],
        correct_answer: 'A',
        explanation: '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。',
        score: 2,
        category_name: '物质概念',
        tags: ['马克思主义基本原理', '哲学原理', '高频考点']
      }
    });
  }
});

// 提交答案
router.post('/:id/answer', async (req, res) => {
  try {
    const questionId = req.params.id;
    const userId = req.user?.userId || 1;
    const { answer, time_spent = 0 } = req.body;
    
    // 获取题目信息
    const question = await query(
      'SELECT correct_answer, score FROM questions WHERE question_id = ?',
      [questionId]
    );
    
    if (question.length === 0) {
      return res.status(404).json({
        success: false,
        message: '题目不存在'
      });
    }
    
    const correctAnswer = question[0].correct_answer;
    const isCorrect = answer === correctAnswer;
    
    // 记录答题记录
    await query(
      `INSERT INTO user_answers (user_id, question_id, user_answer, correct_answer, is_correct, time_spent, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, NOW())
       ON DUPLICATE KEY UPDATE 
       user_answer = VALUES(user_answer), 
       is_correct = VALUES(is_correct), 
       time_spent = VALUES(time_spent), 
       updated_at = NOW()`,
      [userId, questionId, answer, correctAnswer, isCorrect, time_spent]
    );
    
    // 如果答错了，添加到错题本
    if (!isCorrect) {
      await query(
        `INSERT IGNORE INTO user_errors (user_id, question_id, created_at) VALUES (?, ?, NOW())`,
        [userId, questionId]
      );
    } else {
      // 如果答对了，从错题本中移除
      await query(
        'DELETE FROM user_errors WHERE user_id = ? AND question_id = ?',
        [userId, questionId]
      );
    }
    
    res.json({
      success: true,
      data: {
        isCorrect,
        correctAnswer,
        score: isCorrect ? question[0].score : 0
      }
    });
  } catch (error) {
    console.error('提交答案失败:', error);
    
    // 返回模拟结果
    const mockCorrectAnswer = 'A';
    const userAnswer = req.body.answer;
    const isCorrect = userAnswer === mockCorrectAnswer;
    
    res.json({
      success: true,
      data: {
        isCorrect,
        correctAnswer: mockCorrectAnswer,
        score: isCorrect ? 2 : 0
      }
    });
  }
});

module.exports = router;
