{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\learning\\errors.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\learning\\errors.vue", "mtime": 1752572240868}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRXJyb3JzTGlzdCcsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7fTsKICB9Cn07"}, {"version": 3, "names": ["name", "data"], "sources": ["src/views/learning/errors.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"content-card\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>错题管理</span>\n      </div>\n      <div class=\"placeholder-body\">\n        <el-alert\n          title=\"功能开发中\"\n          type=\"info\"\n          description=\"错题管理功能正在开发中，敬请期待...\"\n          show-icon\n          :closable=\"false\"\n        />\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ErrorsList',\n  data() {\n    return {}\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .content-card {\n    margin-bottom: 20px;\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .placeholder-body {\n      padding: 40px 0;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 300px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}