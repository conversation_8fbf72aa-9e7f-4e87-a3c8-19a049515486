const mysql = require('mysql2/promise');
require('dotenv').config();

async function testDatabase() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '*************',
      user: process.env.DB_USER || 'fxc-shuati',
      password: process.env.DB_PASSWORD || 'haf6LeMjHLKefKs7',
      database: process.env.DB_NAME || 'fxc-shuati',
      port: process.env.DB_PORT || 3306
    });

    console.log('数据库连接成功');

    // 检查数据库是否存在
    const [databases] = await connection.query('SHOW DATABASES');
    console.log('可用数据库:', databases.map(db => db.Database));
    
    // 检查当前使用的数据库
    const [currentDb] = await connection.query('SELECT DATABASE() as current_db');
    console.log('当前数据库:', currentDb[0].current_db);
    
    // 检查表是否存在
    const [tables] = await connection.query('SHOW TABLES');
    console.log('数据库中的表:', tables);
    
    // 如果admins表存在，查询管理员数据
    if (tables.some(table => Object.values(table)[0] === 'admins')) {
      const [admins] = await connection.query('SELECT username, real_name FROM admins');
      console.log('管理员列表:', admins);
    } else {
      console.log('admins表不存在');
    }

  } catch (error) {
    console.error('数据库测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testDatabase();
