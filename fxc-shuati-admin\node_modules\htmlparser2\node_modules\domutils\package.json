{"_from": "domutils@^2.5.2", "_id": "domutils@2.8.0", "_inBundle": false, "_integrity": "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==", "_location": "/htmlparser2/domutils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "domutils@^2.5.2", "name": "domutils", "escapedName": "domutils", "rawSpec": "^2.5.2", "saveSpec": null, "fetchSpec": "^2.5.2"}, "_requiredBy": ["/htmlparser2"], "_resolved": "https://registry.npmmirror.com/domutils/-/domutils-2.8.0.tgz", "_shasum": "4437def5db6e2d1f5d6ee859bd95ca7d02048135", "_spec": "domutils@^2.5.2", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\htmlparser2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/domutils/issues"}, "bundleDependencies": false, "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "deprecated": false, "description": "Utilities for working with htmlparser2's dom", "devDependencies": {"@types/jest": "^27.0.1", "@types/node": "^16.7.2", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-jsdoc": "^36.0.8", "htmlparser2": "~7.0.0", "jest": "^27.1.0", "prettier": "^2.0.5", "ts-jest": "^27.0.5", "typedoc": "^0.21.6", "typescript": "^4.4.2"}, "files": ["lib/**/*"], "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}, "homepage": "https://github.com/fb55/domutils#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "keywords": ["dom", "htmlparser2"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "name": "domutils", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/domutils.git"}, "scripts": {"build": "tsc", "build:docs": "typedoc --hideGenerator --exclude \"**/*+(index|.spec).ts\" src", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "2.8.0"}