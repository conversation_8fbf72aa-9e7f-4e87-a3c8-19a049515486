{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue", "mtime": 1752627989997}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["waves", "parseTime", "Pagination", "name", "components", "directives", "filters", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "status", "parentOptions", "temp", "menu_id", "undefined", "parent_id", "menu_name", "menu_path", "component", "menu_icon", "sort_order", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "computed", "treeData", "tree", "map", "for<PERSON>ach", "item", "_objectSpread", "children", "push", "created", "getList", "methods", "_this", "setTimeout", "parent_name", "length", "filter", "handleFilter", "resetTemp", "handleCreate", "_this2", "$nextTick", "$refs", "clearValidate", "createData", "_this3", "validate", "valid", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this4", "Object", "assign", "updateData", "_this5", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "then"], "sources": ["src/views/system/menus.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索菜单名称、路径\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加菜单\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"menu_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"menu_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"菜单名称\" prop=\"menu_name\" min-width=\"150\" />\n      <el-table-column label=\"菜单路径\" prop=\"menu_path\" min-width=\"180\" />\n      <el-table-column label=\"菜单图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.menu_icon\" :class=\"row.menu_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"组件路径\" prop=\"component\" min-width=\"180\" />\n      <el-table-column label=\"父菜单\" prop=\"parent_name\" min-width=\"120\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父菜单\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父菜单\" style=\"width: 100%\">\n            <el-option label=\"顶级菜单\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.menu_id\"\n              :label=\"item.menu_name\"\n              :value=\"item.menu_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"菜单名称\" prop=\"menu_name\">\n          <el-input v-model=\"temp.menu_name\" />\n        </el-form-item>\n        <el-form-item label=\"菜单路径\" prop=\"menu_path\">\n          <el-input v-model=\"temp.menu_path\" />\n        </el-form-item>\n        <el-form-item label=\"组件路径\" prop=\"component\">\n          <el-input v-model=\"temp.component\" />\n        </el-form-item>\n        <el-form-item label=\"菜单图标\" prop=\"menu_icon\">\n          <el-input v-model=\"temp.menu_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'MenuManagement',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑菜单',\n        create: '添加菜单'\n      },\n      rules: {\n        menu_name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],\n        menu_path: [{ required: true, message: '菜单路径不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  computed: {\n    treeData() {\n      // 将平铺数据转换为树形结构\n      const tree = []\n      const map = {}\n\n      // 先创建所有节点的映射\n      this.list.forEach(item => {\n        map[item.menu_id] = { ...item, children: [] }\n      })\n\n      // 构建树形结构\n      this.list.forEach(item => {\n        if (item.parent_id === 0) {\n          // 顶级菜单\n          tree.push(map[item.menu_id])\n        } else {\n          // 子菜单\n          if (map[item.parent_id]) {\n            map[item.parent_id].children.push(map[item.menu_id])\n          }\n        }\n      })\n\n      return tree\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟数据，实际项目中应该调用API\n      setTimeout(() => {\n        this.list = [\n          // 首页\n          {\n            menu_id: 1,\n            menu_name: '首页',\n            menu_path: '/dashboard',\n            component: 'Layout',\n            menu_icon: 'dashboard',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 2,\n            menu_name: '首页',\n            menu_path: '/dashboard/index',\n            component: 'dashboard/index',\n            menu_icon: 'dashboard',\n            parent_id: 1,\n            parent_name: '首页',\n            status: 'active',\n            sort_order: 1\n          },\n          // 系统管理\n          {\n            menu_id: 10,\n            menu_name: '系统管理',\n            menu_path: '/system',\n            component: 'Layout',\n            menu_icon: 'el-icon-setting',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 11,\n            menu_name: '用户管理',\n            menu_path: '/system/users',\n            component: 'users/list',\n            menu_icon: 'el-icon-user-solid',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 12,\n            menu_name: '角色管理',\n            menu_path: '/system/roles',\n            component: 'users/roles',\n            menu_icon: 'el-icon-s-custom',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 13,\n            menu_name: '权限管理',\n            menu_path: '/system/permissions',\n            component: 'users/permissions',\n            menu_icon: 'el-icon-key',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 14,\n            menu_name: '菜单管理',\n            menu_path: '/system/menus',\n            component: 'system/menus',\n            menu_icon: 'el-icon-menu',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 题库管理\n          {\n            menu_id: 20,\n            menu_name: '题库管理',\n            menu_path: '/questions',\n            component: 'Layout',\n            menu_icon: 'el-icon-document',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 21,\n            menu_name: '题目分类',\n            menu_path: '/questions/categories',\n            component: 'questions/categories',\n            menu_icon: 'el-icon-folder',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 22,\n            menu_name: '题目列表',\n            menu_path: '/questions/list',\n            component: 'questions/list',\n            menu_icon: 'el-icon-document-copy',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 23,\n            menu_name: '添加题目',\n            menu_path: '/questions/add',\n            component: 'questions/add',\n            menu_icon: 'el-icon-plus',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 24,\n            menu_name: '批量导入',\n            menu_path: '/questions/upload',\n            component: 'questions/upload',\n            menu_icon: 'el-icon-upload',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 学习数据\n          {\n            menu_id: 30,\n            menu_name: '学习数据',\n            menu_path: '/learning',\n            component: 'Layout',\n            menu_icon: 'el-icon-data-analysis',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 4\n          },\n          {\n            menu_id: 31,\n            menu_name: '收藏管理',\n            menu_path: '/learning/favorites',\n            component: 'learning/favorites',\n            menu_icon: 'el-icon-star-on',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 32,\n            menu_name: '错题管理',\n            menu_path: '/learning/errors',\n            component: 'learning/errors',\n            menu_icon: 'el-icon-warning',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 33,\n            menu_name: '练习记录',\n            menu_path: '/learning/records',\n            component: 'learning/records',\n            menu_icon: 'el-icon-tickets',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 3\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n        \n        // 更新父菜单选项\n        this.parentOptions = this.list.filter(item => item.parent_id === 0)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该菜单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA,OAAAA,KAAA;AACA,SAAAC,SAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,OAAA;IACAL,SAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,aAAA;MACAC,IAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAT,MAAA;QACAU,UAAA;MACA;MACAC,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAV,SAAA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAZ,SAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,QAAA,WAAAA,SAAA;MACA;MACA,IAAAC,IAAA;MACA,IAAAC,GAAA;;MAEA;MACA,KAAA9B,IAAA,CAAA+B,OAAA,WAAAC,IAAA;QACAF,GAAA,CAAAE,IAAA,CAAAtB,OAAA,IAAAuB,aAAA,CAAAA,aAAA,KAAAD,IAAA;UAAAE,QAAA;QAAA;MACA;;MAEA;MACA,KAAAlC,IAAA,CAAA+B,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAApB,SAAA;UACA;UACAiB,IAAA,CAAAM,IAAA,CAAAL,GAAA,CAAAE,IAAA,CAAAtB,OAAA;QACA;UACA;UACA,IAAAoB,GAAA,CAAAE,IAAA,CAAApB,SAAA;YACAkB,GAAA,CAAAE,IAAA,CAAApB,SAAA,EAAAsB,QAAA,CAAAC,IAAA,CAAAL,GAAA,CAAAE,IAAA,CAAAtB,OAAA;UACA;QACA;MACA;MAEA,OAAAmB,IAAA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAArC,WAAA;;MAEA;MACAsC,UAAA;QACAD,KAAA,CAAAvC,IAAA;QACA;QACA;UACAU,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA;QACA;QACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA;QACA;QACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA;QACA;QACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACA6B,WAAA;UACAlC,MAAA;UACAU,UAAA;QACA,EACA;QACAsB,KAAA,CAAAtC,KAAA,GAAAsC,KAAA,CAAAvC,IAAA,CAAA0C,MAAA;QACAH,KAAA,CAAArC,WAAA;;QAEA;QACAqC,KAAA,CAAA/B,aAAA,GAAA+B,KAAA,CAAAvC,IAAA,CAAA2C,MAAA,WAAAX,IAAA;UAAA,OAAAA,IAAA,CAAApB,SAAA;QAAA;MACA;IACA;IACAgC,YAAA,WAAAA,aAAA;MACA,KAAAzC,SAAA,CAAAC,IAAA;MACA,KAAAiC,OAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACA,KAAApC,IAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAT,MAAA;QACAU,UAAA;MACA;IACA;IACA6B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAA1B,YAAA;MACA,KAAAD,iBAAA;MACA,KAAA8B,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAG,OAAA;YACAC,KAAA;YACA/B,OAAA;YACAgC,IAAA;YACAC,QAAA;UACA;UACAN,MAAA,CAAAlC,iBAAA;QACA;MACA;IACA;IACAyC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAApD,IAAA,GAAAqD,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAAzC,YAAA;MACA,KAAAD,iBAAA;MACA,KAAA8B,SAAA;QACAa,MAAA,CAAAZ,KAAA,aAAAC,aAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAW,MAAA,CAAAV,OAAA;YACAC,KAAA;YACA/B,OAAA;YACAgC,IAAA;YACAC,QAAA;UACA;UACAO,MAAA,CAAA/C,iBAAA;QACA;MACA;IACA;IACAgD,YAAA,WAAAA,aAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAb,IAAA;MACA,GAAAc,IAAA;QACAJ,MAAA,CAAAZ,OAAA;UACAC,KAAA;UACA/B,OAAA;UACAgC,IAAA;UACAC,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}