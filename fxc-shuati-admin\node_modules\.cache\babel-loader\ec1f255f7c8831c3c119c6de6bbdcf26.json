{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\menus.vue", "mtime": 1752627684846}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["waves", "parseTime", "Pagination", "name", "components", "directives", "filters", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "status", "parentOptions", "temp", "menu_id", "undefined", "parent_id", "menu_name", "menu_path", "component", "menu_icon", "sort_order", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "setTimeout", "parent_name", "length", "filter", "item", "handleFilter", "resetTemp", "handleCreate", "_this2", "$nextTick", "$refs", "clearValidate", "createData", "_this3", "validate", "valid", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this4", "Object", "assign", "updateData", "_this5", "handleDelete", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "then"], "sources": ["src/views/system/menus.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索菜单名称、路径\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加菜单\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"menu_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"menu_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"菜单名称\" prop=\"menu_name\" width=\"150\" />\n      <el-table-column label=\"菜单路径\" prop=\"menu_path\" width=\"180\" />\n      <el-table-column label=\"菜单图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.menu_icon\" :class=\"row.menu_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"组件路径\" prop=\"component\" width=\"180\" />\n      <el-table-column label=\"父菜单\" prop=\"parent_name\" width=\"150\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父菜单\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父菜单\" style=\"width: 100%\">\n            <el-option label=\"顶级菜单\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.menu_id\"\n              :label=\"item.menu_name\"\n              :value=\"item.menu_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"菜单名称\" prop=\"menu_name\">\n          <el-input v-model=\"temp.menu_name\" />\n        </el-form-item>\n        <el-form-item label=\"菜单路径\" prop=\"menu_path\">\n          <el-input v-model=\"temp.menu_path\" />\n        </el-form-item>\n        <el-form-item label=\"组件路径\" prop=\"component\">\n          <el-input v-model=\"temp.component\" />\n        </el-form-item>\n        <el-form-item label=\"菜单图标\" prop=\"menu_icon\">\n          <el-input v-model=\"temp.menu_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'MenuManagement',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑菜单',\n        create: '添加菜单'\n      },\n      rules: {\n        menu_name: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],\n        menu_path: [{ required: true, message: '菜单路径不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      \n      // 模拟数据，实际项目中应该调用API\n      setTimeout(() => {\n        this.list = [\n          // 首页\n          {\n            menu_id: 1,\n            menu_name: '首页',\n            menu_path: '/dashboard',\n            component: 'Layout',\n            menu_icon: 'dashboard',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 2,\n            menu_name: '首页',\n            menu_path: '/dashboard/index',\n            component: 'dashboard/index',\n            menu_icon: 'dashboard',\n            parent_id: 1,\n            parent_name: '首页',\n            status: 'active',\n            sort_order: 1\n          },\n          // 系统管理\n          {\n            menu_id: 10,\n            menu_name: '系统管理',\n            menu_path: '/system',\n            component: 'Layout',\n            menu_icon: 'el-icon-setting',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 11,\n            menu_name: '用户管理',\n            menu_path: '/system/users',\n            component: 'users/list',\n            menu_icon: 'el-icon-user-solid',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 12,\n            menu_name: '角色管理',\n            menu_path: '/system/roles',\n            component: 'users/roles',\n            menu_icon: 'el-icon-s-custom',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 13,\n            menu_name: '权限管理',\n            menu_path: '/system/permissions',\n            component: 'users/permissions',\n            menu_icon: 'el-icon-key',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 14,\n            menu_name: '菜单管理',\n            menu_path: '/system/menus',\n            component: 'system/menus',\n            menu_icon: 'el-icon-menu',\n            parent_id: 10,\n            parent_name: '系统管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 题库管理\n          {\n            menu_id: 20,\n            menu_name: '题库管理',\n            menu_path: '/questions',\n            component: 'Layout',\n            menu_icon: 'el-icon-document',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 21,\n            menu_name: '题目分类',\n            menu_path: '/questions/categories',\n            component: 'questions/categories',\n            menu_icon: 'el-icon-folder',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 22,\n            menu_name: '题目列表',\n            menu_path: '/questions/list',\n            component: 'questions/list',\n            menu_icon: 'el-icon-document-copy',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 23,\n            menu_name: '添加题目',\n            menu_path: '/questions/add',\n            component: 'questions/add',\n            menu_icon: 'el-icon-plus',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 3\n          },\n          {\n            menu_id: 24,\n            menu_name: '批量导入',\n            menu_path: '/questions/upload',\n            component: 'questions/upload',\n            menu_icon: 'el-icon-upload',\n            parent_id: 20,\n            parent_name: '题库管理',\n            status: 'active',\n            sort_order: 4\n          },\n          // 学习数据\n          {\n            menu_id: 30,\n            menu_name: '学习数据',\n            menu_path: '/learning',\n            component: 'Layout',\n            menu_icon: 'el-icon-data-analysis',\n            parent_id: 0,\n            parent_name: '顶级菜单',\n            status: 'active',\n            sort_order: 4\n          },\n          {\n            menu_id: 31,\n            menu_name: '收藏管理',\n            menu_path: '/learning/favorites',\n            component: 'learning/favorites',\n            menu_icon: 'el-icon-star-on',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 1\n          },\n          {\n            menu_id: 32,\n            menu_name: '错题管理',\n            menu_path: '/learning/errors',\n            component: 'learning/errors',\n            menu_icon: 'el-icon-warning',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 2\n          },\n          {\n            menu_id: 33,\n            menu_name: '练习记录',\n            menu_path: '/learning/records',\n            component: 'learning/records',\n            menu_icon: 'el-icon-tickets',\n            parent_id: 30,\n            parent_name: '学习数据',\n            status: 'active',\n            sort_order: 3\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n        \n        // 更新父菜单选项\n        this.parentOptions = this.list.filter(item => item.parent_id === 0)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        menu_id: undefined,\n        parent_id: 0,\n        menu_name: '',\n        menu_path: '',\n        component: '',\n        menu_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该菜单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA,OAAAA,KAAA;AACA,SAAAC,SAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,OAAA;IACAL,SAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,aAAA;MACAC,IAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAT,MAAA;QACAU,UAAA;MACA;MACAC,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAV,SAAA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAZ,SAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA5B,WAAA;;MAEA;MACA6B,UAAA;QACAD,KAAA,CAAA9B,IAAA;QACA;QACA;UACAU,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA;QACA;QACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA;QACA;QACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA;QACA;QACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,GACA;UACAP,OAAA;UACAG,SAAA;UACAC,SAAA;UACAC,SAAA;UACAC,SAAA;UACAJ,SAAA;UACAoB,WAAA;UACAzB,MAAA;UACAU,UAAA;QACA,EACA;QACAa,KAAA,CAAA7B,KAAA,GAAA6B,KAAA,CAAA9B,IAAA,CAAAiC,MAAA;QACAH,KAAA,CAAA5B,WAAA;;QAEA;QACA4B,KAAA,CAAAtB,aAAA,GAAAsB,KAAA,CAAA9B,IAAA,CAAAkC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAvB,SAAA;QAAA;MACA;IACA;IACAwB,YAAA,WAAAA,aAAA;MACA,KAAAjC,SAAA,CAAAC,IAAA;MACA,KAAAwB,OAAA;IACA;IACAS,SAAA,WAAAA,UAAA;MACA,KAAA5B,IAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAT,MAAA;QACAU,UAAA;MACA;IACA;IACAqB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAAlB,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAsB,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAG,OAAA;YACAC,KAAA;YACAvB,OAAA;YACAwB,IAAA;YACAC,QAAA;UACA;UACAN,MAAA,CAAA1B,iBAAA;QACA;MACA;IACA;IACAiC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA5C,IAAA,GAAA6C,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAAjC,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAsB,SAAA;QACAa,MAAA,CAAAZ,KAAA,aAAAC,aAAA;MACA;IACA;IACAc,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAW,MAAA,CAAAV,OAAA;YACAC,KAAA;YACAvB,OAAA;YACAwB,IAAA;YACAC,QAAA;UACA;UACAO,MAAA,CAAAvC,iBAAA;QACA;MACA;IACA;IACAwC,YAAA,WAAAA,aAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAb,IAAA;MACA,GAAAc,IAAA;QACAJ,MAAA,CAAAZ,OAAA;UACAC,KAAA;UACAvB,OAAA;UACAwB,IAAA;UACAC,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}