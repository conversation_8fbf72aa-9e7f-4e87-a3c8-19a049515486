{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\learning\\favorites.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\learning\\favorites.vue", "mtime": 1752572265216}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRmF2b3JpdGVzTGlzdCcsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7fQogIH0KfQo="}, {"version": 3, "sources": ["favorites.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA", "file": "favorites.vue", "sourceRoot": "src/views/learning", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"content-card\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>收藏管理</span>\n      </div>\n      <div class=\"placeholder-body\">\n        <el-alert\n          title=\"功能开发中\"\n          type=\"info\"\n          description=\"收藏管理功能正在开发中，敬请期待...\"\n          show-icon\n          :closable=\"false\"\n        />\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FavoritesList',\n  data() {\n    return {}\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .content-card {\n    margin-bottom: 20px;\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .placeholder-body {\n      padding: 40px 0;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 300px;\n    }\n  }\n}\n</style>\n"]}]}