const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const jwt = require('jsonwebtoken');

// 微信登录
router.post('/login', async (req, res) => {
  try {
    const { code, userInfo } = req.body;
    
    // 这里应该调用微信API验证code，获取openid
    // 为了演示，我们使用模拟数据
    const openid = `mock_openid_${Date.now()}`;
    
    // 查询或创建用户
    let user = await query('SELECT * FROM wechat_users WHERE openid = ?', [openid]);
    
    if (user.length === 0) {
      // 创建新用户
      const insertResult = await query(
        'INSERT INTO wechat_users (openid, nickname, avatar_url, created_at) VALUES (?, ?, ?, NOW())',
        [openid, userInfo?.nickName || '微信用户', userInfo?.avatarUrl || '']
      );
      
      user = await query('SELECT * FROM wechat_users WHERE user_id = ?', [insertResult.insertId]);
    } else {
      // 更新用户信息
      if (userInfo) {
        await query(
          'UPDATE wechat_users SET nickname = ?, avatar_url = ?, last_login_time = NOW() WHERE openid = ?',
          [userInfo.nickName, userInfo.avatarUrl, openid]
        );
      }
    }
    
    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user[0].user_id, 
        openid: user[0].openid 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );
    
    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        userInfo: {
          userId: user[0].user_id,
          openid: user[0].openid,
          nickname: user[0].nickname,
          avatarUrl: user[0].avatar_url
        }
      }
    });
  } catch (error) {
    console.error('微信登录失败:', error);
    
    // 返回模拟登录成功数据
    const mockToken = jwt.sign(
      { 
        userId: 1, 
        openid: 'mock_openid_demo' 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );
    
    res.json({
      success: true,
      message: '登录成功（模拟数据）',
      data: {
        token: mockToken,
        userInfo: {
          userId: 1,
          openid: 'mock_openid_demo',
          nickname: '演示用户',
          avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
        }
      }
    });
  }
});

// 获取用户信息
router.get('/profile', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    
    const user = await query(
      'SELECT user_id, openid, nickname, avatar_url, created_at, last_login_time FROM wechat_users WHERE user_id = ?',
      [userId]
    );
    
    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      data: user[0]
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    
    // 返回模拟数据
    res.json({
      success: true,
      data: {
        user_id: 1,
        openid: 'mock_openid_demo',
        nickname: '演示用户',
        avatar_url: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
        created_at: '2024-01-01 00:00:00',
        last_login_time: '2024-01-20 10:30:00'
      }
    });
  }
});

// 更新用户信息
router.put('/profile', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    const { nickname, avatar_url } = req.body;
    
    await query(
      'UPDATE wechat_users SET nickname = ?, avatar_url = ? WHERE user_id = ?',
      [nickname, avatar_url, userId]
    );
    
    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.json({
      success: true,
      message: '更新成功（模拟）'
    });
  }
});

// 获取用户统计信息
router.get('/stats', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    
    // 获取各种统计数据
    const [
      totalQuestions,
      correctAnswers,
      favoriteCount,
      errorCount,
      practiceCount
    ] = await Promise.all([
      query('SELECT COUNT(*) as count FROM user_answers WHERE user_id = ?', [userId]),
      query('SELECT COUNT(*) as count FROM user_answers WHERE user_id = ? AND is_correct = 1', [userId]),
      query('SELECT COUNT(*) as count FROM user_favorites WHERE user_id = ?', [userId]),
      query('SELECT COUNT(*) as count FROM user_errors WHERE user_id = ?', [userId]),
      query('SELECT COUNT(*) as count FROM practice_records WHERE user_id = ?', [userId])
    ]);
    
    const total = totalQuestions[0].count;
    const correct = correctAnswers[0].count;
    const accuracy = total > 0 ? Math.round((correct / total) * 100) : 0;
    
    res.json({
      success: true,
      data: {
        totalQuestions: total,
        correctAnswers: correct,
        accuracy: accuracy,
        favoriteCount: favoriteCount[0].count,
        errorCount: errorCount[0].count,
        practiceCount: practiceCount[0].count
      }
    });
  } catch (error) {
    console.error('获取用户统计失败:', error);
    
    // 返回模拟统计数据
    res.json({
      success: true,
      data: {
        totalQuestions: 156,
        correctAnswers: 123,
        accuracy: 79,
        favoriteCount: 23,
        errorCount: 33,
        practiceCount: 12
      }
    });
  }
});

module.exports = router;
