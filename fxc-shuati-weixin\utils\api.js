// API请求封装
const app = getApp()

// API基础配置
const API_BASE_URL = 'http://localhost:3000/api'

/**
 * 封装请求方法
 * @param {Object} options 请求配置
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '加载中...',
        mask: true
      })
    }

    // 获取token
    const token = wx.getStorageSync('token') || app.globalData.token

    // 设置请求头
    const header = {
      'Content-Type': 'application/json'
    }

    if (token) {
      header['Authorization'] = `Bearer ${token}`
    }

    wx.request({
      url: API_BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      success: (res) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }

        if (res.statusCode === 200) {
          if (res.data.success) {
            resolve(res.data)
          } else {
            // 显示错误提示
            if (options.showError !== false) {
              wx.showToast({
                title: res.data.message || '请求失败',
                icon: 'none'
              })
            }
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // token过期，清除本地存储
          wx.removeStorageSync('token')
          app.globalData.token = null
          app.globalData.userInfo = null
          
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
          
          // 跳转到登录页面或重新登录
          setTimeout(() => {
            app.wxLogin()
          }, 1500)
          
          reject(res.data)
        } else {
          // 隐藏加载提示
          if (options.showLoading !== false) {
            wx.hideLoading()
          }
          
          if (options.showError !== false) {
            wx.showToast({
              title: '网络请求失败',
              icon: 'none'
            })
          }
          reject(res.data)
        }
      },
      fail: (err) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }
        
        if (options.showError !== false) {
          wx.showToast({
            title: '网络连接失败',
            icon: 'none'
          })
        }
        reject(err)
      }
    })
  })
}

// API接口定义
const api = {
  // 用户相关
  login: (data) => request({
    url: '/wechat/login',
    method: 'POST',
    data,
    loadingText: '登录中...'
  }),

  getUserInfo: () => request({
    url: '/wechat/profile',
    loadingText: '获取用户信息...'
  }),

  updateUserInfo: (data) => request({
    url: '/wechat/profile',
    method: 'PUT',
    data,
    loadingText: '保存中...'
  }),

  getUserStats: () => request({
    url: '/wechat/stats',
    showLoading: false
  }),

  // 题目相关
  getQuestions: (params) => request({
    url: '/wechat/questions',
    data: params,
    loadingText: '加载题目...'
  }),

  getQuestionDetail: (id) => request({
    url: `/wechat/questions/${id}`,
    loadingText: '加载题目详情...'
  }),

  submitAnswer: (id, data) => request({
    url: `/wechat/questions/${id}/answer`,
    method: 'POST',
    data,
    loadingText: '提交答案...'
  }),

  // 分类相关
  getCategories: () => request({
    url: '/wechat/categories',
    loadingText: '加载分类...'
  }),

  getCategoryTree: () => request({
    url: '/wechat/categories/tree',
    showLoading: false
  }),

  // 收藏相关
  getFavorites: (params) => request({
    url: '/wechat/favorites',
    data: params,
    loadingText: '加载收藏...'
  }),

  addFavorite: (data) => request({
    url: '/wechat/favorites',
    method: 'POST',
    data,
    loadingText: '收藏中...'
  }),

  removeFavorite: (questionId) => request({
    url: `/wechat/favorites/${questionId}`,
    method: 'DELETE',
    loadingText: '取消收藏...'
  }),

  checkFavorite: (questionId) => request({
    url: `/wechat/favorites/check/${questionId}`,
    showLoading: false,
    showError: false
  }),

  // 错题相关
  getErrors: (params) => request({
    url: '/wechat/errors',
    data: params,
    loadingText: '加载错题...'
  }),

  addError: (data) => request({
    url: '/wechat/errors',
    method: 'POST',
    data,
    showLoading: false,
    showError: false
  }),

  removeError: (questionId) => request({
    url: `/wechat/errors/${questionId}`,
    method: 'DELETE',
    loadingText: '删除错题...'
  }),

  clearErrors: () => request({
    url: '/wechat/errors',
    method: 'DELETE',
    loadingText: '清空错题本...'
  }),

  getErrorStats: () => request({
    url: '/wechat/errors/stats',
    showLoading: false
  }),

  // 组合题相关
  getComposites: (params) => request({ 
    url: '/composite', 
    data: params,
    loadingText: '加载组合题...'
  }),
  
  getCompositeDetail: (id) => request({ 
    url: `/composite/${id}`,
    loadingText: '加载组合题详情...'
  }),

  // 练习记录相关
  getPracticeRecords: (params) => request({
    url: '/practice/records',
    data: params,
    loadingText: '加载练习记录...'
  }),

  savePracticeRecord: (data) => request({
    url: '/practice/records',
    method: 'POST',
    data,
    loadingText: '保存练习记录...'
  })
}

module.exports = {
  request,
  api
}
