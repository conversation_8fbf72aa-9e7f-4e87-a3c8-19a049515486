{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\login\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\login\\index.vue", "mtime": 1752570299690}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["validUsername", "name", "data", "validateUsername", "rule", "value", "callback", "Error", "validatePassword", "length", "loginForm", "username", "password", "loginRules", "required", "trigger", "validator", "loading", "passwordType", "redirect", "undefined", "watch", "$route", "handler", "route", "query", "immediate", "methods", "showPwd", "_this", "$nextTick", "$refs", "focus", "handleLogin", "_this2", "validate", "valid", "$store", "dispatch", "then", "$router", "push", "path", "catch", "error", "console", "log"], "sources": ["src/views/login/index.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" auto-complete=\"on\" label-position=\"left\">\n\n      <div class=\"title-container\">\n        <h3 class=\"title\">Login Form</h3>\n      </div>\n\n      <el-form-item prop=\"username\">\n        <span class=\"svg-container\">\n          <svg-icon icon-class=\"user\" />\n        </span>\n        <el-input\n          ref=\"username\"\n          v-model=\"loginForm.username\"\n          placeholder=\"Username\"\n          name=\"username\"\n          type=\"text\"\n          tabindex=\"1\"\n          auto-complete=\"on\"\n        />\n      </el-form-item>\n\n      <el-form-item prop=\"password\">\n        <span class=\"svg-container\">\n          <svg-icon icon-class=\"password\" />\n        </span>\n        <el-input\n          :key=\"passwordType\"\n          ref=\"password\"\n          v-model=\"loginForm.password\"\n          :type=\"passwordType\"\n          placeholder=\"Password\"\n          name=\"password\"\n          tabindex=\"2\"\n          auto-complete=\"on\"\n          @keyup.enter.native=\"handleLogin\"\n        />\n        <span class=\"show-pwd\" @click=\"showPwd\">\n          <svg-icon :icon-class=\"passwordType === 'password' ? 'eye' : 'eye-open'\" />\n        </span>\n      </el-form-item>\n\n      <el-button :loading=\"loading\" type=\"primary\" style=\"width:100%;margin-bottom:30px;\" @click.native.prevent=\"handleLogin\">Login</el-button>\n\n      <div class=\"tips\">\n        <span style=\"margin-right:20px;\">username: admin</span>\n        <span> password: any</span>\n      </div>\n\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { validUsername } from '@/utils/validate'\n\nexport default {\n  name: 'Login',\n  data() {\n    const validateUsername = (rule, value, callback) => {\n      if (!validUsername(value)) {\n        callback(new Error('Please enter the correct user name'))\n      } else {\n        callback()\n      }\n    }\n    const validatePassword = (rule, value, callback) => {\n      if (value.length < 6) {\n        callback(new Error('The password can not be less than 6 digits'))\n      } else {\n        callback()\n      }\n    }\n    return {\n      loginForm: {\n        username: 'admin',\n        password: '111111'\n      },\n      loginRules: {\n        username: [{ required: true, trigger: 'blur', validator: validateUsername }],\n        password: [{ required: true, trigger: 'blur', validator: validatePassword }]\n      },\n      loading: false,\n      passwordType: 'password',\n      redirect: undefined\n    }\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        this.redirect = route.query && route.query.redirect\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    showPwd() {\n      if (this.passwordType === 'password') {\n        this.passwordType = ''\n      } else {\n        this.passwordType = 'password'\n      }\n      this.$nextTick(() => {\n        this.$refs.password.focus()\n      })\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          this.$store.dispatch('user/login', this.loginForm).then(() => {\n            // 登录成功后获取用户信息\n            return this.$store.dispatch('user/getInfo')\n          }).then(() => {\n            // 获取用户信息成功后跳转\n            this.$router.push({ path: this.redirect || '/' })\n            this.loading = false\n          }).catch((error) => {\n            console.error('登录或获取用户信息失败:', error)\n            this.loading = false\n          })\n        } else {\n          console.log('error submit!!')\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/* 修复input 背景不协调 和光标变色 */\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\n\n$bg:#283443;\n$light_gray:#fff;\n$cursor: #fff;\n\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\n  .login-container .el-input input {\n    color: $cursor;\n  }\n}\n\n/* reset element-ui css */\n.login-container {\n  .el-input {\n    display: inline-block;\n    height: 47px;\n    width: 85%;\n\n    input {\n      background: transparent;\n      border: 0px;\n      -webkit-appearance: none;\n      border-radius: 0px;\n      padding: 12px 5px 12px 15px;\n      color: $light_gray;\n      height: 47px;\n      caret-color: $cursor;\n\n      &:-webkit-autofill {\n        box-shadow: 0 0 0px 1000px $bg inset !important;\n        -webkit-text-fill-color: $cursor !important;\n      }\n    }\n  }\n\n  .el-form-item {\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    background: rgba(0, 0, 0, 0.1);\n    border-radius: 5px;\n    color: #454545;\n  }\n}\n</style>\n\n<style lang=\"scss\" scoped>\n$bg:#2d3a4b;\n$dark_gray:#889aa4;\n$light_gray:#eee;\n\n.login-container {\n  min-height: 100%;\n  width: 100%;\n  background-color: $bg;\n  overflow: hidden;\n\n  .login-form {\n    position: relative;\n    width: 520px;\n    max-width: 100%;\n    padding: 160px 35px 0;\n    margin: 0 auto;\n    overflow: hidden;\n  }\n\n  .tips {\n    font-size: 14px;\n    color: #fff;\n    margin-bottom: 10px;\n\n    span {\n      &:first-of-type {\n        margin-right: 16px;\n      }\n    }\n  }\n\n  .svg-container {\n    padding: 6px 5px 6px 15px;\n    color: $dark_gray;\n    vertical-align: middle;\n    width: 30px;\n    display: inline-block;\n  }\n\n  .title-container {\n    position: relative;\n\n    .title {\n      font-size: 26px;\n      color: $light_gray;\n      margin: 0px auto 40px auto;\n      text-align: center;\n      font-weight: bold;\n    }\n  }\n\n  .show-pwd {\n    position: absolute;\n    right: 10px;\n    top: 7px;\n    font-size: 16px;\n    color: $dark_gray;\n    cursor: pointer;\n    user-select: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA,SAAAA,aAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,gBAAA,YAAAA,iBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAN,aAAA,CAAAK,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA,IAAAE,gBAAA,YAAAA,iBAAAJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA,CAAAI,MAAA;QACAH,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA;MACAI,SAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,UAAA;QACAF,QAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,SAAA,EAAAb;QAAA;QACAS,QAAA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,SAAA,EAAAR;QAAA;MACA;MACAS,OAAA;MACAC,YAAA;MACAC,QAAA,EAAAC;IACA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,KAAA;QACA,KAAAL,QAAA,GAAAK,KAAA,CAAAC,KAAA,IAAAD,KAAA,CAAAC,KAAA,CAAAN,QAAA;MACA;MACAO,SAAA;IACA;EACA;EACAC,OAAA;IACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,SAAAX,YAAA;QACA,KAAAA,YAAA;MACA;QACA,KAAAA,YAAA;MACA;MACA,KAAAY,SAAA;QACAD,KAAA,CAAAE,KAAA,CAAAnB,QAAA,CAAAoB,KAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,CAAArB,SAAA,CAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAjB,OAAA;UACAiB,MAAA,CAAAG,MAAA,CAAAC,QAAA,eAAAJ,MAAA,CAAAxB,SAAA,EAAA6B,IAAA;YACA;YACA,OAAAL,MAAA,CAAAG,MAAA,CAAAC,QAAA;UACA,GAAAC,IAAA;YACA;YACAL,MAAA,CAAAM,OAAA,CAAAC,IAAA;cAAAC,IAAA,EAAAR,MAAA,CAAAf,QAAA;YAAA;YACAe,MAAA,CAAAjB,OAAA;UACA,GAAA0B,KAAA,WAAAC,KAAA;YACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;YACAV,MAAA,CAAAjB,OAAA;UACA;QACA;UACA4B,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}