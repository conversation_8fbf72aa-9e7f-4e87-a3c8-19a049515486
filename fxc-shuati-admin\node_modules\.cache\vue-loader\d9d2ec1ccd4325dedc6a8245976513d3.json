{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\index.vue?vue&type=style&index=0&id=13877386&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\index.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJ+QC9zdHlsZXMvbWl4aW4uc2NzcyI7CkBpbXBvcnQgIn5AL3N0eWxlcy92YXJpYWJsZXMuc2NzcyI7CgouYXBwLXdyYXBwZXIgewogIEBpbmNsdWRlIGNsZWFyZml4OwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBoZWlnaHQ6IDEwMCU7CiAgd2lkdGg6IDEwMCU7CiAgJi5tb2JpbGUub3BlblNpZGViYXJ7CiAgICBwb3NpdGlvbjogZml4ZWQ7CiAgICB0b3A6IDA7CiAgfQp9Ci5kcmF3ZXItYmcgewogIGJhY2tncm91bmQ6ICMwMDA7CiAgb3BhY2l0eTogMC4zOwogIHdpZHRoOiAxMDAlOwogIHRvcDogMDsKICBoZWlnaHQ6IDEwMCU7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHotaW5kZXg6IDk5OTsKfQoKLmZpeGVkLWhlYWRlciB7CiAgcG9zaXRpb246IGZpeGVkOwogIHRvcDogMDsKICByaWdodDogMDsKICB6LWluZGV4OiA5OwogIHdpZHRoOiBjYWxjKDEwMCUgLSAjeyRzaWRlQmFyV2lkdGh9KTsKICB0cmFuc2l0aW9uOiB3aWR0aCAwLjI4czsKfQoKLmhpZGVTaWRlYmFyIC5maXhlZC1oZWFkZXIgewogIHdpZHRoOiBjYWxjKDEwMCUgLSA1NHB4KQp9CgoubW9iaWxlIC5maXhlZC1oZWFkZXIgewogIHdpZHRoOiAxMDAlOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\n    <sidebar class=\"sidebar-container\" />\n    <div class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar />\n      </div>\n      <app-main />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Navbar, Sidebar, AppMain } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\n\nexport default {\n  name: 'Layout',\n  components: {\n    Navbar,\n    Sidebar,\n    AppMain\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    sidebar() {\n      return this.$store.state.app.sidebar\n    },\n    device() {\n      return this.$store.state.app.device\n    },\n    fixedHeader() {\n      return this.$store.state.settings.fixedHeader\n    },\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/styles/mixin.scss\";\n  @import \"~@/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n    &.mobile.openSidebar{\n      position: fixed;\n      top: 0;\n    }\n  }\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$sideBarWidth});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px)\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"]}]}