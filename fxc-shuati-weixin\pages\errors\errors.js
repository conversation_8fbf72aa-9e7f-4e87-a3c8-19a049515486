// errors.js
const { api } = require('../../utils/api.js')
const { formatTime, getQuestionTypeText, showError, showSuccess } = require('../../utils/util.js')

Page({
  data: {
    errorStats: {
      total_errors: 0,
      accuracy_rate: 0,
      recent_errors: 0,
      review_count: 0
    },
    selectedSubject: '',
    subjectOptions: [
      { label: '全部', value: '' },
      { label: '考研英语一', value: '考研英语一' },
      { label: '考研英语二', value: '考研英语二' },
      { label: '阅读理解', value: '阅读理解' },
      { label: '完型填空', value: '完型填空' },
      { label: '翻译', value: '翻译' },
      { label: '写作', value: '写作' }
    ],
    errorList: [],
    suggestions: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    console.log('错题本页面加载')
    this.loadErrorStats()
    this.loadErrorList()
    this.generateSuggestions()
  },

  onShow() {
    console.log('错题本页面显示')
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 刷新数据
  refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      errorList: []
    })
    this.loadErrorStats()
    this.loadErrorList()
  },

  // 加载错题统计
  loadErrorStats() {
    // 使用mock数据
    this.setData({
      errorStats: {
        total_errors: 15,
        accuracy_rate: 68,
        recent_errors: 5,
        review_count: 8
      }
    })

    // 实际API调用示例
    // api.getErrorStats().then(res => {
    //   this.setData({
    //     errorStats: res.data
    //   })
    // }).catch(err => {
    //   console.error('获取错题统计失败', err)
    // })
  },

  // 加载错题列表
  loadErrorList() {
    if (this.data.loading) return

    this.setData({ loading: true })

    // 使用mock数据
    const mockData = [
      {
        error_id: 1,
        question_id: 2,
        user_answer: 'London',
        correct_answer: 'Paris',
        error_count: 2,
        last_error_time: '2023-12-01T10:30:00.000Z',
        question: {
          question_id: 2,
          question_type: 'fill',
          question_content: 'The capital of France is __________.',
          question_subject: '考研英语二',
          question_chapter: '完型填空'
        }
      },
      {
        error_id: 2,
        question_id: 3,
        user_answer: 'Paris, Berlin',
        correct_answer: 'Paris, Lyon, Marseille',
        error_count: 1,
        last_error_time: '2023-12-01T15:20:00.000Z',
        question: {
          question_id: 3,
          question_type: 'multiple',
          question_content: 'Which of the following cities are in France?',
          question_subject: '考研英语一',
          question_chapter: '阅读理解'
        }
      }
    ]

    // 处理数据
    const processedData = mockData.map(item => ({
      ...item,
      timeText: formatTime(item.last_error_time, 'MM-DD HH:mm'),
      question: {
        ...item.question,
        typeText: getQuestionTypeText(item.question.question_type)
      }
    }))

    setTimeout(() => {
      const { page, errorList } = this.data
      const newList = page === 1 ? processedData : [...errorList, ...processedData]
      
      this.setData({
        errorList: newList,
        loading: false,
        hasMore: processedData.length === this.data.pageSize
      })

      if (page === 1) {
        wx.stopPullDownRefresh()
      }
    }, 500)

    // 实际API调用示例
    // const params = {
    //   page: this.data.page,
    //   pageSize: this.data.pageSize,
    //   subject: this.data.selectedSubject
    // }
    
    // api.getErrors(params).then(res => {
    //   const processedData = res.data.map(item => ({
    //     ...item,
    //     timeText: formatTime(item.last_error_time, 'MM-DD HH:mm'),
    //     question: {
    //       ...item.question,
    //       typeText: getQuestionTypeText(item.question.question_type)
    //     }
    //   }))
    
    //   const { page, errorList } = this.data
    //   const newList = page === 1 ? processedData : [...errorList, ...processedData]
    
    //   this.setData({
    //     errorList: newList,
    //     loading: false,
    //     hasMore: processedData.length === this.data.pageSize
    //   })
    
    //   if (page === 1) {
    //     wx.stopPullDownRefresh()
    //   }
    // }).catch(err => {
    //   console.error('获取错题列表失败', err)
    //   this.setData({ loading: false })
    //   if (page === 1) {
    //     wx.stopPullDownRefresh()
    //   }
    // })
  },

  // 生成学习建议
  generateSuggestions() {
    const { errorStats } = this.data
    const suggestions = []

    if (errorStats.total_errors > 10) {
      suggestions.push('错题较多，建议重点复习基础知识')
    }

    if (errorStats.accuracy_rate < 70) {
      suggestions.push('正确率偏低，建议放慢做题速度，仔细审题')
    }

    if (errorStats.recent_errors > 3) {
      suggestions.push('最近错题增多，注意调整学习状态')
    }

    suggestions.push('定期复习错题，避免重复犯错')
    suggestions.push('分析错误原因，总结解题技巧')

    this.setData({
      suggestions
    })
  },

  // 选择科目筛选
  selectSubject(e) {
    const value = e.currentTarget.dataset.value
    this.setData({
      selectedSubject: value
    })
    this.refreshData()
  },

  // 重做题目
  reviewQuestion(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages/question/question?questionId=${item.question_id}&isReview=true&title=错题复习`
    })
  },

  // 删除错题
  deleteError(e) {
    const item = e.currentTarget.dataset.item
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这道错题吗？',
      success: (res) => {
        if (res.confirm) {
          // 从列表中移除
          const errorList = this.data.errorList.filter(error => error.error_id !== item.error_id)
          this.setData({
            errorList,
            'errorStats.total_errors': this.data.errorStats.total_errors - 1
          })
          
          showSuccess('删除成功')
          
          // 实际API调用
          // api.removeError(item.question_id).then(() => {
          //   showSuccess('删除成功')
          // }).catch(err => {
          //   console.error('删除错题失败', err)
          //   // 恢复列表
          //   this.loadErrorList()
          // })
        }
      }
    })
  },

  // 清空所有错题
  clearAllErrors() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有错题吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            errorList: [],
            errorStats: {
              total_errors: 0,
              accuracy_rate: 0,
              recent_errors: 0,
              review_count: 0
            }
          })
          showSuccess('清空成功')
        }
      }
    })
  },

  // 重做所有错题
  reviewAllErrors() {
    const { errorList } = this.data
    if (errorList.length === 0) {
      showError('暂无错题')
      return
    }

    const questionIds = errorList.map(item => item.question_id).join(',')
    wx.navigateTo({
      url: `/pages/question/question?questionIds=${questionIds}&isReviewAll=true&title=错题复习`
    })
  },

  // 跳转到练习页面
  goToPractice() {
    wx.switchTab({
      url: '/pages/practice/practice'
    })
  },

  // 加载更多
  loadMore() {
    this.setData({
      page: this.data.page + 1
    })
    this.loadErrorList()
  }
})
