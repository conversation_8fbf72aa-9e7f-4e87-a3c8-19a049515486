import request from '@/utils/request'

// 收藏管理API
export function getFavoritesList(params) {
  return request({
    url: '/api/favorites',
    method: 'get',
    params
  })
}

export function deleteFavorite(id) {
  return request({
    url: `/api/favorites/${id}`,
    method: 'delete'
  })
}

export function batchDeleteFavorites(ids) {
  return request({
    url: '/api/favorites/batch',
    method: 'delete',
    data: { ids }
  })
}

export function getFavoritesStats() {
  return request({
    url: '/api/favorites/stats',
    method: 'get'
  })
}

// 错题管理API
export function getErrorsList(params) {
  return request({
    url: '/api/errors',
    method: 'get',
    params
  })
}

export function deleteError(id) {
  return request({
    url: `/api/errors/${id}`,
    method: 'delete'
  })
}

export function batchDeleteErrors(ids) {
  return request({
    url: '/api/errors/batch',
    method: 'delete',
    data: { ids }
  })
}

export function getErrorsStats() {
  return request({
    url: '/api/errors/stats',
    method: 'get'
  })
}

export function getErrorAnalysis(params) {
  return request({
    url: '/api/errors/analysis',
    method: 'get',
    params
  })
}

// 练习记录API
export function getPracticeRecords(params) {
  return request({
    url: '/api/practice/records',
    method: 'get',
    params
  })
}

export function getPracticeRecordDetail(id) {
  return request({
    url: `/api/practice/records/${id}`,
    method: 'get'
  })
}

export function deletePracticeRecord(id) {
  return request({
    url: `/api/practice/records/${id}`,
    method: 'delete'
  })
}

export function batchDeletePracticeRecords(ids) {
  return request({
    url: '/api/practice/records/batch',
    method: 'delete',
    data: { ids }
  })
}

export function getPracticeStats() {
  return request({
    url: '/api/practice/stats',
    method: 'get'
  })
}

export function getPracticeAnalysis(params) {
  return request({
    url: '/api/practice/analysis',
    method: 'get',
    params
  })
}

// 学习报告API
export function getLearningReport(params) {
  return request({
    url: '/api/learning/report',
    method: 'get',
    params
  })
}

export function exportLearningReport(params) {
  return request({
    url: '/api/learning/report/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 用户学习数据API
export function getUserLearningData(userId, params) {
  return request({
    url: `/api/users/${userId}/learning`,
    method: 'get',
    params
  })
}

export function getUserPracticeHistory(userId, params) {
  return request({
    url: `/api/users/${userId}/practice-history`,
    method: 'get',
    params
  })
}
