// result.js
const { formatTime } = require('../../utils/util.js')

Page({
  data: {
    result: {
      totalQuestions: 0,
      correctCount: 0,
      wrongCount: 0,
      accuracy: 0,
      totalTime: 0,
      totalTimeText: '',
      averageTime: 0,
      averageTimeText: '',
      wrongQuestions: []
    },
    achievements: [],
    suggestions: [],
    showShare: true
  },

  onLoad(options) {
    console.log('结果页面加载', options)
    this.loadResult(options)
    this.checkAchievements()
    this.generateSuggestions()
  },

  onShareAppMessage() {
    const { result } = this.data
    return {
      title: `我在刷题小程序答对了${result.correctCount}/${result.totalQuestions}题，正确率${result.accuracy}%！`,
      path: '/pages/index/index',
      imageUrl: '' // 可以设置分享图片
    }
  },

  // 加载结果数据
  loadResult(options) {
    const correctCount = parseInt(options.correctCount) || 0
    const totalQuestions = parseInt(options.totalCount) || 0
    const accuracy = parseInt(options.accuracy) || 0
    const wrongCount = totalQuestions - correctCount
    
    // 模拟时间数据
    const totalTime = 1200 // 20分钟
    const averageTime = totalQuestions > 0 ? Math.round(totalTime / totalQuestions) : 0
    
    // 模拟错题数据
    const wrongQuestions = []
    if (wrongCount > 0) {
      for (let i = 0; i < Math.min(wrongCount, 3); i++) {
        wrongQuestions.push({
          questionIndex: i * 2 + 1,
          question: {
            question_content: `这是第${i + 1}道错题的内容...`
          },
          userAnswer: '错误答案',
          correctAnswer: '正确答案'
        })
      }
    }

    const result = {
      totalQuestions,
      correctCount,
      wrongCount,
      accuracy,
      totalTime,
      totalTimeText: this.formatTimeText(totalTime),
      averageTime,
      averageTimeText: this.formatTimeText(averageTime),
      wrongQuestions
    }

    this.setData({
      result,
      resultIcon: this.getResultIcon(accuracy),
      resultTitle: this.getResultTitle(accuracy),
      resultSubtitle: this.getResultSubtitle(accuracy)
    })
  },

  // 格式化时间文本
  formatTimeText(seconds) {
    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    if (minutes > 0) {
      return `${minutes}分${secs}秒`
    } else {
      return `${secs}秒`
    }
  },

  // 获取结果图标
  getResultIcon(accuracy) {
    if (accuracy >= 90) return '🎉'
    if (accuracy >= 80) return '😊'
    if (accuracy >= 60) return '😐'
    return '😔'
  },

  // 获取结果标题
  getResultTitle(accuracy) {
    if (accuracy >= 90) return '优秀！'
    if (accuracy >= 80) return '良好！'
    if (accuracy >= 60) return '及格！'
    return '需要加油！'
  },

  // 获取结果副标题
  getResultSubtitle(accuracy) {
    if (accuracy >= 90) return '表现非常出色，继续保持！'
    if (accuracy >= 80) return '表现不错，再接再厉！'
    if (accuracy >= 60) return '基础还可以，继续努力！'
    return '需要多加练习，加油！'
  },

  // 检查成就
  checkAchievements() {
    const { result } = this.data
    const achievements = []

    if (result.accuracy === 100) {
      achievements.push({
        icon: '🏆',
        name: '完美答题'
      })
    }

    if (result.accuracy >= 90) {
      achievements.push({
        icon: '🎓',
        name: '学霸'
      })
    }

    if (result.totalTime && result.totalTime < 600) { // 10分钟内完成
      achievements.push({
        icon: '⚡',
        name: '速度之王'
      })
    }

    if (result.totalQuestions >= 50) {
      achievements.push({
        icon: '💪',
        name: '刷题达人'
      })
    }

    this.setData({
      achievements
    })
  },

  // 生成学习建议
  generateSuggestions() {
    const { result } = this.data
    const suggestions = []

    if (result.accuracy < 60) {
      suggestions.push('建议回顾基础知识，加强基本概念的理解')
      suggestions.push('可以先从简单题目开始练习，逐步提高难度')
    } else if (result.accuracy < 80) {
      suggestions.push('基础掌握不错，建议多做一些中等难度的题目')
      suggestions.push('注意总结错题，避免重复犯错')
    } else if (result.accuracy < 90) {
      suggestions.push('表现很好，可以挑战一些高难度题目')
      suggestions.push('建议定期复习，保持学习状态')
    } else {
      suggestions.push('表现优秀，建议帮助其他同学一起进步')
      suggestions.push('可以尝试更多不同类型的题目')
    }

    if (result.wrongQuestions.length > 0) {
      suggestions.push('建议重点复习错题，理解错误原因')
    }

    this.setData({
      suggestions
    })
  },

  // 查看错题详情
  reviewQuestion(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages/question/question?questionId=${item.question.question_id}&isReview=true`
    })
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 继续学习
  continueStudy() {
    wx.switchTab({
      url: '/pages/practice/practice'
    })
  },

  // 再来一次
  tryAgain() {
    wx.navigateBack({
      delta: 2 // 返回到练习选择页面
    })
  },

  // 查看错题本
  viewErrors() {
    wx.navigateTo({
      url: '/pages/errors/errors'
    })
  }
})
