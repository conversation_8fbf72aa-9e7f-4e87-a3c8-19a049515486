{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\dashboard\\index.vue?vue&type=style&index=0&id=106c86ed&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\dashboard\\index.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgouZGFzaGJvYXJkIHsKICAmLWNvbnRhaW5lciB7CiAgICBtYXJnaW46IDMwcHg7CiAgfQogICYtdGV4dCB7CiAgICBmb250LXNpemU6IDMwcHg7CiAgICBsaW5lLWhlaWdodDogNDZweDsKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <div class=\"dashboard-text\">name: {{ name }}</div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'Dashboard',\n  computed: {\n    ...mapGetters([\n      'name'\n    ])\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard {\n  &-container {\n    margin: 30px;\n  }\n  &-text {\n    font-size: 30px;\n    line-height: 46px;\n  }\n}\n</style>\n"]}]}