{"_from": "is-equal-shallow@^0.1.3", "_id": "is-equal-shallow@0.1.3", "_inBundle": false, "_integrity": "sha512-0EygVC5qPvIyb+gSz7zdD5/AAoS6Qrx1e//6N4yv4oNm30kqvdmG66oZFWVlQHUWe5OjP08FuTw2IdT0EOTcYA==", "_location": "/is-equal-shallow", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-equal-shallow@^0.1.3", "name": "is-equal-shallow", "escapedName": "is-equal-shallow", "rawSpec": "^0.1.3", "saveSpec": null, "fetchSpec": "^0.1.3"}, "_requiredBy": ["/regex-cache"], "_resolved": "https://registry.npmmirror.com/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz", "_shasum": "2238098fc221de0bcfa5d9eac4c45d638aa1c534", "_spec": "is-equal-shallow@^0.1.3", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\regex-cache", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-equal-shallow/issues"}, "bundleDependencies": false, "dependencies": {"is-primitive": "^2.0.0"}, "deprecated": false, "description": "Does a shallow comparison of two objects, returning false if the keys or values differ.", "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-equal-shallow", "keywords": ["compare", "comparison", "equal", "equals", "is", "is-equal", "key", "object", "same", "shallow", "value"], "license": "MIT", "main": "index.js", "name": "is-equal-shallow", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-equal-shallow.git"}, "scripts": {"test": "mocha"}, "verbiage": {"related": {"description": "Other object utils:", "list": ["is-plain-object", "isobject", "for-in", "for-own", "clone-deep"]}}, "version": "0.1.3"}