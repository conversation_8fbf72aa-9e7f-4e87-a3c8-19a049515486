/**result.wxss**/

.container {
  padding: 30rpx;
  padding-bottom: 120rpx;
}

/* 结果卡片 */
.result-card {
  text-align: center;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-icon {
  margin-bottom: 30rpx;
}

.icon-emoji {
  font-size: 120rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.result-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 统计部分 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-section {
  margin-bottom: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10rpx;
}

.stats-number.correct {
  color: #67C23A;
}

.stats-number.wrong {
  color: #F56C6C;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 用时统计 */
.time-section {
  margin-bottom: 30rpx;
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.time-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.time-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.time-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #409EFF;
}

/* 错题列表 */
.wrong-section {
  margin-bottom: 30rpx;
}

.wrong-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wrong-item {
  background-color: #fef0f0;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 6rpx solid #F56C6C;
}

.question-number {
  font-size: 24rpx;
  color: #F56C6C;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.question-content {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.answer-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 24rpx;
}

.user-answer {
  color: #F56C6C;
}

.correct-answer {
  color: #67C23A;
}

/* 成就徽章 */
.achievement-section {
  margin-bottom: 30rpx;
}

.achievement-list {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
  justify-content: center;
}

.achievement-item {
  text-align: center;
  width: 150rpx;
}

.achievement-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 10rpx;
}

.achievement-name {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 学习建议 */
.suggestion-section {
  margin-bottom: 30rpx;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.suggestion-icon {
  margin-right: 10rpx;
  flex-shrink: 0;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #409EFF;
  color: #fff;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

/* 分享功能 */
.share-section {
  position: fixed;
  right: 30rpx;
  bottom: 140rpx;
  z-index: 99;
}

.share-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #409EFF;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}

.share-icon {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}

/* 动画效果 */
.result-card {
  animation: fadeInDown 0.5s ease-out;
}

.stats-section,
.time-section,
.wrong-section,
.achievement-section,
.suggestion-section {
  animation: fadeInUp 0.5s ease-out;
  animation-fill-mode: both;
}

.stats-section {
  animation-delay: 0.1s;
}

.time-section {
  animation-delay: 0.2s;
}

.wrong-section {
  animation-delay: 0.3s;
}

.achievement-section {
  animation-delay: 0.4s;
}

.suggestion-section {
  animation-delay: 0.5s;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
