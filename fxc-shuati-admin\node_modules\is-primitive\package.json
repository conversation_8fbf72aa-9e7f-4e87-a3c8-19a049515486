{"_from": "is-primitive@^2.0.0", "_id": "is-primitive@2.0.0", "_inBundle": false, "_integrity": "sha512-N3w1tFaRfk3UrPfqeRyD+GYDASU3W5VinKhlORy8EWVf/sIdDL9GAcew85XmktCfH+ngG7SRXEVDoO18WMdB/Q==", "_location": "/is-primitive", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-primitive@^2.0.0", "name": "is-primitive", "escapedName": "is-primitive", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/is-equal-shallow"], "_resolved": "https://registry.npmmirror.com/is-primitive/-/is-primitive-2.0.0.tgz", "_shasum": "207bab91638499c07b2adf240a41a87210034575", "_spec": "is-primitive@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\is-equal-shallow", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-primitive/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Returns `true` if the value is a primitive. ", "devDependencies": {"mocha": "*", "should": "^4.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-primitive", "keywords": ["boolean", "check", "number", "primitive", "string", "symbol", "type", "typeof", "util"], "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-primitive/blob/master/LICENSE"}, "main": "index.js", "name": "is-primitive", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-primitive.git"}, "scripts": {"test": "mocha"}, "version": "2.0.0"}