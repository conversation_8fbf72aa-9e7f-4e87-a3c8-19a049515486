const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateAdminPassword() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '*************',
      user: process.env.DB_USER || 'fxc-shuati',
      password: process.env.DB_PASSWORD || 'haf6LeMjHLKefKs7',
      database: process.env.DB_NAME || 'fxc-shuati',
      port: process.env.DB_PORT || 3306
    });

    console.log('数据库连接成功');

    // 生成新的密码hash
    const password = '123456';
    const hashedPassword = await bcrypt.hash(password, 10);
    console.log('新密码hash:', hashedPassword);
    
    // 更新admin用户的密码
    const [result] = await connection.query(
      'UPDATE admins SET password = ? WHERE username = ?',
      [hashedPassword, 'admin']
    );
    
    console.log('更新结果:', result);
    
    if (result.affectedRows > 0) {
      console.log('admin密码更新成功！');
      
      // 验证更新后的密码
      const [admins] = await connection.query('SELECT username, password FROM admins WHERE username = ?', ['admin']);
      const admin = admins[0];
      const isValid = await bcrypt.compare(password, admin.password);
      console.log('密码验证结果:', isValid);
    } else {
      console.log('没有找到admin用户');
    }

  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

updateAdminPassword();
