{"_from": "is-path-in-cwd@^2.0.0", "_id": "is-path-in-cwd@2.1.0", "_inBundle": false, "_integrity": "sha512-rNocXHgipO+rvnP6dk3zI20RpOtrAM/kzbB258Uw5BWr3TpXi861yzjo16Dn4hUox07iw5AyeMLHWsujkjzvRQ==", "_location": "/is-path-in-cwd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-path-in-cwd@^2.0.0", "name": "is-path-in-cwd", "escapedName": "is-path-in-cwd", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/del"], "_resolved": "https://registry.npmmirror.com/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz", "_shasum": "bfe2dca26c69f397265a4009963602935a053acb", "_spec": "is-path-in-cwd@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\del", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-path-in-cwd/issues"}, "bundleDependencies": false, "dependencies": {"is-path-inside": "^2.1.0"}, "deprecated": false, "description": "Check if a path is in the current working directory", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-path-in-cwd#readme", "keywords": ["path", "cwd", "pwd", "check", "filepath", "file", "folder", "in", "inside"], "license": "MIT", "name": "is-path-in-cwd", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-path-in-cwd.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.1.0"}