<!--errors.wxml-->
<view class="container">
  <!-- 统计信息 -->
  <view class="stats-section card">
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{errorStats.total_errors}}</text>
        <text class="stats-label">错题总数</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{errorStats.accuracy_rate}}%</text>
        <text class="stats-label">平均正确率</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{errorStats.recent_errors}}</text>
        <text class="stats-label">最近错题</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{errorStats.review_count}}</text>
        <text class="stats-label">已复习</text>
      </view>
    </view>
  </view>

  <!-- 筛选条件 -->
  <view class="filter-section card">
    <scroll-view scroll-x="true" class="filter-scroll">
      <view 
        wx:for="{{subjectOptions}}" 
        wx:key="value"
        class="filter-item {{selectedSubject === item.value ? 'active' : ''}}"
        bindtap="selectSubject"
        data-value="{{item.value}}"
      >
        {{item.label}}
      </view>
    </scroll-view>
  </view>

  <!-- 错题列表 -->
  <view class="error-list" wx:if="{{errorList.length > 0}}">
    <view 
      wx:for="{{errorList}}" 
      wx:key="error_id"
      class="error-item card"
      bindtap="reviewQuestion"
      data-item="{{item}}"
    >
      <view class="question-info">
        <view class="question-title">{{item.question.question_content}}</view>
        <view class="question-meta">
          <text class="question-type">{{item.question.typeText}}</text>
          <text class="error-count">错误{{item.error_count}}次</text>
          <text class="error-time">{{item.timeText}}</text>
        </view>
        <view class="answer-comparison" wx:if="{{item.user_answer}}">
          <view class="user-answer">
            <text class="answer-label">你的答案:</text>
            <text class="answer-content wrong">{{item.user_answer}}</text>
          </view>
          <view class="correct-answer" wx:if="{{item.correct_answer}}">
            <text class="answer-label">正确答案:</text>
            <text class="answer-content correct">{{item.correct_answer}}</text>
          </view>
        </view>
      </view>
      <view class="error-actions">
        <button 
          class="review-btn btn btn-primary" 
          size="mini" 
          bindtap="reviewQuestion" 
          data-item="{{item}}"
          catchtap=""
        >
          重做
        </button>
        <button 
          class="delete-btn btn btn-danger" 
          size="mini" 
          bindtap="deleteError" 
          data-item="{{item}}"
          catchtap=""
        >
          删除
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <text class="empty-icon">✅</text>
    <text class="empty-text">暂无错题记录</text>
    <text class="empty-desc">继续练习，加油！</text>
    <button class="go-practice-btn btn btn-primary" bindtap="goToPractice">去练习</button>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions" wx:if="{{errorList.length > 0}}">
    <button class="action-btn btn-secondary" bindtap="clearAllErrors">清空错题本</button>
    <button class="action-btn btn-primary" bindtap="reviewAllErrors">全部重做</button>
  </view>

  <!-- 学习建议 -->
  <view class="suggestion-section card" wx:if="{{suggestions.length > 0}}">
    <view class="section-title">学习建议</view>
    <view class="suggestion-list">
      <view wx:for="{{suggestions}}" wx:key="index" class="suggestion-item">
        <text class="suggestion-icon">💡</text>
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && errorList.length > 0}}">
    <button class="load-more-btn" bindtap="loadMore" loading="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>
</view>
