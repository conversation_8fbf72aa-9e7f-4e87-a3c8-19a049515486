{"name": "fxc-shuati-api", "version": "1.0.0", "description": "刷题小程序API接口", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mysql", "api", "shuati"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "mysql2": "^3.6.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}}