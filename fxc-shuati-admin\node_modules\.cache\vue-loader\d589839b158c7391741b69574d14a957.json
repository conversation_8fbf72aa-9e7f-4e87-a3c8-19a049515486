{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue?vue&type=style&index=0&id=25336f9d&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue", "mtime": 1752629996146}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["upload.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmWA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "upload.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 导入说明 -->\n    <el-card class=\"instruction-card\">\n      <div slot=\"header\">\n        <span>批量导入题目</span>\n      </div>\n      <div class=\"instruction-content\">\n        <el-alert\n          title=\"导入说明\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        >\n          <div slot=\"default\">\n            <p>1. 请下载模板文件，按照模板格式填写题目数据</p>\n            <p>2. 支持的文件格式：Excel (.xlsx, .xls)</p>\n            <p>3. 单次最多导入1000道题目</p>\n            <p>4. 题目类型：single_choice(单选), multiple_choice(多选), true_false(判断), fill_blank(填空), short_answer(简答), essay(论述)</p>\n            <p>5. 难度等级：easy(简单), medium(中等), hard(困难)</p>\n          </div>\n        </el-alert>\n\n        <div class=\"template-download\">\n          <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\n            下载导入模板\n          </el-button>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 文件上传 -->\n    <el-card class=\"upload-card\">\n      <div slot=\"header\">\n        <span>上传文件</span>\n      </div>\n\n      <el-upload\n        ref=\"upload\"\n        class=\"upload-demo\"\n        drag\n        :action=\"uploadUrl\"\n        :before-upload=\"beforeUpload\"\n        :on-success=\"handleSuccess\"\n        :on-error=\"handleError\"\n        :on-progress=\"handleProgress\"\n        :file-list=\"fileList\"\n        :auto-upload=\"false\"\n        accept=\".xlsx,.xls\"\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件，且不超过10MB</div>\n      </el-upload>\n\n      <div class=\"upload-actions\">\n        <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploading\">\n          开始导入\n        </el-button>\n        <el-button @click=\"clearFiles\">清空文件</el-button>\n      </div>\n    </el-card>\n\n    <!-- 导入进度 -->\n    <el-card v-if=\"showProgress\" class=\"progress-card\">\n      <div slot=\"header\">\n        <span>导入进度</span>\n      </div>\n\n      <el-progress\n        :percentage=\"uploadProgress\"\n        :status=\"uploadStatus\"\n        :stroke-width=\"20\"\n      />\n\n      <div class=\"progress-info\">\n        <p>{{ progressText }}</p>\n      </div>\n    </el-card>\n\n    <!-- 导入结果 -->\n    <el-card v-if=\"importResult\" class=\"result-card\">\n      <div slot=\"header\">\n        <span>导入结果</span>\n      </div>\n\n      <div class=\"result-summary\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <div class=\"stat-item success\">\n              <div class=\"stat-number\">{{ importResult.success_count }}</div>\n              <div class=\"stat-label\">成功导入</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item error\">\n              <div class=\"stat-number\">{{ importResult.error_count }}</div>\n              <div class=\"stat-label\">导入失败</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item warning\">\n              <div class=\"stat-number\">{{ importResult.skip_count }}</div>\n              <div class=\"stat-label\">跳过重复</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item info\">\n              <div class=\"stat-number\">{{ importResult.total_count }}</div>\n              <div class=\"stat-label\">总计处理</div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 错误详情 -->\n      <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"error-details\">\n        <h4>错误详情：</h4>\n        <el-table\n          :data=\"importResult.errors\"\n          border\n          size=\"small\"\n          max-height=\"300\"\n        >\n          <el-table-column prop=\"row\" label=\"行号\" width=\"80\" />\n          <el-table-column prop=\"field\" label=\"字段\" width=\"120\" />\n          <el-table-column prop=\"message\" label=\"错误信息\" />\n        </el-table>\n      </div>\n\n      <div class=\"result-actions\">\n        <el-button type=\"primary\" @click=\"goToQuestionList\">查看题目列表</el-button>\n        <el-button @click=\"resetImport\">重新导入</el-button>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'QuestionUpload',\n  data() {\n    return {\n      uploadUrl: '/api/questions/import', // 实际项目中的上传接口\n      fileList: [],\n      uploading: false,\n      showProgress: false,\n      uploadProgress: 0,\n      uploadStatus: '',\n      progressText: '',\n      importResult: null\n    }\n  },\n  methods: {\n    downloadTemplate() {\n      // 创建模板数据\n      const templateData = [\n        {\n          '题目类型': 'single_choice',\n          '难度等级': 'medium',\n          '分类ID': '111',\n          '标签ID': '1,6',\n          '题目内容': '马克思主义哲学的基本问题是什么？',\n          '选项A': '物质和意识的关系问题',\n          '选项B': '理论和实践的关系问题',\n          '选项C': '个人和社会的关系问题',\n          '选项D': '自由和必然的关系问题',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'A',\n          '题目解析': '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。',\n          '分值': '5',\n          '排序': '1',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'multiple_choice',\n          '难度等级': 'medium',\n          '分类ID': '111',\n          '标签ID': '1,7',\n          '题目内容': '马克思主义哲学的特征包括哪些？',\n          '选项A': '实践性',\n          '选项B': '科学性',\n          '选项C': '革命性',\n          '选项D': '阶级性',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'A,B,C,D',\n          '题目解析': '马克思主义哲学具有实践性、科学性、革命性和阶级性等特征。',\n          '分值': '5',\n          '排序': '2',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'true_false',\n          '难度等级': 'easy',\n          '分类ID': '111',\n          '标签ID': '1',\n          '题目内容': '物质是标志客观实在的哲学范畴。',\n          '选项A': '',\n          '选项B': '',\n          '选项C': '',\n          '选项D': '',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'true',\n          '题目解析': '这是马克思主义哲学对物质概念的经典定义。',\n          '分值': '3',\n          '排序': '3',\n          '状态': 'active'\n        }\n      ]\n\n      // 转换为CSV格式并下载\n      this.downloadCSV(templateData, '题目导入模板.csv')\n    },\n\n    downloadCSV(data, filename) {\n      const csvContent = this.convertToCSV(data)\n      const blob = new Blob(['\\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })\n      const link = document.createElement('a')\n\n      if (link.download !== undefined) {\n        const url = URL.createObjectURL(blob)\n        link.setAttribute('href', url)\n        link.setAttribute('download', filename)\n        link.style.visibility = 'hidden'\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n      }\n    },\n\n    convertToCSV(data) {\n      if (!data || data.length === 0) return ''\n\n      const headers = Object.keys(data[0])\n      const csvHeaders = headers.join(',')\n\n      const csvRows = data.map(row => {\n        return headers.map(header => {\n          const value = row[header] || ''\n          return `\"${value.toString().replace(/\"/g, '\"\"')}\"`\n        }).join(',')\n      })\n\n      return [csvHeaders, ...csvRows].join('\\n')\n    },\n\n    beforeUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                     file.type === 'application/vnd.ms-excel'\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isExcel) {\n        this.$message.error('只能上传Excel文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n      return true\n    },\n\n    submitUpload() {\n      if (this.fileList.length === 0) {\n        this.$message.warning('请先选择要上传的文件')\n        return\n      }\n\n      this.uploading = true\n      this.showProgress = true\n      this.uploadProgress = 0\n      this.uploadStatus = ''\n      this.progressText = '开始上传文件...'\n\n      // 模拟上传过程\n      this.simulateUpload()\n    },\n\n    simulateUpload() {\n      const timer = setInterval(() => {\n        this.uploadProgress += Math.random() * 15\n\n        if (this.uploadProgress < 30) {\n          this.progressText = '正在上传文件...'\n        } else if (this.uploadProgress < 60) {\n          this.progressText = '正在解析文件内容...'\n        } else if (this.uploadProgress < 90) {\n          this.progressText = '正在导入题目数据...'\n        } else {\n          this.uploadProgress = 100\n          this.progressText = '导入完成！'\n          this.uploadStatus = 'success'\n          this.uploading = false\n\n          // 模拟导入结果\n          setTimeout(() => {\n            this.importResult = {\n              success_count: 245,\n              error_count: 5,\n              skip_count: 12,\n              total_count: 262,\n              errors: [\n                { row: 15, field: '题目内容', message: '题目内容不能为空' },\n                { row: 23, field: '正确答案', message: '正确答案格式错误' },\n                { row: 45, field: '分类ID', message: '分类ID不存在' },\n                { row: 67, field: '题目类型', message: '不支持的题目类型' },\n                { row: 89, field: '选项A', message: '单选题至少需要2个选项' }\n              ]\n            }\n          }, 500)\n\n          clearInterval(timer)\n        }\n      }, 200)\n    },\n\n    handleSuccess(response, file, fileList) {\n      this.$message.success('文件上传成功')\n    },\n\n    handleError(err, file, fileList) {\n      this.$message.error('文件上传失败')\n      this.uploading = false\n      this.showProgress = false\n    },\n\n    handleProgress(event, file, fileList) {\n      // 实际项目中可以在这里处理真实的上传进度\n    },\n\n    clearFiles() {\n      this.$refs.upload.clearFiles()\n      this.fileList = []\n      this.showProgress = false\n      this.importResult = null\n    },\n\n    resetImport() {\n      this.clearFiles()\n      this.uploadProgress = 0\n      this.uploadStatus = ''\n      this.progressText = ''\n    },\n\n    goToQuestionList() {\n      this.$router.push('/questions/list')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .instruction-card,\n  .upload-card,\n  .progress-card,\n  .result-card {\n    margin-bottom: 20px;\n  }\n\n  .instruction-content {\n    .template-download {\n      margin-top: 20px;\n      text-align: center;\n    }\n  }\n\n  .upload-demo {\n    margin-bottom: 20px;\n  }\n\n  .upload-actions {\n    text-align: center;\n    margin-top: 20px;\n  }\n\n  .progress-info {\n    margin-top: 20px;\n    text-align: center;\n\n    p {\n      margin: 0;\n      color: #606266;\n    }\n  }\n\n  .result-summary {\n    margin-bottom: 30px;\n\n    .stat-item {\n      text-align: center;\n      padding: 20px;\n      border-radius: 8px;\n\n      .stat-number {\n        font-size: 32px;\n        font-weight: bold;\n        margin-bottom: 8px;\n      }\n\n      .stat-label {\n        font-size: 14px;\n        color: #666;\n      }\n\n      &.success {\n        background: #f0f9ff;\n        .stat-number { color: #67c23a; }\n      }\n\n      &.error {\n        background: #fef0f0;\n        .stat-number { color: #f56c6c; }\n      }\n\n      &.warning {\n        background: #fdf6ec;\n        .stat-number { color: #e6a23c; }\n      }\n\n      &.info {\n        background: #f4f4f5;\n        .stat-number { color: #909399; }\n      }\n    }\n  }\n\n  .error-details {\n    margin-bottom: 30px;\n\n    h4 {\n      margin-bottom: 15px;\n      color: #f56c6c;\n    }\n  }\n\n  .result-actions {\n    text-align: center;\n  }\n}\n</style>\n"]}]}