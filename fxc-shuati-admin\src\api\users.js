import request from '@/utils/request'

// 微信用户管理API
export function getWechatUserList(params) {
  return request({
    url: '/api/wechat-users',
    method: 'get',
    params
  })
}

export function getWechatUserDetail(id) {
  return request({
    url: `/api/wechat-users/${id}`,
    method: 'get'
  })
}

export function updateWechatUser(id, data) {
  return request({
    url: `/api/wechat-users/${id}`,
    method: 'put',
    data
  })
}

export function deleteWechatUser(id) {
  return request({
    url: `/api/wechat-users/${id}`,
    method: 'delete'
  })
}

export function batchDeleteWechatUsers(ids) {
  return request({
    url: '/api/wechat-users/batch',
    method: 'delete',
    data: { ids }
  })
}

// 管理员用户API
export function getAdminUserList(params) {
  return request({
    url: '/api/admin-users',
    method: 'get',
    params
  })
}

export function createAdminUser(data) {
  return request({
    url: '/api/admin-users',
    method: 'post',
    data
  })
}

export function updateAdminUser(id, data) {
  return request({
    url: `/api/admin-users/${id}`,
    method: 'put',
    data
  })
}

export function deleteAdminUser(id) {
  return request({
    url: `/api/admin-users/${id}`,
    method: 'delete'
  })
}

// 角色管理API
export function getRoleList(params) {
  return request({
    url: '/api/roles',
    method: 'get',
    params
  })
}

export function createRole(data) {
  return request({
    url: '/api/roles',
    method: 'post',
    data
  })
}

export function updateRole(id, data) {
  return request({
    url: `/api/roles/${id}`,
    method: 'put',
    data
  })
}

export function deleteRole(id) {
  return request({
    url: `/api/roles/${id}`,
    method: 'delete'
  })
}

export function getRolePermissions(id) {
  return request({
    url: `/api/roles/${id}/permissions`,
    method: 'get'
  })
}

export function updateRolePermissions(id, data) {
  return request({
    url: `/api/roles/${id}/permissions`,
    method: 'put',
    data
  })
}

// 权限管理API
export function getPermissionList(params) {
  return request({
    url: '/api/permissions',
    method: 'get',
    params
  })
}

export function getPermissionTree() {
  return request({
    url: '/api/permissions/tree',
    method: 'get'
  })
}

export function createPermission(data) {
  return request({
    url: '/api/permissions',
    method: 'post',
    data
  })
}

export function updatePermission(id, data) {
  return request({
    url: `/api/permissions/${id}`,
    method: 'put',
    data
  })
}

export function deletePermission(id) {
  return request({
    url: `/api/permissions/${id}`,
    method: 'delete'
  })
}

// 用户统计API
export function getUserStats() {
  return request({
    url: '/api/users/stats',
    method: 'get'
  })
}

export function getWechatUserStats() {
  return request({
    url: '/api/wechat-users/stats',
    method: 'get'
  })
}

// 权限树API
export function getPermissionTree() {
  return request({
    url: '/api/permissions/tree',
    method: 'get'
  })
}
