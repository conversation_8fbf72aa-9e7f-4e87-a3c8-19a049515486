{"_from": "is-arrayish@^0.2.1", "_id": "is-arrayish@0.2.1", "_inBundle": false, "_integrity": "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==", "_location": "/is-arrayish", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-arrayish@^0.2.1", "name": "is-arrayish", "escapedName": "is-arrayish", "rawSpec": "^0.2.1", "saveSpec": null, "fetchSpec": "^0.2.1"}, "_requiredBy": ["/error-ex"], "_resolved": "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz", "_shasum": "77c99840527aa8ecb1a8ba697b80645a7a926a9d", "_spec": "is-arrayish@^0.2.1", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\error-ex", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "bugs": {"url": "https://github.com/qix-/node-is-arrayish/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Determines if an object can be used as an array", "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "homepage": "https://github.com/qix-/node-is-arrayish#readme", "keywords": ["is", "array", "duck", "type", "arrayish", "similar", "proto", "prototype", "type"], "license": "MIT", "name": "is-arrayish", "repository": {"type": "git", "url": "git+https://github.com/qix-/node-is-arrayish.git"}, "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "version": "0.2.1"}