const express = require('express');
const { query } = require('../config/database');
const router = express.Router();

// 获取微信用户列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, keyword = '', status = '', is_vip = '' } = req.query;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (keyword) {
      whereClause += ' AND (nickname LIKE ? OR phone LIKE ? OR email LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }
    
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    if (is_vip !== '') {
      whereClause += ' AND is_vip = ?';
      params.push(is_vip === 'true' ? 1 : 0);
    }
    
    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    const listSql = `
      SELECT user_id, openid, nickname, avatar_url, phone, email, is_vip, 
             vip_expire_time, status, created_at,
             (SELECT COUNT(*) FROM user_favorites WHERE user_id = users.user_id) as favorite_count,
             (SELECT COUNT(*) FROM user_errors WHERE user_id = users.user_id) as error_count,
             (SELECT COUNT(*) FROM user_answer_records WHERE user_id = users.user_id) as answer_count
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    params.push(parseInt(pageSize), offset);
    
    const list = await query(listSql, params);
    
    res.json({
      success: true,
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取微信用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取微信用户列表失败'
    });
  }
});

// 获取微信用户详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT user_id, openid, nickname, avatar_url, phone, email, is_vip, 
             vip_expire_time, status, created_at, updated_at,
             (SELECT COUNT(*) FROM user_favorites WHERE user_id = users.user_id) as favorite_count,
             (SELECT COUNT(*) FROM user_errors WHERE user_id = users.user_id) as error_count,
             (SELECT COUNT(*) FROM user_answer_records WHERE user_id = users.user_id) as answer_count,
             (SELECT COUNT(*) FROM user_answer_records WHERE user_id = users.user_id AND is_correct = 1) as correct_count
      FROM users
      WHERE user_id = ?
    `;
    
    const result = await query(sql, [id]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    const user = result[0];
    user.accuracy_rate = user.answer_count > 0 ? Math.round((user.correct_count / user.answer_count) * 100) : 0;
    
    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('获取微信用户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取微信用户详情失败'
    });
  }
});

// 更新微信用户
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { nickname, phone, email, is_vip, vip_expire_time, status } = req.body;
    
    // 检查用户是否存在
    const existCheck = await query('SELECT user_id FROM users WHERE user_id = ?', [id]);
    if (existCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    let updateFields = [];
    let updateValues = [];
    
    if (nickname !== undefined) {
      updateFields.push('nickname = ?');
      updateValues.push(nickname);
    }
    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    if (email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (is_vip !== undefined) {
      updateFields.push('is_vip = ?');
      updateValues.push(is_vip);
    }
    if (vip_expire_time !== undefined) {
      updateFields.push('vip_expire_time = ?');
      updateValues.push(vip_expire_time);
    }
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有要更新的字段'
      });
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    const updateSql = `UPDATE users SET ${updateFields.join(', ')} WHERE user_id = ?`;
    await query(updateSql, updateValues);
    
    res.json({
      success: true,
      message: '更新用户成功'
    });
  } catch (error) {
    console.error('更新微信用户失败:', error);
    res.status(500).json({
      success: false,
      message: '更新微信用户失败'
    });
  }
});

// 删除微信用户
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query('DELETE FROM users WHERE user_id = ?', [id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      message: '删除用户成功'
    });
  } catch (error) {
    console.error('删除微信用户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除微信用户失败'
    });
  }
});

// 批量删除微信用户
router.delete('/', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的用户ID列表'
      });
    }
    
    const placeholders = ids.map(() => '?').join(',');
    const sql = `DELETE FROM users WHERE user_id IN (${placeholders})`;
    
    const result = await query(sql, ids);
    
    res.json({
      success: true,
      message: `成功删除 ${result.affectedRows} 个用户`
    });
  } catch (error) {
    console.error('批量删除微信用户失败:', error);
    res.status(500).json({
      success: false,
      message: '批量删除微信用户失败'
    });
  }
});

// 获取微信用户统计
router.get('/stats/overview', async (req, res) => {
  try {
    const stats = {};
    
    // 总用户数
    const totalResult = await query('SELECT COUNT(*) as total FROM users');
    stats.total_users = totalResult[0].total;
    
    // VIP用户数
    const vipResult = await query('SELECT COUNT(*) as total FROM users WHERE is_vip = 1');
    stats.vip_users = vipResult[0].total;
    
    // 活跃用户数（最近7天有答题记录）
    const activeResult = await query(`
      SELECT COUNT(DISTINCT user_id) as total 
      FROM user_answer_records 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    `);
    stats.active_users = activeResult[0].total;
    
    // 今日新增用户
    const todayResult = await query(`
      SELECT COUNT(*) as total 
      FROM users 
      WHERE DATE(created_at) = CURDATE()
    `);
    stats.today_new_users = todayResult[0].total;
    
    // 用户状态分布
    const statusResult = await query(`
      SELECT status, COUNT(*) as count 
      FROM users 
      GROUP BY status
    `);
    stats.status_distribution = statusResult;
    
    // 最近7天用户注册趋势
    const trendResult = await query(`
      SELECT DATE(created_at) as date, COUNT(*) as count
      FROM users
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date
    `);
    stats.registration_trend = trendResult;
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取微信用户统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取微信用户统计失败'
    });
  }
});

module.exports = router;
