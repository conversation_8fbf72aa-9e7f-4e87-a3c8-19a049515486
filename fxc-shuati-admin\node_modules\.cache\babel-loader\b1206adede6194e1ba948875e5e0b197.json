{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\directive\\waves\\waves.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\directive\\waves\\waves.js", "mtime": 1752568324888}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["context", "handleClick", "el", "binding", "handle", "e", "customOpts", "Object", "assign", "value", "opts", "ele", "type", "color", "target", "style", "position", "overflow", "rect", "getBoundingClientRect", "ripple", "querySelector", "document", "createElement", "className", "height", "width", "Math", "max", "append<PERSON><PERSON><PERSON>", "top", "offsetHeight", "left", "offsetWidth", "pageY", "documentElement", "scrollTop", "body", "pageX", "scrollLeft", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "bind", "addEventListener", "update", "removeEventListener", "unbind"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/directive/waves/waves.js"], "sourcesContent": ["import './waves.css'\n\nconst context = '@@wavesContext'\n\nfunction handleClick(el, binding) {\n  function handle(e) {\n    const customOpts = Object.assign({}, binding.value)\n    const opts = Object.assign({\n      ele: el, // 波纹作用元素\n      type: 'hit', // hit 点击位置扩散 center中心点扩展\n      color: 'rgba(0, 0, 0, 0.15)' // 波纹颜色\n    },\n    customOpts\n    )\n    const target = opts.ele\n    if (target) {\n      target.style.position = 'relative'\n      target.style.overflow = 'hidden'\n      const rect = target.getBoundingClientRect()\n      let ripple = target.querySelector('.waves-ripple')\n      if (!ripple) {\n        ripple = document.createElement('span')\n        ripple.className = 'waves-ripple'\n        ripple.style.height = ripple.style.width = Math.max(rect.width, rect.height) + 'px'\n        target.appendChild(ripple)\n      } else {\n        ripple.className = 'waves-ripple'\n      }\n      switch (opts.type) {\n        case 'center':\n          ripple.style.top = (rect.height / 2 - ripple.offsetHeight / 2) + 'px'\n          ripple.style.left = (rect.width / 2 - ripple.offsetWidth / 2) + 'px'\n          break\n        default:\n          ripple.style.top = (e.pageY - rect.top - ripple.offsetHeight / 2 - document.documentElement.scrollTop || document.body.scrollTop) + 'px'\n          ripple.style.left = (e.pageX - rect.left - ripple.offsetWidth / 2 - document.documentElement.scrollLeft || document.body.scrollLeft) + 'px'\n      }\n      ripple.style.backgroundColor = opts.color\n      ripple.className = 'waves-ripple waves-anim'\n      return false\n    }\n  }\n  if (!el[context]) {\n    el[context] = {\n      removeHandle: handle\n    }\n  } else {\n    el[context].removeHandle = handle\n  }\n  return handle\n}\n\nexport default {\n  bind(el, binding) {\n    el.addEventListener('click', handleClick(el, binding), false)\n  },\n  update(el, binding) {\n    el.removeEventListener('click', el[context].removeHandle, false)\n    el.addEventListener('click', handleClick(el, binding), false)\n  },\n  unbind(el) {\n    el.removeEventListener('click', el[context].removeHandle, false)\n    el[context] = null\n    delete el[context]\n  }\n}\n"], "mappings": "AAAA,OAAO,aAAa;AAEpB,IAAMA,OAAO,GAAG,gBAAgB;AAEhC,SAASC,WAAWA,CAACC,EAAE,EAAEC,OAAO,EAAE;EAChC,SAASC,MAAMA,CAACC,CAAC,EAAE;IACjB,IAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,OAAO,CAACM,KAAK,CAAC;IACnD,IAAMC,IAAI,GAAGH,MAAM,CAACC,MAAM,CAAC;MACzBG,GAAG,EAAET,EAAE;MAAE;MACTU,IAAI,EAAE,KAAK;MAAE;MACbC,KAAK,EAAE,qBAAqB,CAAC;IAC/B,CAAC,EACDP,UACA,CAAC;IACD,IAAMQ,MAAM,GAAGJ,IAAI,CAACC,GAAG;IACvB,IAAIG,MAAM,EAAE;MACVA,MAAM,CAACC,KAAK,CAACC,QAAQ,GAAG,UAAU;MAClCF,MAAM,CAACC,KAAK,CAACE,QAAQ,GAAG,QAAQ;MAChC,IAAMC,IAAI,GAAGJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;MAC3C,IAAIC,MAAM,GAAGN,MAAM,CAACO,aAAa,CAAC,eAAe,CAAC;MAClD,IAAI,CAACD,MAAM,EAAE;QACXA,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;QACvCH,MAAM,CAACI,SAAS,GAAG,cAAc;QACjCJ,MAAM,CAACL,KAAK,CAACU,MAAM,GAAGL,MAAM,CAACL,KAAK,CAACW,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACV,IAAI,CAACQ,KAAK,EAAER,IAAI,CAACO,MAAM,CAAC,GAAG,IAAI;QACnFX,MAAM,CAACe,WAAW,CAACT,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLA,MAAM,CAACI,SAAS,GAAG,cAAc;MACnC;MACA,QAAQd,IAAI,CAACE,IAAI;QACf,KAAK,QAAQ;UACXQ,MAAM,CAACL,KAAK,CAACe,GAAG,GAAIZ,IAAI,CAACO,MAAM,GAAG,CAAC,GAAGL,MAAM,CAACW,YAAY,GAAG,CAAC,GAAI,IAAI;UACrEX,MAAM,CAACL,KAAK,CAACiB,IAAI,GAAId,IAAI,CAACQ,KAAK,GAAG,CAAC,GAAGN,MAAM,CAACa,WAAW,GAAG,CAAC,GAAI,IAAI;UACpE;QACF;UACEb,MAAM,CAACL,KAAK,CAACe,GAAG,GAAG,CAACzB,CAAC,CAAC6B,KAAK,GAAGhB,IAAI,CAACY,GAAG,GAAGV,MAAM,CAACW,YAAY,GAAG,CAAC,GAAGT,QAAQ,CAACa,eAAe,CAACC,SAAS,IAAId,QAAQ,CAACe,IAAI,CAACD,SAAS,IAAI,IAAI;UACxIhB,MAAM,CAACL,KAAK,CAACiB,IAAI,GAAG,CAAC3B,CAAC,CAACiC,KAAK,GAAGpB,IAAI,CAACc,IAAI,GAAGZ,MAAM,CAACa,WAAW,GAAG,CAAC,GAAGX,QAAQ,CAACa,eAAe,CAACI,UAAU,IAAIjB,QAAQ,CAACe,IAAI,CAACE,UAAU,IAAI,IAAI;MAC/I;MACAnB,MAAM,CAACL,KAAK,CAACyB,eAAe,GAAG9B,IAAI,CAACG,KAAK;MACzCO,MAAM,CAACI,SAAS,GAAG,yBAAyB;MAC5C,OAAO,KAAK;IACd;EACF;EACA,IAAI,CAACtB,EAAE,CAACF,OAAO,CAAC,EAAE;IAChBE,EAAE,CAACF,OAAO,CAAC,GAAG;MACZyC,YAAY,EAAErC;IAChB,CAAC;EACH,CAAC,MAAM;IACLF,EAAE,CAACF,OAAO,CAAC,CAACyC,YAAY,GAAGrC,MAAM;EACnC;EACA,OAAOA,MAAM;AACf;AAEA,eAAe;EACbsC,IAAI,WAAJA,IAAIA,CAACxC,EAAE,EAAEC,OAAO,EAAE;IAChBD,EAAE,CAACyC,gBAAgB,CAAC,OAAO,EAAE1C,WAAW,CAACC,EAAE,EAAEC,OAAO,CAAC,EAAE,KAAK,CAAC;EAC/D,CAAC;EACDyC,MAAM,WAANA,MAAMA,CAAC1C,EAAE,EAAEC,OAAO,EAAE;IAClBD,EAAE,CAAC2C,mBAAmB,CAAC,OAAO,EAAE3C,EAAE,CAACF,OAAO,CAAC,CAACyC,YAAY,EAAE,KAAK,CAAC;IAChEvC,EAAE,CAACyC,gBAAgB,CAAC,OAAO,EAAE1C,WAAW,CAACC,EAAE,EAAEC,OAAO,CAAC,EAAE,KAAK,CAAC;EAC/D,CAAC;EACD2C,MAAM,WAANA,MAAMA,CAAC5C,EAAE,EAAE;IACTA,EAAE,CAAC2C,mBAAmB,CAAC,OAAO,EAAE3C,EAAE,CAACF,OAAO,CAAC,CAACyC,YAAY,EAAE,KAAK,CAAC;IAChEvC,EAAE,CAACF,OAAO,CAAC,GAAG,IAAI;IAClB,OAAOE,EAAE,CAACF,OAAO,CAAC;EACpB;AACF,CAAC", "ignoreList": []}]}