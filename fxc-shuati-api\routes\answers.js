const express = require('express');
const { query } = require('../config/database');
const { authenticateAdmin } = require('./auth');

const router = express.Router();

// Mock 答案数据
const answers = [
  {
    answer_id: 1,
    question_id: 2,
    answer_content: "Paris",
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  }
];

// 获取题目答案
router.get('/question/:question_id', async (req, res) => {
  try {
    const questionId = parseInt(req.params.question_id);
    const answer = answers.find(ans => ans.question_id === questionId);

    if (!answer) {
      return res.status(404).json({
        success: false,
        message: '答案不存在'
      });
    }

    res.json({
      success: true,
      data: answer,
      message: '获取答案成功'
    });

  } catch (error) {
    console.error('获取答案错误:', error);
    res.status(500).json({
      success: false,
      message: '获取答案失败'
    });
  }
});

// 创建答案（管理员）
router.post('/', authenticateAdmin, async (req, res) => {
  try {
    const { question_id, answer_content } = req.body;

    if (!question_id || !answer_content) {
      return res.status(400).json({
        success: false,
        message: '题目ID和答案内容不能为空'
      });
    }

    const newAnswer = {
      answer_id: answers.length + 1,
      question_id: parseInt(question_id),
      answer_content,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    answers.push(newAnswer);

    res.json({
      success: true,
      data: newAnswer,
      message: '创建答案成功'
    });

  } catch (error) {
    console.error('创建答案错误:', error);
    res.status(500).json({
      success: false,
      message: '创建答案失败'
    });
  }
});

module.exports = router;
