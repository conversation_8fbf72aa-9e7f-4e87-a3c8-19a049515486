{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\list.vue?vue&type=style&index=0&id=3fad8cba&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\list.vue", "mtime": 1752630716965}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFwcC1jb250YWluZXIgewogIC5maWx0ZXItY2FyZCB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgLmZpbHRlci1jb250YWluZXIgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LXdyYXA6IHdyYXA7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIC5maWx0ZXItaXRlbSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICAgIH0KICAgIH0KICB9CiAgLmVsLXRhYmxlIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50OwoKICAgIC5lbC10YWJsZV9fYm9keS13cmFwcGVyIHsKICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsKICAgIH0KCiAgICAucXVlc3Rpb24tY29udGVudCB7CiAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7CiAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLXdvcmQ7CiAgICB9CiAgfQogIC5wYWdpbmF0aW9uLWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAxNXB4IDA7CiAgfQp9CgoucXVlc3Rpb24tZGV0YWlsIHsKICAuZGV0YWlsLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICBsYWJlbCB7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgbWFyZ2luLWJvdHRvbTogOHB4OwogICAgICBkaXNwbGF5OiBibG9jazsKICAgIH0KCiAgICAuY29udGVudCwKICAgIC5hbnN3ZXIsCiAgICAuZXhwbGFuYXRpb24gewogICAgICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOwogICAgICBwYWRkaW5nOiAxMnB4OwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7CiAgICB9CgogICAgLm9wdGlvbnMgewogICAgICAub3B0aW9uIHsKICAgICAgICBwYWRkaW5nOiA4cHggMTJweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgICAgICAgYmFja2dyb3VuZDogI2Y1ZjdmYTsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgICAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjNDA5ZWZmOwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索题目内容\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.question_type\"\n          placeholder=\"题目类型\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"单选题\" value=\"single_choice\" />\n          <el-option label=\"多选题\" value=\"multiple_choice\" />\n          <el-option label=\"判断题\" value=\"true_false\" />\n          <el-option label=\"填空题\" value=\"fill_blank\" />\n          <el-option label=\"简答题\" value=\"short_answer\" />\n          <el-option label=\"论述题\" value=\"essay\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.difficulty\"\n          placeholder=\"难度等级\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"简单\" value=\"easy\" />\n          <el-option label=\"中等\" value=\"medium\" />\n          <el-option label=\"困难\" value=\"hard\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.category_id\"\n          placeholder=\"题目分类\"\n          clearable\n          style=\"width: 150px\"\n          class=\"filter-item\"\n        >\n          <el-option\n            v-for=\"category in categoryOptions\"\n            :key=\"category.category_id\"\n            :label=\"category.category_name\"\n            :value=\"category.category_id\"\n          />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加题目\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          type=\"success\"\n          icon=\"el-icon-upload\"\n          @click=\"handleImport\"\n        >\n          批量导入\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"question_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"题目内容\" prop=\"question_content\" min-width=\"300\">\n        <template slot-scope=\"{row}\">\n          <div class=\"question-content\">\n            {{ row.question_content.length > 100 ? row.question_content.substring(0, 100) + '...' : row.question_content }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"题目类型\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"typeTagMap[row.question_type]\">\n            {{ typeTextMap[row.question_type] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"难度\" width=\"80\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"difficultyTagMap[row.difficulty]\" size=\"mini\">\n            {{ difficultyTextMap[row.difficulty] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"分类\" prop=\"category_name\" min-width=\"120\" />\n      <el-table-column label=\"标签\" min-width=\"150\">\n        <template slot-scope=\"{row}\">\n          <el-tag\n            v-for=\"tag in row.tags\"\n            :key=\"tag.tag_id\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n          >\n            {{ tag.tag_name }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"分值\" prop=\"score\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ formatDate(row.created_at) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button type=\"info\" size=\"mini\" @click=\"handleView(row)\">\n            查看\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 查看题目对话框 -->\n    <el-dialog title=\"题目详情\" :visible.sync=\"viewDialogVisible\" width=\"60%\">\n      <div v-if=\"currentQuestion\" class=\"question-detail\">\n        <div class=\"detail-item\">\n          <label>题目类型：</label>\n          <el-tag :type=\"typeTagMap[currentQuestion.question_type]\">\n            {{ typeTextMap[currentQuestion.question_type] }}\n          </el-tag>\n        </div>\n        <div class=\"detail-item\">\n          <label>难度等级：</label>\n          <el-tag :type=\"difficultyTagMap[currentQuestion.difficulty]\">\n            {{ difficultyTextMap[currentQuestion.difficulty] }}\n          </el-tag>\n        </div>\n        <div class=\"detail-item\">\n          <label>题目内容：</label>\n          <div class=\"content\">{{ currentQuestion.question_content }}</div>\n        </div>\n        <div v-if=\"currentQuestion.options && currentQuestion.options.length > 0\" class=\"detail-item\">\n          <label>选项：</label>\n          <div class=\"options\">\n            <div\n              v-for=\"(option, index) in currentQuestion.options\"\n              :key=\"index\"\n              class=\"option\"\n            >\n              {{ String.fromCharCode(65 + index) }}. {{ option.content }}\n            </div>\n          </div>\n        </div>\n        <div class=\"detail-item\">\n          <label>正确答案：</label>\n          <div class=\"answer\">{{ currentQuestion.correct_answer }}</div>\n        </div>\n        <div v-if=\"currentQuestion.explanation\" class=\"detail-item\">\n          <label>题目解析：</label>\n          <div class=\"explanation\">{{ currentQuestion.explanation }}</div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionList',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        question_type: '',\n        difficulty: '',\n        category_id: '',\n        status: ''\n      },\n      categoryOptions: [],\n      viewDialogVisible: false,\n      currentQuestion: null,\n      typeTagMap: {\n        single_choice: 'primary',\n        multiple_choice: 'success',\n        true_false: 'warning',\n        fill_blank: 'info',\n        short_answer: 'danger',\n        essay: 'danger'\n      },\n      typeTextMap: {\n        single_choice: '单选题',\n        multiple_choice: '多选题',\n        true_false: '判断题',\n        fill_blank: '填空题',\n        short_answer: '简答题',\n        essay: '论述题'\n      },\n      difficultyTagMap: {\n        easy: 'success',\n        medium: 'warning',\n        hard: 'danger'\n      },\n      difficultyTextMap: {\n        easy: '简单',\n        medium: '中等',\n        hard: '困难'\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.loadCategoryOptions()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n\n      // 模拟题目数据\n      setTimeout(() => {\n        this.list = [\n          // 马克思主义基本原理 - 哲学部分\n          {\n            question_id: 1,\n            question_content: '马克思主义哲学的基本问题是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '物质和意识的关系问题' },\n              { content: '理论和实践的关系问题' },\n              { content: '个人和社会的关系问题' },\n              { content: '自由和必然的关系问题' }\n            ],\n            correct_answer: 'A',\n            explanation: '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。它包括两个方面：第一，物质和意识何者为第一性；第二，物质和意识是否具有同一性。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 2,\n            question_content: '物质的唯一特性是（）',\n            question_type: 'single_choice',\n            difficulty: 'easy',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 16, tag_name: '2023年真题' }\n            ],\n            options: [\n              { content: '运动性' },\n              { content: '客观实在性' },\n              { content: '可知性' },\n              { content: '绝对性' }\n            ],\n            correct_answer: 'B',\n            explanation: '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在，并能为人的意识所反映。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 3,\n            question_content: '意识的本质是（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 1112,\n            category_name: '意识本质',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '意识是人脑的机能' },\n              { content: '意识是客观存在的反映' },\n              { content: '意识是社会的产物' },\n              { content: '意识具有主观能动性' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '意识的本质包括：（1）意识是人脑的机能；（2）意识是客观存在的反映；（3）意识是社会的产物；（4）意识具有主观能动性。这四个方面构成了意识本质的完整内容。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 4,\n            question_content: '马克思主义哲学认为，世界的真正统一性在于它的物质性。',\n            question_type: 'true_false',\n            difficulty: 'easy',\n            category_id: 1111,\n            category_name: '物质概念',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' }\n            ],\n            options: [],\n            correct_answer: 'true',\n            explanation: '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。这是马克思主义一元论的基本观点，强调世界万物都是物质的不同表现形式。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 5,\n            question_content: '联系的特点包括（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 1121,\n            category_name: '联系观',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '客观性' },\n              { content: '普遍性' },\n              { content: '多样性' },\n              { content: '条件性' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '联系具有客观性、普遍性、多样性和条件性等特点。客观性指联系是事物本身所固有的；普遍性指任何事物都处在联系之中；多样性指联系的形式多种多样；条件性指联系是有条件的。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 6,\n            question_content: '矛盾的基本属性是_____和_____。',\n            question_type: 'fill_blank',\n            difficulty: 'easy',\n            category_id: 1123,\n            category_name: '矛盾规律',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [],\n            correct_answer: '同一性；斗争性',\n            explanation: '矛盾的基本属性是同一性和斗争性。同一性是指矛盾双方相互依存、相互贯通的性质和趋势；斗争性是指矛盾双方相互排斥、相互对立的性质和趋势。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          {\n            question_id: 7,\n            question_content: '实践是认识的基础，主要表现在（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 113,\n            category_name: '认识论',\n            tags: [\n              { tag_id: 1, tag_name: '马克思主义基本原理' },\n              { tag_id: 9, tag_name: '哲学原理' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [\n              { content: '实践是认识的来源' },\n              { content: '实践是认识发展的动力' },\n              { content: '实践是检验认识真理性的唯一标准' },\n              { content: '实践是认识的目的' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '实践是认识的基础，主要表现在四个方面：（1）实践是认识的来源；（2）实践是认识发展的动力；（3）实践是检验认识真理性的唯一标准；（4）实践是认识的目的。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-15'\n          },\n          // 毛泽东思想部分\n          {\n            question_id: 8,\n            question_content: '毛泽东思想形成的时代背景是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '帝国主义和无产阶级革命的时代' },\n              { content: '资本主义向社会主义过渡的时代' },\n              { content: '和平与发展的时代' },\n              { content: '全球化的时代' }\n            ],\n            correct_answer: 'A',\n            explanation: '毛泽东思想形成的时代背景是帝国主义和无产阶级革命的时代。这一时代特征为毛泽东思想的形成和发展提供了重要的历史条件。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          {\n            question_id: 9,\n            question_content: '新民主主义革命的总路线是（）',\n            question_type: 'single_choice',\n            difficulty: 'hard',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [\n              { content: '无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命' },\n              { content: '工人阶级领导的，以工农联盟为基础的人民民主专政' },\n              { content: '中国共产党领导的多党合作和政治协商制度' },\n              { content: '人民当家作主的社会主义民主政治' }\n            ],\n            correct_answer: 'A',\n            explanation: '新民主主义革命的总路线是：无产阶级领导的，人民大众的，反对帝国主义、封建主义和官僚资本主义的革命。这是毛泽东对新民主主义革命本质的科学概括。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          {\n            question_id: 10,\n            question_content: '中国革命的基本问题是（）',\n            question_type: 'single_choice',\n            difficulty: 'medium',\n            category_id: 21,\n            category_name: '毛泽东思想',\n            tags: [\n              { tag_id: 2, tag_name: '毛泽东思想' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 17, tag_name: '2022年真题' }\n            ],\n            options: [\n              { content: '农民问题' },\n              { content: '武装斗争问题' },\n              { content: '统一战线问题' },\n              { content: '党的建设问题' }\n            ],\n            correct_answer: 'A',\n            explanation: '中国革命的基本问题是农民问题。毛泽东指出，农民问题乃国民革命的中心问题，农民是中国革命的主力军，农民问题是中国革命的基本问题。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-16'\n          },\n          // 中国近现代史纲要部分\n          {\n            question_id: 11,\n            question_content: '中国近代史的起点是（）',\n            question_type: 'single_choice',\n            difficulty: 'easy',\n            category_id: 31,\n            category_name: '旧民主主义革命时期',\n            tags: [\n              { tag_id: 3, tag_name: '中国近现代史纲要' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '1840年鸦片战争' },\n              { content: '1842年《南京条约》签订' },\n              { content: '1851年太平天国运动' },\n              { content: '1894年甲午中日战争' }\n            ],\n            correct_answer: 'A',\n            explanation: '中国近代史的起点是1840年鸦片战争。鸦片战争是中国历史的转折点，标志着中国开始沦为半殖民地半封建社会，中国近代史由此开始。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-17'\n          },\n          {\n            question_id: 12,\n            question_content: '五四运动的历史意义包括（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 32,\n            category_name: '新民主主义革命时期',\n            tags: [\n              { tag_id: 3, tag_name: '中国近现代史纲要' },\n              { tag_id: 12, tag_name: '新民主主义革命' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '是中国新民主主义革命的开端' },\n              { content: '促进了马克思主义在中国的传播' },\n              { content: '促进了马克思主义与中国工人运动的结合' },\n              { content: '为中国共产党的成立作了思想和干部准备' }\n            ],\n            correct_answer: 'A,B,C,D',\n            explanation: '五四运动的历史意义重大：（1）是中国新民主主义革命的开端；（2）促进了马克思主义在中国的传播；（3）促进了马克思主义与中国工人运动的结合；（4）为中国共产党的成立作了思想和干部准备。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-17'\n          },\n          // 思想道德与法治部分\n          {\n            question_id: 13,\n            question_content: '理想信念的作用主要表现在（）',\n            question_type: 'multiple_choice',\n            difficulty: 'medium',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [\n              { content: '指引人生的奋斗目标' },\n              { content: '提供人生的前进动力' },\n              { content: '提高人生的精神境界' },\n              { content: '增强人生的使命感' }\n            ],\n            correct_answer: 'A,B,C',\n            explanation: '理想信念的作用主要表现在三个方面：（1）指引人生的奋斗目标；（2）提供人生的前进动力；（3）提高人生的精神境界。理想信念是人生的精神支柱和前进动力。',\n            score: 2,\n            status: 'active',\n            created_at: '2024-01-18'\n          },\n          {\n            question_id: 14,\n            question_content: '社会主义核心价值观的基本内容是_____、_____、_____。',\n            question_type: 'fill_blank',\n            difficulty: 'easy',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 15, tag_name: '新时代' },\n              { tag_id: 7, tag_name: '高频考点' }\n            ],\n            options: [],\n            correct_answer: '富强、民主、文明、和谐；自由、平等、公正、法治；爱国、敬业、诚信、友善',\n            explanation: '社会主义核心价值观的基本内容是：国家层面的价值要求是富强、民主、文明、和谐；社会层面的价值要求是自由、平等、公正、法治；个人层面的价值要求是爱国、敬业、诚信、友善。',\n            score: 3,\n            status: 'active',\n            created_at: '2024-01-18'\n          },\n          {\n            question_id: 15,\n            question_content: '请论述新时代爱国主义的基本要求。',\n            question_type: 'essay',\n            difficulty: 'hard',\n            category_id: 4,\n            category_name: '思想道德与法治',\n            tags: [\n              { tag_id: 4, tag_name: '思想道德与法治' },\n              { tag_id: 15, tag_name: '新时代' },\n              { tag_id: 6, tag_name: '重点难点' }\n            ],\n            options: [],\n            correct_answer: '新时代爱国主义的基本要求包括：（1）坚持爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）坚持立足民族又面向世界。新时代的爱国主义必须坚持爱国和爱党、爱社会主义相统一，这是当代中国爱国主义精神最重要的体现。',\n            explanation: '这道题考查新时代爱国主义的基本要求。答题要点：（1）爱国和爱党、爱社会主义相统一；（2）维护祖国统一和民族团结；（3）尊重和传承中华民族历史和文化；（4）立足民族又面向世界。要结合新时代特点进行论述。',\n            score: 10,\n            status: 'active',\n            created_at: '2024-01-18'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n      }, 500)\n    },\n\n    loadCategoryOptions() {\n      // 模拟分类选项（扁平化）\n      this.categoryOptions = [\n        { category_id: 111, category_name: '唯物论' },\n        { category_id: 112, category_name: '辩证法' },\n        { category_id: 113, category_name: '认识论' },\n        { category_id: 12, category_name: '马克思主义政治经济学' },\n        { category_id: 13, category_name: '科学社会主义' },\n        { category_id: 21, category_name: '毛泽东思想' },\n        { category_id: 22, category_name: '邓小平理论' },\n        { category_id: 23, category_name: '三个代表重要思想' },\n        { category_id: 31, category_name: '旧民主主义革命时期' },\n        { category_id: 32, category_name: '新民主主义革命时期' },\n        { category_id: 33, category_name: '社会主义革命和建设时期' }\n      ]\n    },\n\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n\n    handleCreate() {\n      this.$router.push('/questions/add')\n    },\n\n    handleImport() {\n      this.$router.push('/questions/upload')\n    },\n\n    handleUpdate(row) {\n      this.$router.push(`/questions/edit/${row.question_id}`)\n    },\n\n    handleView(row) {\n      this.currentQuestion = row\n      this.viewDialogVisible = true\n    },\n\n    handleDelete(row) {\n      this.$confirm('确定要删除该题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n\n    .question-content {\n      line-height: 1.5;\n      word-break: break-word;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n\n.question-detail {\n  .detail-item {\n    margin-bottom: 20px;\n\n    label {\n      font-weight: bold;\n      color: #303133;\n      margin-bottom: 8px;\n      display: block;\n    }\n\n    .content,\n    .answer,\n    .explanation {\n      background: #f5f7fa;\n      padding: 12px;\n      border-radius: 4px;\n      line-height: 1.6;\n    }\n\n    .options {\n      .option {\n        padding: 8px 12px;\n        margin-bottom: 8px;\n        background: #f5f7fa;\n        border-radius: 4px;\n        border-left: 3px solid #409eff;\n      }\n    }\n  }\n}\n</style>\n"]}]}