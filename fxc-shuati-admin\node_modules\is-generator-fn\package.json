{"_from": "is-generator-fn@^2.0.0", "_id": "is-generator-fn@2.1.0", "_inBundle": false, "_integrity": "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==", "_location": "/is-generator-fn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-generator-fn@^2.0.0", "name": "is-generator-fn", "escapedName": "is-generator-fn", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/jest-jasmine2"], "_resolved": "https://registry.npmmirror.com/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "_shasum": "7d140adc389aaf3011a8f2a2a4cfa6faadffb118", "_spec": "is-generator-fn@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\jest-jasmine2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-generator-fn/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if something is a generator function", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-generator-fn#readme", "keywords": ["generator", "function", "func", "fn", "is", "check", "detect", "yield", "type"], "license": "MIT", "name": "is-generator-fn", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-generator-fn.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.1.0"}