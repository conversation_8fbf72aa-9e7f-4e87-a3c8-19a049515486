{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\settings.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\system\\settings.vue", "mtime": 1752568908391}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3lzdGVtU2V0dGluZ3MnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4ge307CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "data"], "sources": ["src/views/system/settings.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"placeholder-content\">\n      <el-card>\n        <div slot=\"header\">\n          <span>系统配置</span>\n        </div>\n        <div class=\"placeholder-body\">\n          <el-alert\n            title=\"功能开发中\"\n            type=\"info\"\n            description=\"系统配置功能正在开发中，敬请期待...\"\n            show-icon\n            :closable=\"false\"\n          />\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SystemSettings',\n  data() {\n    return {}\n  }\n}\n</script>\n\n<style scoped>\n.placeholder-content {\n  min-height: 400px;\n}\n.placeholder-body {\n  padding: 20px 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}