const express = require('express');
const { query } = require('../config/database');
const { authenticateAdmin } = require('./auth');

const router = express.Router();

// Mock 选项数据
const options = [
  { option_id: 1, question_id: 1, option_content: "Paris", option_order: 1, is_correct: 1 },
  { option_id: 2, question_id: 1, option_content: "London", option_order: 2, is_correct: 0 },
  { option_id: 3, question_id: 1, option_content: "Berlin", option_order: 3, is_correct: 0 },
  { option_id: 4, question_id: 1, option_content: "Madrid", option_order: 4, is_correct: 0 },
  { option_id: 5, question_id: 3, option_content: "Paris", option_order: 1, is_correct: 1 },
  { option_id: 6, question_id: 3, option_content: "Lyon", option_order: 2, is_correct: 1 },
  { option_id: 7, question_id: 3, option_content: "Berlin", option_order: 3, is_correct: 0 },
  { option_id: 8, question_id: 3, option_content: "Marseille", option_order: 4, is_correct: 1 }
];

// 获取题目的选项
router.get('/question/:question_id', async (req, res) => {
  try {
    const questionId = parseInt(req.params.question_id);
    const questionOptions = options
      .filter(opt => opt.question_id === questionId)
      .sort((a, b) => a.option_order - b.option_order);

    res.json({
      success: true,
      data: questionOptions,
      message: '获取选项成功'
    });

  } catch (error) {
    console.error('获取选项错误:', error);
    res.status(500).json({
      success: false,
      message: '获取选项失败'
    });
  }
});

// 创建选项（管理员）
router.post('/', authenticateAdmin, async (req, res) => {
  try {
    const { question_id, option_content, option_order, is_correct = 0 } = req.body;

    if (!question_id || !option_content) {
      return res.status(400).json({
        success: false,
        message: '题目ID和选项内容不能为空'
      });
    }

    const newOption = {
      option_id: options.length + 1,
      question_id: parseInt(question_id),
      option_content,
      option_order: parseInt(option_order) || 1,
      is_correct: parseInt(is_correct),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    options.push(newOption);

    res.json({
      success: true,
      data: newOption,
      message: '创建选项成功'
    });

  } catch (error) {
    console.error('创建选项错误:', error);
    res.status(500).json({
      success: false,
      message: '创建选项失败'
    });
  }
});

module.exports = router;
