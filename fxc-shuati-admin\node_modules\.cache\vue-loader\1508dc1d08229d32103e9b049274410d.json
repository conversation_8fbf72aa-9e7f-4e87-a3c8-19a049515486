{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue?vue&type=template&id=25336f9d&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue", "mtime": 1752630804729}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}