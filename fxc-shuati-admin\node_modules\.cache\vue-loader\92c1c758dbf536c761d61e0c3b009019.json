{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue", "mtime": 1752627977905}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFBlcm1pc3Npb25MaXN0LCBjcmVhdGVQZXJtaXNzaW9uLCB1cGRhdGVQZXJtaXNzaW9uLCBkZWxldGVQZXJtaXNzaW9uIH0gZnJvbSAnQC9hcGkvdXNlcnMnCmltcG9ydCB3YXZlcyBmcm9tICdAL2RpcmVjdGl2ZS93YXZlcycKaW1wb3J0IHsgcGFyc2VUaW1lIH0gZnJvbSAnQC91dGlscycKaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1Blcm1pc3Npb25MaXN0JywKICBjb21wb25lbnRzOiB7IFBhZ2luYXRpb24gfSwKICBkaXJlY3RpdmVzOiB7IHdhdmVzIH0sCiAgZmlsdGVyczogewogICAgcGFyc2VUaW1lCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDUwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHBlcm1pc3Npb25fdHlwZTogJycsCiAgICAgICAgc3RhdHVzOiAnJwogICAgICB9LAogICAgICB0eXBlVGV4dE1hcDogewogICAgICAgIG1lbnU6ICfoj5zljZUnLAogICAgICAgIGJ1dHRvbjogJ+aMiemSricsCiAgICAgICAgYXBpOiAn5o6l5Y+jJwogICAgICB9LAogICAgICB0eXBlVGFnTWFwOiB7CiAgICAgICAgbWVudTogJ3ByaW1hcnknLAogICAgICAgIGJ1dHRvbjogJ3N1Y2Nlc3MnLAogICAgICAgIGFwaTogJ3dhcm5pbmcnCiAgICAgIH0sCiAgICAgIHBhcmVudE9wdGlvbnM6IFtdLAogICAgICB0ZW1wOiB7CiAgICAgICAgcGVybWlzc2lvbl9pZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICBwZXJtaXNzaW9uX25hbWU6ICcnLAogICAgICAgIHBlcm1pc3Npb25fY29kZTogJycsCiAgICAgICAgcGVybWlzc2lvbl90eXBlOiAnbWVudScsCiAgICAgICAgcGVybWlzc2lvbl91cmw6ICcnLAogICAgICAgIHBlcm1pc3Npb25faWNvbjogJycsCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICBzb3J0X29yZGVyOiAwCiAgICAgIH0sCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nU3RhdHVzOiAnJywKICAgICAgdGV4dE1hcDogewogICAgICAgIHVwZGF0ZTogJ+e8lui+keadg+mZkCcsCiAgICAgICAgY3JlYXRlOiAn5re75Yqg5p2D6ZmQJwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIHBlcm1pc3Npb25fbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmnYPpmZDlkI3np7DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgcGVybWlzc2lvbl9jb2RlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+adg+mZkOe8lueggeS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9XSwKICAgICAgICBwZXJtaXNzaW9uX3R5cGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5p2D6ZmQ57G75Z6L5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2NoYW5nZScgfV0KICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZQogICAgICBnZXRQZXJtaXNzaW9uTGlzdCh0aGlzLmxpc3RRdWVyeSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWwKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2UKICAgICAgICAKICAgICAgICAvLyDmm7TmlrDniLbmnYPpmZDpgInpobkKICAgICAgICB0aGlzLnBhcmVudE9wdGlvbnMgPSB0aGlzLmxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5wZXJtaXNzaW9uX3R5cGUgPT09ICdtZW51JykKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVGaWx0ZXIoKSB7CiAgICAgIHRoaXMubGlzdFF1ZXJ5LnBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgcmVzZXRUZW1wKCkgewogICAgICB0aGlzLnRlbXAgPSB7CiAgICAgICAgcGVybWlzc2lvbl9pZDogdW5kZWZpbmVkLAogICAgICAgIHBhcmVudF9pZDogMCwKICAgICAgICBwZXJtaXNzaW9uX25hbWU6ICcnLAogICAgICAgIHBlcm1pc3Npb25fY29kZTogJycsCiAgICAgICAgcGVybWlzc2lvbl90eXBlOiAnbWVudScsCiAgICAgICAgcGVybWlzc2lvbl91cmw6ICcnLAogICAgICAgIHBlcm1pc3Npb25faWNvbjogJycsCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICBzb3J0X29yZGVyOiAwCiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVDcmVhdGUoKSB7CiAgICAgIHRoaXMucmVzZXRUZW1wKCkKICAgICAgdGhpcy5kaWFsb2dTdGF0dXMgPSAnY3JlYXRlJwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS5jbGVhclZhbGlkYXRlKCkKICAgICAgfSkKICAgIH0sCiAgICBjcmVhdGVEYXRhKCkgewogICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgY3JlYXRlUGVybWlzc2lvbih0aGlzLnRlbXApLnRoZW4oKCkgPT4gewogICAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIm+W7uuaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMudGVtcCA9IE9iamVjdC5hc3NpZ24oe30sIHJvdykKICAgICAgdGhpcy5kaWFsb2dTdGF0dXMgPSAndXBkYXRlJwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS5jbGVhclZhbGlkYXRlKCkKICAgICAgfSkKICAgIH0sCiAgICB1cGRhdGVEYXRhKCkgewogICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgY29uc3QgdGVtcERhdGEgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnRlbXApCiAgICAgICAgICB1cGRhdGVQZXJtaXNzaW9uKHRlbXBEYXRhLnBlcm1pc3Npb25faWQsIHRlbXBEYXRhKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfmm7TmlrDmiJDlip8nLAogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTor6XmnYPpmZDlkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgZGVsZXRlUGVybWlzc2lvbihyb3cucGVybWlzc2lvbl9pZCkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8nLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB9KQogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["permissions.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoLA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "permissions.vue", "sourceRoot": "src/views/users", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索权限名称、编码\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.permission_type\"\n          placeholder=\"权限类型\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"菜单\" value=\"menu\" />\n          <el-option label=\"按钮\" value=\"button\" />\n          <el-option label=\"接口\" value=\"api\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加权限\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"permission_id\"\n      default-expand-all\n    >\n      <el-table-column label=\"ID\" prop=\"permission_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"权限名称\" prop=\"permission_name\" min-width=\"150\" />\n      <el-table-column label=\"权限编码\" prop=\"permission_code\" min-width=\"180\" />\n      <el-table-column label=\"权限类型\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"typeTagMap[row.permission_type]\">\n            {{ typeTextMap[row.permission_type] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"权限URL\" prop=\"permission_url\" min-width=\"180\" />\n      <el-table-column label=\"权限图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.permission_icon\" :class=\"row.permission_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"父权限\" prop=\"parent_name\" min-width=\"150\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父权限\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父权限\" style=\"width: 100%\">\n            <el-option label=\"顶级权限\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.permission_id\"\n              :label=\"item.permission_name\"\n              :value=\"item.permission_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限名称\" prop=\"permission_name\">\n          <el-input v-model=\"temp.permission_name\" />\n        </el-form-item>\n        <el-form-item label=\"权限编码\" prop=\"permission_code\">\n          <el-input v-model=\"temp.permission_code\" />\n        </el-form-item>\n        <el-form-item label=\"权限类型\" prop=\"permission_type\">\n          <el-select v-model=\"temp.permission_type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\n            <el-option label=\"菜单\" value=\"menu\" />\n            <el-option label=\"按钮\" value=\"button\" />\n            <el-option label=\"接口\" value=\"api\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限URL\" prop=\"permission_url\">\n          <el-input v-model=\"temp.permission_url\" />\n        </el-form-item>\n        <el-form-item label=\"权限图标\" prop=\"permission_icon\">\n          <el-input v-model=\"temp.permission_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPermissionList, createPermission, updatePermission, deletePermission } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'PermissionList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        permission_type: '',\n        status: ''\n      },\n      typeTextMap: {\n        menu: '菜单',\n        button: '按钮',\n        api: '接口'\n      },\n      typeTagMap: {\n        menu: 'primary',\n        button: 'success',\n        api: 'warning'\n      },\n      parentOptions: [],\n      temp: {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑权限',\n        create: '添加权限'\n      },\n      rules: {\n        permission_name: [{ required: true, message: '权限名称不能为空', trigger: 'blur' }],\n        permission_code: [{ required: true, message: '权限编码不能为空', trigger: 'blur' }],\n        permission_type: [{ required: true, message: '权限类型不能为空', trigger: 'change' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getPermissionList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n        \n        // 更新父权限选项\n        this.parentOptions = this.list.filter(item => item.permission_type === 'menu')\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createPermission(this.temp).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          updatePermission(tempData.permission_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该权限吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deletePermission(row.permission_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}