{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\add.vue?vue&type=template&id=4f9629b5", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\add.vue", "mtime": 1752629871992}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}