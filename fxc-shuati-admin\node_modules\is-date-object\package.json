{"_from": "is-date-object@^1.0.5", "_id": "is-date-object@1.1.0", "_inBundle": false, "_integrity": "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==", "_location": "/is-date-object", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-date-object@^1.0.5", "name": "is-date-object", "escapedName": "is-date-object", "rawSpec": "^1.0.5", "saveSpec": null, "fetchSpec": "^1.0.5"}, "_requiredBy": ["/deep-equal", "/es-to-primitive", "/which-builtin-type"], "_resolved": "https://registry.npmmirror.com/is-date-object/-/is-date-object-1.1.0.tgz", "_shasum": "ad85541996fc7aa8b2729701d27b7319f95d82f7", "_spec": "is-date-object@^1.0.5", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\es-to-primitive", "author": {"name": "<PERSON>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-date-object/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "deprecated": false, "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/core-js": "^2.5.8", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "core-js": "^3.39.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "indexof": "^0.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "^5.8.0-dev.20241212"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-date-object#readme", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "license": "MIT", "main": "index.js", "name": "is-date-object", "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-date-object.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:corejs", "test:corejs": "nyc tape test-corejs.js", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.0"}