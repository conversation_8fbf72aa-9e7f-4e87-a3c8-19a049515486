{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\SvgIcon\\index.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KCi8vIGRvYzogaHR0cHM6Ly9wYW5qaWFjaGVuLmdpdGh1Yi5pby92dWUtZWxlbWVudC1hZG1pbi1zaXRlL2ZlYXR1cmUvY29tcG9uZW50L3N2Zy1pY29uLmh0bWwjdXNhZ2UKaW1wb3J0IHsgaXNFeHRlcm5hbCB9IGZyb20gJ0AvdXRpbHMvdmFsaWRhdGUnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1N2Z0ljb24nLAogIHByb3BzOiB7CiAgICBpY29uQ2xhc3M6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIGNsYXNzTmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgaXNFeHRlcm5hbCgpIHsKICAgICAgcmV0dXJuIGlzRXh0ZXJuYWwodGhpcy5pY29uQ2xhc3MpCiAgICB9LAogICAgaWNvbk5hbWUoKSB7CiAgICAgIHJldHVybiBgI2ljb24tJHt0aGlzLmljb25DbGFzc31gCiAgICB9LAogICAgc3ZnQ2xhc3MoKSB7CiAgICAgIGlmICh0aGlzLmNsYXNzTmFtZSkgewogICAgICAgIHJldHVybiAnc3ZnLWljb24gJyArIHRoaXMuY2xhc3NOYW1lCiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICdzdmctaWNvbicKICAgICAgfQogICAgfSwKICAgIHN0eWxlRXh0ZXJuYWxJY29uKCkgewogICAgICByZXR1cm4gewogICAgICAgIG1hc2s6IGB1cmwoJHt0aGlzLmljb25DbGFzc30pIG5vLXJlcGVhdCA1MCUgNTAlYCwKICAgICAgICAnLXdlYmtpdC1tYXNrJzogYHVybCgke3RoaXMuaWNvbkNsYXNzfSkgbm8tcmVwZWF0IDUwJSA1MCVgCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;AAQA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SvgIcon", "sourcesContent": ["<template>\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\n// doc: https://panjiachen.github.io/vue-element-admin-site/feature/component/svg-icon.html#usage\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.iconClass)\n    },\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    },\n    styleExternalIcon() {\n      return {\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n.svg-external-icon {\n  background-color: currentColor;\n  mask-size: cover!important;\n  display: inline-block;\n}\n</style>\n"]}]}