<!--index.wxml-->
<view class="container">
  <!-- 顶部轮播图 -->
  <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="1000">
    <swiper-item wx:for="{{banners}}" wx:key="index">
      <view class="banner-item" style="background-color: {{item.color}};">
        <text class="banner-title">{{item.title}}</text>
        <text class="banner-desc">{{item.desc}}</text>
      </view>
    </swiper-item>
  </swiper>

  <!-- 功能菜单 -->
  <view class="menu-section card">
    <view class="menu-grid">
      <view class="menu-item" bindtap="goToPractice">
        <view class="menu-icon practice-icon">📚</view>
        <text class="menu-text">开始练习</text>
      </view>
      <view class="menu-item" bindtap="goToFavorites">
        <view class="menu-icon favorite-icon">❤️</view>
        <text class="menu-text">我的收藏</text>
      </view>
      <view class="menu-item" bindtap="goToErrors">
        <view class="menu-icon error-icon">❌</view>
        <text class="menu-text">错题本</text>
      </view>
      <view class="menu-item" bindtap="goToProfile">
        <view class="menu-icon profile-icon">👤</view>
        <text class="menu-text">个人中心</text>
      </view>
    </view>
  </view>

  <!-- 学习统计 -->
  <view class="stats-section card">
    <view class="section-title">我的学习统计</view>
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalQuestions}}</text>
        <text class="stats-label">已练习题目</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.correctRate}}%</text>
        <text class="stats-label">正确率</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.studyDays}}</text>
        <text class="stats-label">学习天数</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.favoriteCount}}</text>
        <text class="stats-label">收藏题目</text>
      </view>
    </view>
  </view>

  <!-- 快速开始 -->
  <view class="quick-start-section card">
    <view class="section-title">快速开始</view>
    <view class="quick-start-buttons">
      <button class="quick-btn primary" bindtap="startRandomPractice">
        <text class="btn-icon">🎲</text>
        <text class="btn-text">随机练习</text>
      </button>
      <button class="quick-btn secondary" bindtap="goToPractice">
        <text class="btn-icon">📖</text>
        <text class="btn-text">分类练习</text>
      </button>
    </view>
  </view>

  <!-- 最近练习 -->
  <view class="recent-section card">
    <view class="section-title">最近练习</view>
    <view class="recent-list" wx:if="{{recentPractice.length > 0}}">
      <view wx:for="{{recentPractice}}" wx:key="id" class="recent-item" bindtap="continueQuestion" data-item="{{item}}">
        <view class="recent-info">
          <text class="recent-title">{{item.title}}</text>
          <text class="recent-desc">{{item.description}}</text>
        </view>
        <view class="recent-progress">
          <text class="progress-text">{{item.progress}}%</text>
          <progress percent="{{item.progress}}" stroke-width="4" activeColor="#409EFF" backgroundColor="#f0f0f0" />
        </view>
      </view>
    </view>
    <view class="empty-state" wx:else>
      <text class="empty-text">暂无练习记录</text>
      <text class="empty-desc">开始你的第一次练习吧！</text>
      <button class="start-btn" bindtap="startRandomPractice">立即开始</button>
    </view>
  </view>
</view>
