# 刷题小程序 - 微信小程序版

一个专业的考研英语刷题平台，使用微信小程序原生开发。

## 项目特色

- 🎯 **原生开发** - 使用微信小程序原生技术栈，性能优异
- 📱 **响应式设计** - 适配各种屏幕尺寸，用户体验佳
- 🎨 **现代UI** - 简洁美观的界面设计，符合微信设计规范
- 🚀 **功能完整** - 涵盖练习、收藏、错题本、个人中心等核心功能
- 💾 **本地存储** - 支持离线数据存储，无网络也能查看历史记录

## 功能模块

### 📚 首页
- 轮播图展示
- 快捷功能入口
- 学习统计概览
- 最近练习记录

### 🎯 题库练习
- 分类浏览题目
- 搜索功能
- 推荐练习
- 快速练习模式

### ❓ 答题页面
- 支持多种题型（单选、多选、判断、填空、简答）
- 实时计时
- 答案解析
- 收藏功能

### 📊 结果页面
- 详细答题统计
- 错题回顾
- 成就系统
- 学习建议

### ❤️ 我的收藏
- 收藏题目管理
- 分类筛选
- 批量操作
- 收藏练习

### ❌ 错题本
- 错题统计分析
- 错题分类管理
- 重做功能
- 学习建议

### 👤 个人中心
- 用户信息管理
- 学习数据统计
- 学习进度图表
- 设置功能

## 技术栈

- **框架**: 微信小程序原生框架
- **样式**: WXSS + CSS Grid/Flexbox
- **数据**: 本地存储 + Mock数据
- **工具**: 微信开发者工具

## 项目结构

```
fxc-shuati-weixin/
├── app.js                 # 小程序入口文件
├── app.json               # 小程序配置文件
├── app.wxss               # 全局样式文件
├── sitemap.json           # 站点地图
├── project.config.json    # 项目配置
├── pages/                 # 页面目录
│   ├── index/            # 首页
│   ├── practice/         # 练习页
│   ├── question/         # 答题页
│   ├── result/           # 结果页
│   ├── favorites/        # 收藏页
│   ├── errors/           # 错题本
│   └── profile/          # 个人中心
└── utils/                # 工具函数
    ├── util.js           # 通用工具
    ├── api.js            # API封装
    └── mock.js           # 模拟数据
```

## 开发指南

### 环境要求

- 微信开发者工具 1.06.0+
- Node.js 16.0+（可选，用于代码格式化等）

### 快速开始

1. **下载代码**
   ```bash
   git clone [项目地址]
   cd fxc-shuati-weixin
   ```

2. **打开项目**
   - 启动微信开发者工具
   - 选择"导入项目"
   - 选择项目目录
   - 填写AppID（测试可使用测试号）

3. **预览调试**
   - 点击"编译"按钮
   - 在模拟器中预览效果
   - 使用真机调试测试功能

### 配置说明

#### app.json 主要配置
- `pages`: 页面路径配置
- `window`: 窗口表现配置
- `tabBar`: 底部标签栏配置
- `networkTimeout`: 网络超时配置

#### 样式规范
- 使用rpx作为尺寸单位
- 遵循微信设计规范
- 支持深色模式适配
- 响应式布局设计

### 数据接口

当前使用Mock数据，生产环境需要对接真实API：

```javascript
// utils/api.js 中的接口示例
const api = {
  // 获取题目列表
  getQuestions: (params) => request({ url: '/questions', data: params }),
  
  // 提交答案
  submitAnswer: (id, data) => request({ url: `/questions/${id}/answer`, method: 'POST', data }),
  
  // 获取收藏列表
  getFavorites: (params) => request({ url: '/favorites', data: params }),
  
  // 更多接口...
}
```

### 本地存储

使用微信小程序的本地存储API：

```javascript
// 存储数据
wx.setStorageSync('key', value)

// 获取数据
const value = wx.getStorageSync('key')

// 删除数据
wx.removeStorageSync('key')
```

## 部署发布

### 测试环境
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核

### 生产环境
1. 配置正式的AppID和AppSecret
2. 对接生产环境API
3. 配置服务器域名白名单
4. 提交微信审核发布

## 功能扩展

### 计划功能
- [ ] 语音答题
- [ ] 社区讨论
- [ ] 学习计划
- [ ] 排行榜
- [ ] 学习报告
- [ ] 消息推送

### 技术优化
- [ ] 图片懒加载
- [ ] 数据缓存策略
- [ ] 性能监控
- [ ] 错误上报
- [ ] 用户行为分析

## 注意事项

1. **网络请求**: 需要在微信公众平台配置合法域名
2. **用户授权**: 获取用户信息需要用户主动授权
3. **存储限制**: 本地存储有10MB限制
4. **审核规范**: 需要遵循微信小程序审核规范
5. **性能优化**: 注意包体积大小，建议使用分包加载

## 联系方式

如有问题或建议，请联系：
- 邮箱: [<EMAIL>]
- 微信: [your-wechat-id]

## 许可证

MIT License
