{"_from": "is-path-cwd@^2.0.0", "_id": "is-path-cwd@2.2.0", "_inBundle": false, "_integrity": "sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==", "_location": "/is-path-cwd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-path-cwd@^2.0.0", "name": "is-path-cwd", "escapedName": "is-path-cwd", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/del"], "_resolved": "https://registry.npmmirror.com/is-path-cwd/-/is-path-cwd-2.2.0.tgz", "_shasum": "67d43b82664a7b5191fd9119127eb300048a9fdb", "_spec": "is-path-cwd@^2.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\del", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-path-cwd/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a path is the current working directory", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-path-cwd#readme", "keywords": ["path", "cwd", "pwd", "check", "filepath", "file", "folder"], "license": "MIT", "name": "is-path-cwd", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-path-cwd.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.2.0"}