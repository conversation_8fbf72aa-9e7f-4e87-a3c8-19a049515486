{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzRXh0ZXJuYWwgfSBmcm9tICdAL3V0aWxzL3ZhbGlkYXRlJwoKZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICB0bzogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgaXNFeHRlcm5hbCgpIHsKICAgICAgcmV0dXJuIGlzRXh0ZXJuYWwodGhpcy50bykKICAgIH0sCiAgICB0eXBlKCkgewogICAgICBpZiAodGhpcy5pc0V4dGVybmFsKSB7CiAgICAgICAgcmV0dXJuICdhJwogICAgICB9CiAgICAgIHJldHVybiAncm91dGVyLWxpbmsnCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBsaW5rUHJvcHModG8pIHsKICAgICAgaWYgKHRoaXMuaXNFeHRlcm5hbCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBocmVmOiB0bywKICAgICAgICAgIHRhcmdldDogJ19ibGFuaycsCiAgICAgICAgICByZWw6ICdub29wZW5lcicKICAgICAgICB9CiAgICAgIH0KICAgICAgcmV0dXJuIHsKICAgICAgICB0bzogdG8KICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["Link.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Link.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\n    <slot />\n  </component>\n</template>\n\n<script>\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  props: {\n    to: {\n      type: String,\n      required: true\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.to)\n    },\n    type() {\n      if (this.isExternal) {\n        return 'a'\n      }\n      return 'router-link'\n    }\n  },\n  methods: {\n    linkProps(to) {\n      if (this.isExternal) {\n        return {\n          href: to,\n          target: '_blank',\n          rel: 'noopener'\n        }\n      }\n      return {\n        to: to\n      }\n    }\n  }\n}\n</script>\n"]}]}