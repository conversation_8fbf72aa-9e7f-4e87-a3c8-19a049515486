{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue?vue&type=style&index=0&id=f497745a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\permissions.vue", "mtime": 1752572132189}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHAtY29udGFpbmVyIHsKICAuZmlsdGVyLWNhcmQgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIC5maWx0ZXItY29udGFpbmVyIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZmxleC13cmFwOiB3cmFwOwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAuZmlsdGVyLWl0ZW0gewogICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICB9CiAgICB9CiAgfQogIC5lbC10YWJsZSB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogIH0KICAucGFnaW5hdGlvbi1jb250YWluZXIgewogICAgcGFkZGluZzogMTVweCAwOwogIH0KfQo="}, {"version": 3, "sources": ["permissions.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "permissions.vue", "sourceRoot": "src/views/users", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索权限名称、编码\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.permission_type\"\n          placeholder=\"权限类型\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"菜单\" value=\"menu\" />\n          <el-option label=\"按钮\" value=\"button\" />\n          <el-option label=\"接口\" value=\"api\" />\n        </el-select>\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加权限\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"permission_id\"\n      default-expand-all\n    >\n      <el-table-column label=\"ID\" prop=\"permission_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"权限名称\" prop=\"permission_name\" width=\"150\" />\n      <el-table-column label=\"权限编码\" prop=\"permission_code\" width=\"180\" />\n      <el-table-column label=\"权限类型\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"typeTagMap[row.permission_type]\">\n            {{ typeTextMap[row.permission_type] }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"权限URL\" prop=\"permission_url\" width=\"180\" />\n      <el-table-column label=\"权限图标\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <i v-if=\"row.permission_icon\" :class=\"row.permission_icon\" />\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"父权限\" prop=\"parent_name\" width=\"150\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"120px\"\n        style=\"width: 450px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父权限\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父权限\" style=\"width: 100%\">\n            <el-option label=\"顶级权限\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.permission_id\"\n              :label=\"item.permission_name\"\n              :value=\"item.permission_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限名称\" prop=\"permission_name\">\n          <el-input v-model=\"temp.permission_name\" />\n        </el-form-item>\n        <el-form-item label=\"权限编码\" prop=\"permission_code\">\n          <el-input v-model=\"temp.permission_code\" />\n        </el-form-item>\n        <el-form-item label=\"权限类型\" prop=\"permission_type\">\n          <el-select v-model=\"temp.permission_type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\n            <el-option label=\"菜单\" value=\"menu\" />\n            <el-option label=\"按钮\" value=\"button\" />\n            <el-option label=\"接口\" value=\"api\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"权限URL\" prop=\"permission_url\">\n          <el-input v-model=\"temp.permission_url\" />\n        </el-form-item>\n        <el-form-item label=\"权限图标\" prop=\"permission_icon\">\n          <el-input v-model=\"temp.permission_icon\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPermissionList, createPermission, updatePermission, deletePermission } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'PermissionList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        permission_type: '',\n        status: ''\n      },\n      typeTextMap: {\n        menu: '菜单',\n        button: '按钮',\n        api: '接口'\n      },\n      typeTagMap: {\n        menu: 'primary',\n        button: 'success',\n        api: 'warning'\n      },\n      parentOptions: [],\n      temp: {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑权限',\n        create: '添加权限'\n      },\n      rules: {\n        permission_name: [{ required: true, message: '权限名称不能为空', trigger: 'blur' }],\n        permission_code: [{ required: true, message: '权限编码不能为空', trigger: 'blur' }],\n        permission_type: [{ required: true, message: '权限类型不能为空', trigger: 'change' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getPermissionList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n        \n        // 更新父权限选项\n        this.parentOptions = this.list.filter(item => item.permission_type === 'menu')\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        permission_id: undefined,\n        parent_id: 0,\n        permission_name: '',\n        permission_code: '',\n        permission_type: 'menu',\n        permission_url: '',\n        permission_icon: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createPermission(this.temp).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          updatePermission(tempData.permission_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该权限吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deletePermission(row.permission_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}