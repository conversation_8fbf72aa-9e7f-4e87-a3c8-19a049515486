{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\categories.vue?vue&type=style&index=0&id=1f907f90&lang=scss&scoped=true", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\categories.vue", "mtime": 1752630494570}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHAtY29udGFpbmVyIHsKICAuZmlsdGVyLWNhcmQgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIC5maWx0ZXItY29udGFpbmVyIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZmxleC13cmFwOiB3cmFwOwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAuZmlsdGVyLWl0ZW0gewogICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICB9CiAgICB9CiAgfQogIC5lbC10YWJsZSB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsKCiAgICAuZWwtdGFibGVfX2JvZHktd3JhcHBlciB7CiAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7CiAgICB9CiAgfQogIC5wYWdpbmF0aW9uLWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAxNXB4IDA7CiAgfQp9Cg=="}, {"version": 3, "sources": ["categories.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0kBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "categories.vue", "sourceRoot": "src/views/questions", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索分类名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"启用\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"disabled\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加分类\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"treeData\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n      row-key=\"category_id\"\n      default-expand-all\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column label=\"ID\" prop=\"category_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"分类名称\" prop=\"category_name\" min-width=\"200\" />\n      <el-table-column label=\"分类编码\" prop=\"category_code\" min-width=\"150\" />\n      <el-table-column label=\"分类描述\" prop=\"category_desc\" min-width=\"200\" />\n      <el-table-column label=\"题目数量\" prop=\"question_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ formatDate(row.created_at) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"父分类\" prop=\"parent_id\">\n          <el-select v-model=\"temp.parent_id\" placeholder=\"请选择父分类\" style=\"width: 100%\">\n            <el-option label=\"顶级分类\" :value=\"0\" />\n            <el-option\n              v-for=\"item in parentOptions\"\n              :key=\"item.category_id\"\n              :label=\"item.category_name\"\n              :value=\"item.category_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"分类名称\" prop=\"category_name\">\n          <el-input v-model=\"temp.category_name\" />\n        </el-form-item>\n        <el-form-item label=\"分类编码\" prop=\"category_code\">\n          <el-input v-model=\"temp.category_code\" />\n        </el-form-item>\n        <el-form-item label=\"分类描述\" prop=\"category_desc\">\n          <el-input v-model=\"temp.category_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport waves from '@/directive/waves'\nimport { formatDate } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'QuestionCategories',\n  components: { Pagination },\n  directives: { waves },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 50,\n        keyword: '',\n        status: ''\n      },\n      parentOptions: [],\n      temp: {\n        category_id: undefined,\n        parent_id: 0,\n        category_name: '',\n        category_code: '',\n        category_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑分类',\n        create: '添加分类'\n      },\n      rules: {\n        category_name: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],\n        category_code: [{ required: true, message: '分类编码不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  computed: {\n    treeData() {\n      // 将平铺数据转换为树形结构\n      const tree = []\n      const map = {}\n\n      // 先创建所有节点的映射\n      this.list.forEach(item => {\n        map[item.category_id] = { ...item, children: [] }\n      })\n\n      // 构建树形结构\n      this.list.forEach(item => {\n        if (item.parent_id === 0) {\n          // 顶级分类\n          tree.push(map[item.category_id])\n        } else {\n          // 子分类\n          if (map[item.parent_id]) {\n            map[item.parent_id].children.push(map[item.category_id])\n          }\n        }\n      })\n\n      return tree\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n\n      // 模拟考研政治分类数据\n      setTimeout(() => {\n        this.list = [\n          // 一级分类\n          {\n            category_id: 1,\n            parent_id: 0,\n            category_name: '马克思主义基本原理',\n            category_code: 'marxism',\n            category_desc: '马克思主义基本原理概论',\n            question_count: 245,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 2,\n            parent_id: 0,\n            category_name: '毛泽东思想和中国特色社会主义理论体系概论',\n            category_code: 'maoism',\n            category_desc: '毛泽东思想和中国特色社会主义理论体系概论',\n            question_count: 312,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 3,\n            parent_id: 0,\n            category_name: '中国近现代史纲要',\n            category_code: 'history',\n            category_desc: '中国近现代史纲要',\n            question_count: 198,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 4,\n            parent_id: 0,\n            category_name: '思想道德与法治',\n            category_code: 'morality',\n            category_desc: '思想道德与法治',\n            question_count: 167,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-01'\n          },\n          {\n            category_id: 5,\n            parent_id: 0,\n            category_name: '形势与政策',\n            category_code: 'policy',\n            category_desc: '形势与政策以及当代世界经济与政治',\n            question_count: 123,\n            status: 'active',\n            sort_order: 5,\n            created_at: '2024-01-01'\n          },\n          // 马克思主义基本原理的二级分类\n          {\n            category_id: 11,\n            parent_id: 1,\n            category_name: '马克思主义哲学',\n            category_code: 'marxism_philosophy',\n            category_desc: '马克思主义哲学基本原理',\n            question_count: 78,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 12,\n            parent_id: 1,\n            category_name: '马克思主义政治经济学',\n            category_code: 'marxism_economics',\n            category_desc: '马克思主义政治经济学原理',\n            question_count: 45,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 13,\n            parent_id: 1,\n            category_name: '科学社会主义',\n            category_code: 'scientific_socialism',\n            category_desc: '科学社会主义基本原理',\n            question_count: 33,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          },\n          // 马克思主义哲学的三级分类\n          {\n            category_id: 111,\n            parent_id: 11,\n            category_name: '唯物论',\n            category_code: 'materialism',\n            category_desc: '马克思主义唯物论',\n            question_count: 35,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 112,\n            parent_id: 11,\n            category_name: '辩证法',\n            category_code: 'dialectics',\n            category_desc: '马克思主义辩证法',\n            question_count: 42,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 113,\n            parent_id: 11,\n            category_name: '认识论',\n            category_code: 'epistemology',\n            category_desc: '马克思主义认识论',\n            question_count: 38,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-03'\n          },\n          {\n            category_id: 114,\n            parent_id: 11,\n            category_name: '历史观',\n            category_code: 'historical_materialism',\n            category_desc: '马克思主义历史观',\n            question_count: 32,\n            status: 'active',\n            sort_order: 4,\n            created_at: '2024-01-03'\n          },\n          // 唯物论的四级分类\n          {\n            category_id: 1111,\n            parent_id: 111,\n            category_name: '物质概念',\n            category_code: 'matter_concept',\n            category_desc: '物质的哲学概念',\n            question_count: 12,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1112,\n            parent_id: 111,\n            category_name: '意识本质',\n            category_code: 'consciousness_essence',\n            category_desc: '意识的本质和特点',\n            question_count: 15,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1113,\n            parent_id: 111,\n            category_name: '物质与意识关系',\n            category_code: 'matter_consciousness_relation',\n            category_desc: '物质与意识的辩证关系',\n            question_count: 8,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-04'\n          },\n          // 辩证法的四级分类\n          {\n            category_id: 1121,\n            parent_id: 112,\n            category_name: '联系观',\n            category_code: 'connection_view',\n            category_desc: '马克思主义联系观',\n            question_count: 14,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1122,\n            parent_id: 112,\n            category_name: '发展观',\n            category_code: 'development_view',\n            category_desc: '马克思主义发展观',\n            question_count: 16,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-04'\n          },\n          {\n            category_id: 1123,\n            parent_id: 112,\n            category_name: '矛盾规律',\n            category_code: 'contradiction_law',\n            category_desc: '对立统一规律',\n            question_count: 12,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-04'\n          },\n          // 毛泽东思想的二级分类\n          {\n            category_id: 21,\n            parent_id: 2,\n            category_name: '毛泽东思想',\n            category_code: 'mao_thought',\n            category_desc: '毛泽东思想的形成和发展',\n            question_count: 89,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 22,\n            parent_id: 2,\n            category_name: '邓小平理论',\n            category_code: 'deng_theory',\n            category_desc: '邓小平理论',\n            question_count: 67,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 23,\n            parent_id: 2,\n            category_name: '三个代表重要思想',\n            category_code: 'three_represents',\n            category_desc: '三个代表重要思想',\n            question_count: 47,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          },\n          // 中国近现代史的二级分类\n          {\n            category_id: 31,\n            parent_id: 3,\n            category_name: '旧民主主义革命时期',\n            category_code: 'old_democratic_revolution',\n            category_desc: '1840-1919年旧民主主义革命时期',\n            question_count: 56,\n            status: 'active',\n            sort_order: 1,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 32,\n            parent_id: 3,\n            category_name: '新民主主义革命时期',\n            category_code: 'new_democratic_revolution',\n            category_desc: '1919-1949年新民主主义革命时期',\n            question_count: 78,\n            status: 'active',\n            sort_order: 2,\n            created_at: '2024-01-02'\n          },\n          {\n            category_id: 33,\n            parent_id: 3,\n            category_name: '社会主义革命和建设时期',\n            category_code: 'socialist_construction',\n            category_desc: '1949年以来社会主义革命和建设时期',\n            question_count: 44,\n            status: 'active',\n            sort_order: 3,\n            created_at: '2024-01-02'\n          }\n        ]\n        this.total = this.list.length\n        this.listLoading = false\n\n        // 更新父分类选项（只显示可以作为父级的分类）\n        this.parentOptions = this.list.filter(item => item.parent_id === 0 || item.parent_id === 1 || item.parent_id === 2 || item.parent_id === 3)\n      }, 500)\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        category_id: undefined,\n        parent_id: 0,\n        category_name: '',\n        category_code: '',\n        category_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '创建成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          this.$notify({\n            title: '成功',\n            message: '更新成功（模拟）',\n            type: 'success',\n            duration: 2000\n          })\n          this.dialogFormVisible = false\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该分类吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$notify({\n          title: '成功',\n          message: '删除成功（模拟）',\n          type: 'success',\n          duration: 2000\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n    width: 100% !important;\n\n    .el-table__body-wrapper {\n      width: 100% !important;\n    }\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"]}]}