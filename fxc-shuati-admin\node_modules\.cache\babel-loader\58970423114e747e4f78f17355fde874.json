{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzRXh0ZXJuYWwgYXMgX2lzRXh0ZXJuYWwgfSBmcm9tICdAL3V0aWxzL3ZhbGlkYXRlJzsKZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICB0bzogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgaXNFeHRlcm5hbDogZnVuY3Rpb24gaXNFeHRlcm5hbCgpIHsKICAgICAgcmV0dXJuIF9pc0V4dGVybmFsKHRoaXMudG8pOwogICAgfSwKICAgIHR5cGU6IGZ1bmN0aW9uIHR5cGUoKSB7CiAgICAgIGlmICh0aGlzLmlzRXh0ZXJuYWwpIHsKICAgICAgICByZXR1cm4gJ2EnOwogICAgICB9CiAgICAgIHJldHVybiAncm91dGVyLWxpbmsnOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgbGlua1Byb3BzOiBmdW5jdGlvbiBsaW5rUHJvcHModG8pIHsKICAgICAgaWYgKHRoaXMuaXNFeHRlcm5hbCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBocmVmOiB0bywKICAgICAgICAgIHRhcmdldDogJ19ibGFuaycsCiAgICAgICAgICByZWw6ICdub29wZW5lcicKICAgICAgICB9OwogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgdG86IHRvCiAgICAgIH07CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["isExternal", "props", "to", "type", "String", "required", "computed", "methods", "linkProps", "href", "target", "rel"], "sources": ["src/layout/components/Sidebar/Link.vue"], "sourcesContent": ["<template>\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\n    <slot />\n  </component>\n</template>\n\n<script>\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  props: {\n    to: {\n      type: String,\n      required: true\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.to)\n    },\n    type() {\n      if (this.isExternal) {\n        return 'a'\n      }\n      return 'router-link'\n    }\n  },\n  methods: {\n    linkProps(to) {\n      if (this.isExternal) {\n        return {\n          href: to,\n          target: '_blank',\n          rel: 'noopener'\n        }\n      }\n      return {\n        to: to\n      }\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;AAOA,SAAAA,UAAA,IAAAA,WAAA;AAEA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAN,UAAA,WAAAA,WAAA;MACA,OAAAA,WAAA,MAAAE,EAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA,SAAAH,UAAA;QACA;MACA;MACA;IACA;EACA;EACAO,OAAA;IACAC,SAAA,WAAAA,UAAAN,EAAA;MACA,SAAAF,UAAA;QACA;UACAS,IAAA,EAAAP,EAAA;UACAQ,MAAA;UACAC,GAAA;QACA;MACA;MACA;QACAT,EAAA,EAAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}