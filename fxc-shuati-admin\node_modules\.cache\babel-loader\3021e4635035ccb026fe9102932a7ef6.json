{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\list.vue", "mtime": 1752627877659}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEFkbWluVXNlckxpc3QsIGNyZWF0ZUFkbWluVXNlciwgdXBkYXRlQWRtaW5Vc2VyLCBkZWxldGVBZG1pblVzZXIgfSBmcm9tICdAL2FwaS91c2Vycyc7CmltcG9ydCB7IGdldFJvbGVMaXN0IH0gZnJvbSAnQC9hcGkvdXNlcnMnOwppbXBvcnQgd2F2ZXMgZnJvbSAnQC9kaXJlY3RpdmUvd2F2ZXMnOwppbXBvcnQgeyBwYXJzZVRpbWUgfSBmcm9tICdAL3V0aWxzJzsKaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24nOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1VzZXJMaXN0JywKICBjb21wb25lbnRzOiB7CiAgICBQYWdpbmF0aW9uOiBQYWdpbmF0aW9uCiAgfSwKICBkaXJlY3RpdmVzOiB7CiAgICB3YXZlczogd2F2ZXMKICB9LAogIGZpbHRlcnM6IHsKICAgIHBhcnNlVGltZTogcGFyc2VUaW1lCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGFibGVLZXk6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHN0YXR1czogJycKICAgICAgfSwKICAgICAgcm9sZU9wdGlvbnM6IFtdLAogICAgICB0ZW1wOiB7CiAgICAgICAgYWRtaW5faWQ6IHVuZGVmaW5lZCwKICAgICAgICB1c2VybmFtZTogJycsCiAgICAgICAgcGFzc3dvcmQ6ICcnLAogICAgICAgIHJlYWxfbmFtZTogJycsCiAgICAgICAgZW1haWw6ICcnLAogICAgICAgIHBob25lOiAnJywKICAgICAgICByb2xlX2lkczogW10sCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJwogICAgICB9LAogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIGRpYWxvZ1N0YXR1czogJycsCiAgICAgIHRleHRNYXA6IHsKICAgICAgICB1cGRhdGU6ICfnvJbovpHnrqHnkIblkZgnLAogICAgICAgIGNyZWF0ZTogJ+a3u+WKoOeuoeeQhuWRmCcKICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICB1c2VybmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+eUqOaIt+WQjeS4jeiDveS4uuepuicsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBwYXNzd29yZDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+WvhueggeS4jeiDveS4uuepuicsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XQogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXRSb2xlT3B0aW9ucygpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGdldEFkbWluVXNlckxpc3QodGhpcy5saXN0UXVlcnkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMubGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdDsKICAgICAgICBfdGhpcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CiAgICAgICAgX3RoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0Um9sZU9wdGlvbnM6IGZ1bmN0aW9uIGdldFJvbGVPcHRpb25zKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgZ2V0Um9sZUxpc3QoewogICAgICAgIHBhZ2VTaXplOiAxMDAKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIucm9sZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhLmxpc3Q7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUZpbHRlcjogZnVuY3Rpb24gaGFuZGxlRmlsdGVyKCkgewogICAgICB0aGlzLmxpc3RRdWVyeS5wYWdlID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgcmVzZXRUZW1wOiBmdW5jdGlvbiByZXNldFRlbXAoKSB7CiAgICAgIHRoaXMudGVtcCA9IHsKICAgICAgICBhZG1pbl9pZDogdW5kZWZpbmVkLAogICAgICAgIHVzZXJuYW1lOiAnJywKICAgICAgICBwYXNzd29yZDogJycsCiAgICAgICAgcmVhbF9uYW1lOiAnJywKICAgICAgICBlbWFpbDogJycsCiAgICAgICAgcGhvbmU6ICcnLAogICAgICAgIHJvbGVfaWRzOiBbXSwKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnCiAgICAgIH07CiAgICB9LAogICAgaGFuZGxlQ3JlYXRlOiBmdW5jdGlvbiBoYW5kbGVDcmVhdGUoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0VGVtcCgpOwogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICdjcmVhdGUnOwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMy4kcmVmc1snZGF0YUZvcm0nXS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGNyZWF0ZURhdGE6IGZ1bmN0aW9uIGNyZWF0ZURhdGEoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgY3JlYXRlQWRtaW5Vc2VyKF90aGlzNC50ZW1wKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXM0Lmxpc3QudW5zaGlmdChfdGhpczQudGVtcCk7CiAgICAgICAgICAgIF90aGlzNC5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICBfdGhpczQuJG5vdGlmeSh7CiAgICAgICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliJvlu7rmiJDlip8nLAogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX3RoaXM0LmdldExpc3QoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLnRlbXAgPSBPYmplY3QuYXNzaWduKHt9LCByb3cpOwogICAgICB0aGlzLnRlbXAucGFzc3dvcmQgPSAnJzsKICAgICAgdGhpcy5kaWFsb2dTdGF0dXMgPSAndXBkYXRlJzsKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUuJHJlZnNbJ2RhdGFGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpOwogICAgICB9KTsKICAgIH0sCiAgICB1cGRhdGVEYXRhOiBmdW5jdGlvbiB1cGRhdGVEYXRhKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHZhciB0ZW1wRGF0YSA9IE9iamVjdC5hc3NpZ24oe30sIF90aGlzNi50ZW1wKTsKICAgICAgICAgIGlmICghdGVtcERhdGEucGFzc3dvcmQpIHsKICAgICAgICAgICAgZGVsZXRlIHRlbXBEYXRhLnBhc3N3b3JkOwogICAgICAgICAgfQogICAgICAgICAgdXBkYXRlQWRtaW5Vc2VyKHRlbXBEYXRhLmFkbWluX2lkLCB0ZW1wRGF0YSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzNi5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICBfdGhpczYuJG5vdGlmeSh7CiAgICAgICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfmm7TmlrDmiJDlip8nLAogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX3RoaXM2LmdldExpc3QoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTor6XnrqHnkIblkZjlkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgZGVsZXRlQWRtaW5Vc2VyKHJvdy5hZG1pbl9pZCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczcuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgIH0pOwogICAgICAgICAgX3RoaXM3LmdldExpc3QoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["getAdminUserList", "createAdminUser", "updateAdminUser", "deleteAdminUser", "getRoleList", "waves", "parseTime", "Pagination", "name", "components", "directives", "filters", "data", "table<PERSON><PERSON>", "list", "total", "listLoading", "list<PERSON>uery", "page", "pageSize", "keyword", "status", "roleOptions", "temp", "admin_id", "undefined", "username", "password", "real_name", "email", "phone", "role_ids", "dialogFormVisible", "dialogStatus", "textMap", "update", "create", "rules", "required", "message", "trigger", "created", "getList", "getRoleOptions", "methods", "_this", "then", "response", "_this2", "handleFilter", "resetTemp", "handleCreate", "_this3", "$nextTick", "$refs", "clearValidate", "createData", "_this4", "validate", "valid", "unshift", "$notify", "title", "type", "duration", "handleUpdate", "row", "_this5", "Object", "assign", "updateData", "_this6", "tempData", "handleDelete", "_this7", "$confirm", "confirmButtonText", "cancelButtonText"], "sources": ["src/views/users/list.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <el-card class=\"filter-card\">\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.keyword\"\n          placeholder=\"搜索用户名、姓名、邮箱\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"handleFilter\"\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"状态\"\n          clearable\n          style=\"width: 120px\"\n          class=\"filter-item\"\n        >\n          <el-option label=\"正常\" value=\"active\" />\n          <el-option label=\"禁用\" value=\"banned\" />\n        </el-select>\n        <el-button\n          v-waves\n          class=\"filter-item\"\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          @click=\"handleFilter\"\n        >\n          搜索\n        </el-button>\n        <el-button\n          class=\"filter-item\"\n          style=\"margin-left: 10px;\"\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"handleCreate\"\n        >\n          添加管理员\n        </el-button>\n      </div>\n    </el-card>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"admin_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"用户名\" prop=\"username\" min-width=\"120\" />\n      <el-table-column label=\"真实姓名\" prop=\"real_name\" min-width=\"120\" />\n      <el-table-column label=\"邮箱\" prop=\"email\" min-width=\"200\" />\n      <el-table-column label=\"手机号\" prop=\"phone\" min-width=\"120\" />\n      <el-table-column label=\"角色\" min-width=\"150\">\n        <template slot-scope=\"{row}\">\n          <el-tag\n            v-for=\"role in (row.roles || '').split(',')\"\n            :key=\"role\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n          >\n            {{ role }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'danger'\">\n            {{ row.status === 'active' ? '正常' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"最后登录\" min-width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.last_login_time | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" min-width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button\n            v-if=\"row.admin_id !== 1\"\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"temp.username\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"temp.password\" type=\"password\" show-password />\n        </el-form-item>\n        <el-form-item label=\"真实姓名\" prop=\"real_name\">\n          <el-input v-model=\"temp.real_name\" />\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"temp.email\" />\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"temp.phone\" />\n        </el-form-item>\n        <el-form-item label=\"角色\" prop=\"role_ids\">\n          <el-select v-model=\"temp.role_ids\" multiple placeholder=\"请选择角色\">\n            <el-option\n              v-for=\"role in roleOptions\"\n              :key=\"role.role_id\"\n              :label=\"role.role_name\"\n              :value=\"role.role_id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"正常\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"banned\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getAdminUserList, createAdminUser, updateAdminUser, deleteAdminUser } from '@/api/users'\nimport { getRoleList } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'UserList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      roleOptions: [],\n      temp: {\n        admin_id: undefined,\n        username: '',\n        password: '',\n        real_name: '',\n        email: '',\n        phone: '',\n        role_ids: [],\n        status: 'active'\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑管理员',\n        create: '添加管理员'\n      },\n      rules: {\n        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],\n        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getList()\n    this.getRoleOptions()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getAdminUserList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n      })\n    },\n    getRoleOptions() {\n      getRoleList({ pageSize: 100 }).then(response => {\n        this.roleOptions = response.data.list\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        admin_id: undefined,\n        username: '',\n        password: '',\n        real_name: '',\n        email: '',\n        phone: '',\n        role_ids: [],\n        status: 'active'\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createAdminUser(this.temp).then(() => {\n            this.list.unshift(this.temp)\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.temp.password = ''\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          if (!tempData.password) {\n            delete tempData.password\n          }\n          updateAdminUser(tempData.admin_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该管理员吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteAdminUser(row.admin_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .filter-card {\n    margin-bottom: 20px;\n    .filter-container {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      .filter-item {\n        margin-right: 10px;\n        margin-bottom: 10px;\n      }\n    }\n  }\n  .el-table {\n    margin-bottom: 20px;\n  }\n  .pagination-container {\n    padding: 15px 0;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA,SAAAA,gBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA;AACA,SAAAC,WAAA;AACA,OAAAC,KAAA;AACA,SAAAC,SAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,UAAA,EAAAA;EAAA;EACAG,UAAA;IAAAL,KAAA,EAAAA;EAAA;EACAM,OAAA;IACAL,SAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,WAAA;MACAC,IAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAV,MAAA;MACA;MACAW,iBAAA;MACAC,YAAA;MACAC,OAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAX,QAAA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAb,QAAA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAA7B,WAAA;MACAhB,gBAAA,MAAAiB,SAAA,EAAA6B,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAA/B,IAAA,GAAAiC,QAAA,CAAAnC,IAAA,CAAAE,IAAA;QACA+B,KAAA,CAAA9B,KAAA,GAAAgC,QAAA,CAAAnC,IAAA,CAAAG,KAAA;QACA8B,KAAA,CAAA7B,WAAA;MACA;IACA;IACA2B,cAAA,WAAAA,eAAA;MAAA,IAAAK,MAAA;MACA5C,WAAA;QAAAe,QAAA;MAAA,GAAA2B,IAAA,WAAAC,QAAA;QACAC,MAAA,CAAA1B,WAAA,GAAAyB,QAAA,CAAAnC,IAAA,CAAAE,IAAA;MACA;IACA;IACAmC,YAAA,WAAAA,aAAA;MACA,KAAAhC,SAAA,CAAAC,IAAA;MACA,KAAAwB,OAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACA,KAAA3B,IAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAV,MAAA;MACA;IACA;IACA8B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAF,SAAA;MACA,KAAAjB,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAD,MAAA,CAAAE,KAAA,aAAAC,aAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA1D,eAAA,CAAAwD,MAAA,CAAAlC,IAAA,EAAAuB,IAAA;YACAW,MAAA,CAAA3C,IAAA,CAAA8C,OAAA,CAAAH,MAAA,CAAAlC,IAAA;YACAkC,MAAA,CAAAzB,iBAAA;YACAyB,MAAA,CAAAI,OAAA;cACAC,KAAA;cACAvB,OAAA;cACAwB,IAAA;cACAC,QAAA;YACA;YACAP,MAAA,CAAAf,OAAA;UACA;QACA;MACA;IACA;IACAuB,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA5C,IAAA,GAAA6C,MAAA,CAAAC,MAAA,KAAAH,GAAA;MACA,KAAA3C,IAAA,CAAAI,QAAA;MACA,KAAAM,YAAA;MACA,KAAAD,iBAAA;MACA,KAAAqB,SAAA;QACAc,MAAA,CAAAb,KAAA,aAAAC,aAAA;MACA;IACA;IACAe,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAjB,KAAA,aAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAa,QAAA,GAAAJ,MAAA,CAAAC,MAAA,KAAAE,MAAA,CAAAhD,IAAA;UACA,KAAAiD,QAAA,CAAA7C,QAAA;YACA,OAAA6C,QAAA,CAAA7C,QAAA;UACA;UACAzB,eAAA,CAAAsE,QAAA,CAAAhD,QAAA,EAAAgD,QAAA,EAAA1B,IAAA;YACAyB,MAAA,CAAAvC,iBAAA;YACAuC,MAAA,CAAAV,OAAA;cACAC,KAAA;cACAvB,OAAA;cACAwB,IAAA;cACAC,QAAA;YACA;YACAO,MAAA,CAAA7B,OAAA;UACA;QACA;MACA;IACA;IACA+B,YAAA,WAAAA,aAAAP,GAAA;MAAA,IAAAQ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAd,IAAA;MACA,GAAAjB,IAAA;QACA3C,eAAA,CAAA+D,GAAA,CAAA1C,QAAA,EAAAsB,IAAA;UACA4B,MAAA,CAAAb,OAAA;YACAC,KAAA;YACAvB,OAAA;YACAwB,IAAA;YACAC,QAAA;UACA;UACAU,MAAA,CAAAhC,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}