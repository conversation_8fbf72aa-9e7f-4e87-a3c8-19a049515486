// 工具函数集合

/**
 * 格式化时间
 * @param {Date|String|Number} time 时间
 * @param {String} format 格式 默认 'YYYY-MM-DD HH:mm:ss'
 */
function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  const date = new Date(time)

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  const second = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 格式化倒计时
 * @param {Number} seconds 秒数
 */
function formatCountdown(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
}

/**
 * 获取题目类型文本
 * @param {String} type 题目类型
 */
function getQuestionTypeText(type) {
  const typeMap = {
    single: '单选题',
    multiple: '多选题',
    judge: '判断题',
    fill: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || '未知题型'
}

/**
 * 获取难度文本
 * @param {Number} difficulty 难度等级
 */
function getDifficultyText(difficulty) {
  const difficultyMap = {
    1: '简单',
    2: '中等',
    3: '困难',
    4: '极难',
    5: '地狱'
  }
  return difficultyMap[difficulty] || '未知'
}

/**
 * 获取选项标签
 * @param {Number} index 选项索引
 */
function getOptionLabel(index) {
  return String.fromCharCode(65 + index) // A, B, C, D...
}

/**
 * 计算正确率
 * @param {Number} correct 正确数
 * @param {Number} total 总数
 */
function calculateAccuracy(correct, total) {
  if (total === 0) return 0
  return Math.round((correct / total) * 100)
}

/**
 * 显示加载提示
 * @param {String} title 提示文字
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title: title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading()
}

/**
 * 显示成功提示
 * @param {String} title 提示文字
 */
function showSuccess(title) {
  wx.showToast({
    title: title,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 显示错误提示
 * @param {String} title 提示文字
 */
function showError(title) {
  wx.showToast({
    title: title,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 确认对话框
 * @param {String} content 内容
 * @param {String} title 标题
 */
function showConfirm(content, title = '提示') {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title: title,
      content: content,
      success: (res) => {
        if (res.confirm) {
          resolve(true)
        } else {
          resolve(false)
        }
      },
      fail: () => {
        reject(false)
      }
    })
  })
}

/**
 * 存储数据到本地
 * @param {String} key 键名
 * @param {*} value 值
 */
function setStorage(key, value) {
  try {
    wx.setStorageSync(key, value)
  } catch (e) {
    console.error('存储数据失败:', e)
  }
}

/**
 * 从本地获取数据
 * @param {String} key 键名
 * @param {*} defaultValue 默认值
 */
function getStorage(key, defaultValue = null) {
  try {
    const value = wx.getStorageSync(key)
    return value !== '' ? value : defaultValue
  } catch (e) {
    console.error('获取数据失败:', e)
    return defaultValue
  }
}

module.exports = {
  formatTime,
  formatCountdown,
  getQuestionTypeText,
  getDifficultyText,
  getOptionLabel,
  calculateAccuracy,
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  showConfirm,
  setStorage,
  getStorage
}
