{"_from": "htmlparser2@^6.1.0", "_id": "htmlparser2@6.1.0", "_inBundle": false, "_integrity": "sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==", "_location": "/htmlparser2", "_phantomChildren": {"domhandler": "4.3.1"}, "_requested": {"type": "range", "registry": true, "raw": "htmlparser2@^6.1.0", "name": "htmlparser2", "escapedName": "htmlparser2", "rawSpec": "^6.1.0", "saveSpec": null, "fetchSpec": "^6.1.0"}, "_requiredBy": ["/renderkid"], "_resolved": "https://registry.npmmirror.com/htmlparser2/-/htmlparser2-6.1.0.tgz", "_shasum": "c4d762b6c3371a05dbe65e94ae43a9f845fb8fb7", "_spec": "htmlparser2@^6.1.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\renderkid", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/htmlparser2/issues"}, "bundleDependencies": false, "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}, "deprecated": false, "description": "Fast & forgiving HTML/XML parser", "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "eslint": "^7.15.0", "eslint-config-prettier": "^8.1.0", "jest": "^26.0.1", "prettier": "^2.1.1", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "directories": {"lib": "lib/"}, "files": ["lib/**/*"], "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "homepage": "https://github.com/fb55/htmlparser2#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "keywords": ["html", "parser", "streams", "xml", "dom", "rss", "feed", "atom"], "license": "MIT", "main": "lib/index.js", "name": "htmlparser2", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/htmlparser2.git"}, "scripts": {"build": "tsc", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "prepare": "npm run build", "test": "jest --coverage"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "6.1.0"}