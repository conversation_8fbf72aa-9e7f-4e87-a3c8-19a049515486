{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\api\\user.js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\api\\user.js", "mtime": 1752565964549}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKZXhwb3J0IGZ1bmN0aW9uIGxvZ2luKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL2F1dGgvbG9naW4nLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIGdldEluZm8oKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS9hdXRoL2luZm8nLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CmV4cG9ydCBmdW5jdGlvbiBsb2dvdXQoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS9hdXRoL2xvZ291dCcsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9CmV4cG9ydCBmdW5jdGlvbiByZWZyZXNoVG9rZW4oKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS9hdXRoL3JlZnJlc2gnLAogICAgbWV0aG9kOiAncG9zdCcKICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gY2hhbmdlUGFzc3dvcmQoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvYXV0aC9jaGFuZ2UtcGFzc3dvcmQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0="}, {"version": 3, "names": ["request", "login", "data", "url", "method", "getInfo", "logout", "refreshToken", "changePassword"], "sources": ["D:/code/fanxiaochang/fxc-shuati-admin/src/api/user.js"], "sourcesContent": ["import request from '@/utils/request'\n\nexport function login(data) {\n  return request({\n    url: '/api/auth/login',\n    method: 'post',\n    data\n  })\n}\n\nexport function getInfo() {\n  return request({\n    url: '/api/auth/info',\n    method: 'get'\n  })\n}\n\nexport function logout() {\n  return request({\n    url: '/api/auth/logout',\n    method: 'post'\n  })\n}\n\nexport function refreshToken() {\n  return request({\n    url: '/api/auth/refresh',\n    method: 'post'\n  })\n}\n\nexport function changePassword(data) {\n  return request({\n    url: '/api/auth/change-password',\n    method: 'post',\n    data\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASG,OAAOA,CAAA,EAAG;EACxB,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASE,MAAMA,CAAA,EAAG;EACvB,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASG,YAAYA,CAAA,EAAG;EAC7B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASI,cAAcA,CAACN,IAAI,EAAE;EACnC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}