/**errors.wxss**/

.container {
  padding-bottom: 120rpx;
}

/* 统计信息 */
.stats-section {
  margin: 20rpx;
  margin-bottom: 10rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #F56C6C;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 筛选条件 */
.filter-section {
  margin: 20rpx;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-item.active {
  background-color: #F56C6C;
  color: #fff;
}

/* 错题列表 */
.error-list {
  padding: 0 20rpx;
}

.error-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #F56C6C;
  transition: all 0.3s ease;
}

.error-item:active {
  transform: scale(0.98);
  background-color: #fef0f0;
}

.question-info {
  flex: 1;
  margin-right: 20rpx;
}

.question-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.question-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.question-type {
  background-color: #e6f3ff;
  color: #409EFF;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.error-count {
  background-color: #fef0f0;
  color: #F56C6C;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.error-time {
  color: #999;
}

.answer-comparison {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
}

.user-answer,
.correct-answer {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}

.user-answer:last-child,
.correct-answer:last-child {
  margin-bottom: 0;
}

.answer-label {
  width: 120rpx;
  color: #666;
  flex-shrink: 0;
}

.answer-content {
  flex: 1;
  word-break: break-all;
}

.answer-content.wrong {
  color: #F56C6C;
}

.answer-content.correct {
  color: #67C23A;
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.review-btn,
.delete-btn {
  width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
  border: none;
  border-radius: 30rpx;
  padding: 0;
  line-height: 60rpx;
}

.review-btn {
  background-color: #409EFF;
  color: #fff;
}

.delete-btn {
  background-color: #F56C6C;
  color: #fff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 50rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.go-practice-btn {
  width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 学习建议 */
.suggestion-section {
  margin: 20rpx;
  border-left: 6rpx solid #E6A23C;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #E6A23C;
  margin-bottom: 20rpx;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.suggestion-icon {
  margin-right: 10rpx;
  flex-shrink: 0;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #409EFF;
  color: #fff;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-btn {
  background-color: #f8f9fa;
  color: #666;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15rpx;
  }
  
  .error-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .question-info {
    margin-right: 0;
    margin-bottom: 20rpx;
    width: 100%;
  }
  
  .error-actions {
    flex-direction: row;
    width: 100%;
    justify-content: flex-end;
  }
}

/* 动画效果 */
.error-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 特殊状态 */
.error-item.reviewed {
  opacity: 0.7;
  border-left-color: #67C23A;
}

.error-item.reviewed .stats-number {
  color: #67C23A;
}
