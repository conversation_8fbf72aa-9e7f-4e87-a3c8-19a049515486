-- 创建数据库
CREATE DATABASE IF NOT EXISTS `fxc-shuati` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `fxc-shuati`;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  user_id INT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
  nickname VARCHAR(100) DEFAULT '' COMMENT '昵称',
  avatar_url VARCHAR(500) DEFAULT '' COMMENT '头像URL',
  phone VARCHAR(20) DEFAULT '' COMMENT '手机号',
  email VARCHAR(100) DEFAULT '' COMMENT '邮箱',
  is_vip BOOLEAN DEFAULT FALSE COMMENT '是否VIP',
  vip_expire_time DATETIME NULL COMMENT 'VIP过期时间',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-正常，banned-禁用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 管理员表
CREATE TABLE IF NOT EXISTS admins (
  admin_id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  real_name VARCHAR(50) DEFAULT '' COMMENT '真实姓名',
  email VARCHAR(100) DEFAULT '' COMMENT '邮箱',
  phone VARCHAR(20) DEFAULT '' COMMENT '手机号',
  avatar VARCHAR(500) DEFAULT '' COMMENT '头像',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-正常，banned-禁用',
  last_login_time DATETIME NULL COMMENT '最后登录时间',
  last_login_ip VARCHAR(50) DEFAULT '' COMMENT '最后登录IP',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
  role_id INT PRIMARY KEY AUTO_INCREMENT,
  role_name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
  role_code VARCHAR(50) UNIQUE NOT NULL COMMENT '角色编码',
  role_desc TEXT COMMENT '角色描述',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-启用，disabled-禁用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
  permission_id INT PRIMARY KEY AUTO_INCREMENT,
  parent_id INT DEFAULT 0 COMMENT '父权限ID，0为顶级权限',
  permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
  permission_code VARCHAR(100) UNIQUE NOT NULL COMMENT '权限编码',
  permission_type VARCHAR(20) DEFAULT 'menu' COMMENT '权限类型：menu-菜单，button-按钮，api-接口',
  permission_url VARCHAR(200) DEFAULT '' COMMENT '权限URL',
  permission_icon VARCHAR(50) DEFAULT '' COMMENT '权限图标',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-启用，disabled-禁用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 管理员角色关联表
CREATE TABLE IF NOT EXISTS admin_roles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  admin_id INT NOT NULL COMMENT '管理员ID',
  role_id INT NOT NULL COMMENT '角色ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_admin (admin_id),
  INDEX idx_role (role_id),
  UNIQUE KEY uk_admin_role (admin_id, role_id),
  FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  role_id INT NOT NULL COMMENT '角色ID',
  permission_id INT NOT NULL COMMENT '权限ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_role (role_id),
  INDEX idx_permission (permission_id),
  UNIQUE KEY uk_role_permission (role_id, permission_id),
  FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
);

-- 题目分类表
CREATE TABLE IF NOT EXISTS categories (
  category_id INT PRIMARY KEY AUTO_INCREMENT,
  parent_id INT DEFAULT 0 COMMENT '父分类ID，0为顶级分类',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_desc TEXT COMMENT '分类描述',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-启用，disabled-禁用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 题目表
CREATE TABLE IF NOT EXISTS questions (
  question_id INT PRIMARY KEY AUTO_INCREMENT,
  category_id INT NOT NULL COMMENT '分类ID',
  question_type VARCHAR(20) NOT NULL COMMENT '题目类型：single-单选，multiple-多选，judge-判断，fill-填空，essay-简答',
  question_content TEXT NOT NULL COMMENT '题目内容',
  question_difficulty INT DEFAULT 1 COMMENT '题目难度：1-5',
  question_subject VARCHAR(50) DEFAULT '' COMMENT '所属科目',
  question_chapter VARCHAR(100) DEFAULT '' COMMENT '所属章节',
  question_status VARCHAR(20) DEFAULT 'active' COMMENT '题目状态：active-启用，disabled-禁用，review-审核中',
  view_count INT DEFAULT 0 COMMENT '查看次数',
  correct_count INT DEFAULT 0 COMMENT '正确次数',
  wrong_count INT DEFAULT 0 COMMENT '错误次数',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category_id),
  INDEX idx_type (question_type),
  INDEX idx_difficulty (question_difficulty),
  INDEX idx_status (question_status)
);

-- 选项表
CREATE TABLE IF NOT EXISTS options (
  option_id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '关联题目ID',
  option_content TEXT NOT NULL COMMENT '选项内容',
  option_order INT NOT NULL COMMENT '选项顺序',
  is_correct BOOLEAN DEFAULT FALSE COMMENT '是否为正确答案',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_question (question_id),
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 答案表
CREATE TABLE IF NOT EXISTS answers (
  answer_id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '关联题目ID',
  answer_content TEXT NOT NULL COMMENT '答案内容',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_question (question_id),
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 解析表
CREATE TABLE IF NOT EXISTS explanations (
  explanation_id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '关联题目ID',
  explanation_content TEXT NOT NULL COMMENT '解析内容',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_question (question_id),
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 组合题表
CREATE TABLE IF NOT EXISTS composite_questions (
  composite_id INT PRIMARY KEY AUTO_INCREMENT,
  category_id INT NOT NULL COMMENT '分类ID',
  composite_title VARCHAR(200) DEFAULT '' COMMENT '组合题标题',
  composite_content TEXT NOT NULL COMMENT '组合题材料内容',
  composite_difficulty INT DEFAULT 1 COMMENT '难度：1-5',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-启用，disabled-禁用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category_id)
);

-- 组合题关联表
CREATE TABLE IF NOT EXISTS composite_question_links (
  link_id INT PRIMARY KEY AUTO_INCREMENT,
  composite_id INT NOT NULL COMMENT '关联组合题ID',
  question_id INT NOT NULL COMMENT '关联题目ID',
  question_order INT NOT NULL COMMENT '题目在组合题中的顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_composite (composite_id),
  INDEX idx_question (question_id),
  FOREIGN KEY (composite_id) REFERENCES composite_questions(composite_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 用户收藏表
CREATE TABLE IF NOT EXISTS user_favorites (
  favorite_id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  question_id INT NOT NULL COMMENT '题目ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_question (question_id),
  UNIQUE KEY uk_user_question (user_id, question_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 用户错题表
CREATE TABLE IF NOT EXISTS user_errors (
  error_id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  question_id INT NOT NULL COMMENT '题目ID',
  user_answer TEXT COMMENT '用户答案',
  error_count INT DEFAULT 1 COMMENT '错误次数',
  last_error_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后错误时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_question (question_id),
  UNIQUE KEY uk_user_question (user_id, question_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 用户答题记录表
CREATE TABLE IF NOT EXISTS user_answer_records (
  record_id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  question_id INT NOT NULL COMMENT '题目ID',
  user_answer TEXT COMMENT '用户答案',
  is_correct BOOLEAN NOT NULL COMMENT '是否正确',
  answer_time INT DEFAULT 0 COMMENT '答题用时（秒）',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_question (question_id),
  INDEX idx_correct (is_correct),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 插入默认角色
INSERT INTO roles (role_id, role_name, role_code, role_desc, status, sort_order) VALUES
(1, '超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 'active', 1),
(2, '管理员', 'admin', '系统管理员，拥有大部分权限', 'active', 2),
(3, '内容编辑', 'editor', '内容编辑，负责题库管理', 'active', 3),
(4, '普通用户', 'user', '普通用户，只有查看权限', 'active', 4)
ON DUPLICATE KEY UPDATE role_name = role_name;

-- 插入默认权限
INSERT INTO permissions (permission_id, parent_id, permission_name, permission_code, permission_type, permission_url, permission_icon, status, sort_order) VALUES
-- 系统管理
(1, 0, '系统管理', 'system', 'menu', '/system', 'el-icon-setting', 'active', 1),
(2, 1, '用户管理', 'system:user', 'menu', '/system/user', 'el-icon-user', 'active', 1),
(3, 1, '角色管理', 'system:role', 'menu', '/system/role', 'el-icon-s-custom', 'active', 2),
(4, 1, '权限管理', 'system:permission', 'menu', '/system/permission', 'el-icon-key', 'active', 3),
(5, 1, '菜单管理', 'system:menu', 'menu', '/system/menu', 'el-icon-menu', 'active', 4),
(6, 1, '系统设置', 'system:setting', 'menu', '/system/setting', 'el-icon-s-tools', 'active', 5),
(7, 1, '操作日志', 'system:log', 'menu', '/system/log', 'el-icon-document', 'active', 6),

-- 题库管理
(100, 0, '题库管理', 'question', 'menu', '/question', 'el-icon-document', 'active', 2),
(101, 100, '题目分类', 'question:category', 'menu', '/question/category', 'el-icon-folder', 'active', 1),
(102, 100, '题目列表', 'question:list', 'menu', '/question/list', 'el-icon-document-copy', 'active', 2),
(103, 100, '添加题目', 'question:add', 'menu', '/question/add', 'el-icon-plus', 'active', 3),
(104, 100, '编辑题目', 'question:edit', 'menu', '/question/edit', 'el-icon-edit', 'active', 4),
(105, 100, '批量导入', 'question:import', 'menu', '/question/import', 'el-icon-upload', 'active', 5),
(106, 100, '批量导出', 'question:export', 'menu', '/question/export', 'el-icon-download', 'active', 6),

-- 学习数据
(200, 0, '学习数据', 'learning', 'menu', '/learning', 'el-icon-data-analysis', 'active', 3),
(201, 200, '收藏管理', 'learning:favorite', 'menu', '/learning/favorite', 'el-icon-star-on', 'active', 1),
(202, 200, '错题管理', 'learning:error', 'menu', '/learning/error', 'el-icon-warning', 'active', 2),
(203, 200, '练习记录', 'learning:record', 'menu', '/learning/record', 'el-icon-tickets', 'active', 3),
(204, 200, '学习报告', 'learning:report', 'menu', '/learning/report', 'el-icon-s-data', 'active', 4),

-- 用户管理
(300, 0, '用户管理', 'user', 'menu', '/user', 'el-icon-user', 'active', 4),
(301, 300, '用户列表', 'user:list', 'menu', '/user/list', 'el-icon-user-solid', 'active', 1),
(302, 300, '用户详情', 'user:detail', 'menu', '/user/detail', 'el-icon-view', 'active', 2),
(303, 300, '用户统计', 'user:stats', 'menu', '/user/stats', 'el-icon-s-marketing', 'active', 3)
ON DUPLICATE KEY UPDATE permission_name = permission_name;

-- 插入默认管理员账号 (密码: 123456)
INSERT INTO admins (admin_id, username, password, real_name, email, status) VALUES
(1, 'admin', '$2a$10$OxWYJJGTP2gi00l2x06QuOWNzOKJGzIHb6BNn8hYZKCMvpKSjcZEO', '系统管理员', '<EMAIL>', 'active')
ON DUPLICATE KEY UPDATE username = username;

-- 关联管理员和角色
INSERT INTO admin_roles (admin_id, role_id) VALUES
(1, 1)
ON DUPLICATE KEY UPDATE admin_id = admin_id;

-- 关联角色和权限 (超级管理员拥有所有权限)
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, permission_id FROM permissions
ON DUPLICATE KEY UPDATE role_id = role_id;

-- 关联角色和权限 (管理员拥有除系统管理外的所有权限)
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, permission_id FROM permissions WHERE parent_id != 1
ON DUPLICATE KEY UPDATE role_id = role_id;

-- 关联角色和权限 (内容编辑只有题库管理权限)
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, permission_id FROM permissions WHERE parent_id = 100
ON DUPLICATE KEY UPDATE role_id = role_id;

-- 插入默认分类
INSERT INTO categories (category_name, category_desc, sort_order) VALUES
('考研英语', '考研英语题库', 1),
('英语一', '考研英语一', 2),
('英语二', '考研英语二', 3),
('阅读理解', '阅读理解题型', 4),
('完型填空', '完型填空题型', 5),
('翻译', '翻译题型', 6),
('写作', '写作题型', 7)
ON DUPLICATE KEY UPDATE category_name = category_name;
