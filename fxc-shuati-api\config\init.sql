-- 创建数据库
CREATE DATABASE IF NOT EXISTS fxc_shuati CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE fxc_shuati;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  user_id INT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
  nickname VARCHAR(100) DEFAULT '' COMMENT '昵称',
  avatar_url VARCHAR(500) DEFAULT '' COMMENT '头像URL',
  phone VARCHAR(20) DEFAULT '' COMMENT '手机号',
  email VARCHAR(100) DEFAULT '' COMMENT '邮箱',
  is_vip BOOLEAN DEFAULT FALSE COMMENT '是否VIP',
  vip_expire_time DATETIME NULL COMMENT 'VIP过期时间',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-正常，banned-禁用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 管理员表
CREATE TABLE IF NOT EXISTS admins (
  admin_id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  real_name VARCHAR(50) DEFAULT '' COMMENT '真实姓名',
  email VARCHAR(100) DEFAULT '' COMMENT '邮箱',
  role VARCHAR(20) DEFAULT 'admin' COMMENT '角色：admin-管理员，super-超级管理员',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-正常，banned-禁用',
  last_login_time DATETIME NULL COMMENT '最后登录时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 题目分类表
CREATE TABLE IF NOT EXISTS categories (
  category_id INT PRIMARY KEY AUTO_INCREMENT,
  parent_id INT DEFAULT 0 COMMENT '父分类ID，0为顶级分类',
  category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
  category_desc TEXT COMMENT '分类描述',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-启用，disabled-禁用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 题目表
CREATE TABLE IF NOT EXISTS questions (
  question_id INT PRIMARY KEY AUTO_INCREMENT,
  category_id INT NOT NULL COMMENT '分类ID',
  question_type VARCHAR(20) NOT NULL COMMENT '题目类型：single-单选，multiple-多选，judge-判断，fill-填空，essay-简答',
  question_content TEXT NOT NULL COMMENT '题目内容',
  question_difficulty INT DEFAULT 1 COMMENT '题目难度：1-5',
  question_subject VARCHAR(50) DEFAULT '' COMMENT '所属科目',
  question_chapter VARCHAR(100) DEFAULT '' COMMENT '所属章节',
  question_status VARCHAR(20) DEFAULT 'active' COMMENT '题目状态：active-启用，disabled-禁用，review-审核中',
  view_count INT DEFAULT 0 COMMENT '查看次数',
  correct_count INT DEFAULT 0 COMMENT '正确次数',
  wrong_count INT DEFAULT 0 COMMENT '错误次数',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category_id),
  INDEX idx_type (question_type),
  INDEX idx_difficulty (question_difficulty),
  INDEX idx_status (question_status)
);

-- 选项表
CREATE TABLE IF NOT EXISTS options (
  option_id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '关联题目ID',
  option_content TEXT NOT NULL COMMENT '选项内容',
  option_order INT NOT NULL COMMENT '选项顺序',
  is_correct BOOLEAN DEFAULT FALSE COMMENT '是否为正确答案',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_question (question_id),
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 答案表
CREATE TABLE IF NOT EXISTS answers (
  answer_id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '关联题目ID',
  answer_content TEXT NOT NULL COMMENT '答案内容',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_question (question_id),
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 解析表
CREATE TABLE IF NOT EXISTS explanations (
  explanation_id INT PRIMARY KEY AUTO_INCREMENT,
  question_id INT NOT NULL COMMENT '关联题目ID',
  explanation_content TEXT NOT NULL COMMENT '解析内容',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_question (question_id),
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 组合题表
CREATE TABLE IF NOT EXISTS composite_questions (
  composite_id INT PRIMARY KEY AUTO_INCREMENT,
  category_id INT NOT NULL COMMENT '分类ID',
  composite_title VARCHAR(200) DEFAULT '' COMMENT '组合题标题',
  composite_content TEXT NOT NULL COMMENT '组合题材料内容',
  composite_difficulty INT DEFAULT 1 COMMENT '难度：1-5',
  status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-启用，disabled-禁用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category_id)
);

-- 组合题关联表
CREATE TABLE IF NOT EXISTS composite_question_links (
  link_id INT PRIMARY KEY AUTO_INCREMENT,
  composite_id INT NOT NULL COMMENT '关联组合题ID',
  question_id INT NOT NULL COMMENT '关联题目ID',
  question_order INT NOT NULL COMMENT '题目在组合题中的顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_composite (composite_id),
  INDEX idx_question (question_id),
  FOREIGN KEY (composite_id) REFERENCES composite_questions(composite_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 用户收藏表
CREATE TABLE IF NOT EXISTS user_favorites (
  favorite_id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  question_id INT NOT NULL COMMENT '题目ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_question (question_id),
  UNIQUE KEY uk_user_question (user_id, question_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 用户错题表
CREATE TABLE IF NOT EXISTS user_errors (
  error_id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  question_id INT NOT NULL COMMENT '题目ID',
  user_answer TEXT COMMENT '用户答案',
  error_count INT DEFAULT 1 COMMENT '错误次数',
  last_error_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后错误时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_question (question_id),
  UNIQUE KEY uk_user_question (user_id, question_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 用户答题记录表
CREATE TABLE IF NOT EXISTS user_answer_records (
  record_id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  question_id INT NOT NULL COMMENT '题目ID',
  user_answer TEXT COMMENT '用户答案',
  is_correct BOOLEAN NOT NULL COMMENT '是否正确',
  answer_time INT DEFAULT 0 COMMENT '答题用时（秒）',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_question (question_id),
  INDEX idx_correct (is_correct),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE
);

-- 插入默认管理员账号
INSERT INTO admins (username, password, real_name, role) VALUES 
('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'super')
ON DUPLICATE KEY UPDATE username = username;

-- 插入默认分类
INSERT INTO categories (category_name, category_desc, sort_order) VALUES 
('考研英语', '考研英语题库', 1),
('英语一', '考研英语一', 2),
('英语二', '考研英语二', 3),
('阅读理解', '阅读理解题型', 4),
('完型填空', '完型填空题型', 5),
('翻译', '翻译题型', 6),
('写作', '写作题型', 7)
ON DUPLICATE KEY UPDATE category_name = category_name;
