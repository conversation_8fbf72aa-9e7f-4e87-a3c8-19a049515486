{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\questions\\upload.vue", "mtime": 1752630804729}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "uploadUrl", "fileList", "uploading", "showProgress", "uploadProgress", "uploadStatus", "progressText", "importResult", "methods", "downloadTemplate", "templateData", "downloadCSV", "filename", "csv<PERSON><PERSON>nt", "convertToCSV", "blob", "Blob", "type", "link", "document", "createElement", "download", "undefined", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "length", "headers", "Object", "keys", "csvHeaders", "join", "csvRows", "map", "row", "header", "value", "concat", "toString", "replace", "_toConsumableArray", "beforeUpload", "file", "isExcel", "isLt10M", "size", "$message", "error", "submitUpload", "warning", "simulateUpload", "_this", "timer", "setInterval", "Math", "random", "setTimeout", "success_count", "error_count", "skip_count", "total_count", "errors", "field", "message", "clearInterval", "handleSuccess", "response", "success", "handleError", "err", "handleProgress", "event", "clearFiles", "$refs", "upload", "resetImport", "goToQuestionList", "$router", "push"], "sources": ["src/views/questions/upload.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 导入说明 -->\n    <el-card class=\"instruction-card\">\n      <div slot=\"header\">\n        <span>批量导入题目</span>\n      </div>\n      <div class=\"instruction-content\">\n        <el-alert\n          title=\"导入说明\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        >\n          <div slot=\"default\">\n            <p>1. 请下载模板文件，按照模板格式填写题目数据</p>\n            <p>2. 支持的文件格式：Excel (.xlsx, .xls)</p>\n            <p>3. 单次最多导入1000道题目</p>\n            <p>4. 题目类型：single_choice(单选), multiple_choice(多选), true_false(判断), fill_blank(填空), short_answer(简答), essay(论述)</p>\n            <p>5. 难度等级：easy(简单), medium(中等), hard(困难)</p>\n          </div>\n        </el-alert>\n\n        <div class=\"template-download\">\n          <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\n            下载导入模板\n          </el-button>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 文件上传 -->\n    <el-card class=\"upload-card\">\n      <div slot=\"header\">\n        <span>上传文件</span>\n      </div>\n\n      <el-upload\n        ref=\"upload\"\n        class=\"upload-demo\"\n        drag\n        :action=\"uploadUrl\"\n        :before-upload=\"beforeUpload\"\n        :on-success=\"handleSuccess\"\n        :on-error=\"handleError\"\n        :on-progress=\"handleProgress\"\n        :file-list=\"fileList\"\n        :auto-upload=\"false\"\n        accept=\".xlsx,.xls\"\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件，且不超过10MB</div>\n      </el-upload>\n\n      <div class=\"upload-actions\">\n        <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploading\">\n          开始导入\n        </el-button>\n        <el-button @click=\"clearFiles\">清空文件</el-button>\n      </div>\n    </el-card>\n\n    <!-- 导入进度 -->\n    <el-card v-if=\"showProgress\" class=\"progress-card\">\n      <div slot=\"header\">\n        <span>导入进度</span>\n      </div>\n\n      <el-progress\n        :percentage=\"uploadProgress\"\n        :status=\"uploadStatus\"\n        :stroke-width=\"20\"\n      />\n\n      <div class=\"progress-info\">\n        <p>{{ progressText }}</p>\n      </div>\n    </el-card>\n\n    <!-- 导入结果 -->\n    <el-card v-if=\"importResult\" class=\"result-card\">\n      <div slot=\"header\">\n        <span>导入结果</span>\n      </div>\n\n      <div class=\"result-summary\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <div class=\"stat-item success\">\n              <div class=\"stat-number\">{{ importResult.success_count }}</div>\n              <div class=\"stat-label\">成功导入</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item error\">\n              <div class=\"stat-number\">{{ importResult.error_count }}</div>\n              <div class=\"stat-label\">导入失败</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item warning\">\n              <div class=\"stat-number\">{{ importResult.skip_count }}</div>\n              <div class=\"stat-label\">跳过重复</div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"stat-item info\">\n              <div class=\"stat-number\">{{ importResult.total_count }}</div>\n              <div class=\"stat-label\">总计处理</div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 错误详情 -->\n      <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"error-details\">\n        <h4>错误详情：</h4>\n        <el-table\n          :data=\"importResult.errors\"\n          border\n          size=\"small\"\n          max-height=\"300\"\n        >\n          <el-table-column prop=\"row\" label=\"行号\" width=\"80\" />\n          <el-table-column prop=\"field\" label=\"字段\" width=\"120\" />\n          <el-table-column prop=\"message\" label=\"错误信息\" />\n        </el-table>\n      </div>\n\n      <div class=\"result-actions\">\n        <el-button type=\"primary\" @click=\"goToQuestionList\">查看题目列表</el-button>\n        <el-button @click=\"resetImport\">重新导入</el-button>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'QuestionUpload',\n  data() {\n    return {\n      uploadUrl: '/api/questions/import', // 实际项目中的上传接口\n      fileList: [],\n      uploading: false,\n      showProgress: false,\n      uploadProgress: 0,\n      uploadStatus: '',\n      progressText: '',\n      importResult: null\n    }\n  },\n  methods: {\n    downloadTemplate() {\n      // 创建模板数据\n      const templateData = [\n        {\n          '题目类型': 'single_choice',\n          '难度等级': 'medium',\n          '分类ID': '1111',\n          '标签ID': '1,9,7',\n          '题目内容': '马克思主义哲学的基本问题是（）',\n          '选项A': '物质和意识的关系问题',\n          '选项B': '理论和实践的关系问题',\n          '选项C': '个人和社会的关系问题',\n          '选项D': '自由和必然的关系问题',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'A',\n          '题目解析': '马克思主义哲学的基本问题是物质和意识的关系问题，这是哲学的根本问题。它包括两个方面：第一，物质和意识何者为第一性；第二，物质和意识是否具有同一性。',\n          '分值': '2',\n          '排序': '1',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'single_choice',\n          '难度等级': 'easy',\n          '分类ID': '1111',\n          '标签ID': '1,9,16',\n          '题目内容': '物质的唯一特性是（）',\n          '选项A': '运动性',\n          '选项B': '客观实在性',\n          '选项C': '可知性',\n          '选项D': '绝对性',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'B',\n          '题目解析': '物质的唯一特性是客观实在性。这是物质概念的核心，指物质不依赖于人的意识而存在，并能为人的意识所反映。',\n          '分值': '2',\n          '排序': '2',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'multiple_choice',\n          '难度等级': 'medium',\n          '分类ID': '1112',\n          '标签ID': '1,9,7',\n          '题目内容': '意识的本质是（）',\n          '选项A': '意识是人脑的机能',\n          '选项B': '意识是客观存在的反映',\n          '选项C': '意识是社会的产物',\n          '选项D': '意识具有主观能动性',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'A,B,C,D',\n          '题目解析': '意识的本质包括：（1）意识是人脑的机能；（2）意识是客观存在的反映；（3）意识是社会的产物；（4）意识具有主观能动性。这四个方面构成了意识本质的完整内容。',\n          '分值': '2',\n          '排序': '3',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'true_false',\n          '难度等级': 'easy',\n          '分类ID': '1111',\n          '标签ID': '1,9',\n          '题目内容': '马克思主义哲学认为，世界的真正统一性在于它的物质性。',\n          '选项A': '',\n          '选项B': '',\n          '选项C': '',\n          '选项D': '',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': 'true',\n          '题目解析': '正确。马克思主义哲学认为，世界的真正统一性在于它的物质性。这是马克思主义一元论的基本观点，强调世界万物都是物质的不同表现形式。',\n          '分值': '2',\n          '排序': '4',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'fill_blank',\n          '难度等级': 'easy',\n          '分类ID': '1123',\n          '标签ID': '1,9,7',\n          '题目内容': '矛盾的基本属性是_____和_____。',\n          '选项A': '',\n          '选项B': '',\n          '选项C': '',\n          '选项D': '',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': '同一性；斗争性',\n          '题目解析': '矛盾的基本属性是同一性和斗争性。同一性是指矛盾双方相互依存、相互贯通的性质和趋势；斗争性是指矛盾双方相互排斥、相互对立的性质和趋势。',\n          '分值': '2',\n          '排序': '5',\n          '状态': 'active'\n        },\n        {\n          '题目类型': 'short_answer',\n          '难度等级': 'hard',\n          '分类ID': '113',\n          '标签ID': '1,9,6',\n          '题目内容': '简述实践是认识基础的主要表现。',\n          '选项A': '',\n          '选项B': '',\n          '选项C': '',\n          '选项D': '',\n          '选项E': '',\n          '选项F': '',\n          '正确答案': '实践是认识的基础，主要表现在四个方面：（1）实践是认识的来源；（2）实践是认识发展的动力；（3）实践是检验认识真理性的唯一标准；（4）实践是认识的目的。',\n          '题目解析': '这道题考查实践与认识的关系。要从四个方面来回答：来源、动力、标准、目的。每个方面都要简要说明其含义。',\n          '分值': '6',\n          '排序': '6',\n          '状态': 'active'\n        }\n      ]\n\n      // 转换为CSV格式并下载\n      this.downloadCSV(templateData, '题目导入模板.csv')\n    },\n\n    downloadCSV(data, filename) {\n      const csvContent = this.convertToCSV(data)\n      const blob = new Blob(['\\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })\n      const link = document.createElement('a')\n\n      if (link.download !== undefined) {\n        const url = URL.createObjectURL(blob)\n        link.setAttribute('href', url)\n        link.setAttribute('download', filename)\n        link.style.visibility = 'hidden'\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n      }\n    },\n\n    convertToCSV(data) {\n      if (!data || data.length === 0) return ''\n\n      const headers = Object.keys(data[0])\n      const csvHeaders = headers.join(',')\n\n      const csvRows = data.map(row => {\n        return headers.map(header => {\n          const value = row[header] || ''\n          return `\"${value.toString().replace(/\"/g, '\"\"')}\"`\n        }).join(',')\n      })\n\n      return [csvHeaders, ...csvRows].join('\\n')\n    },\n\n    beforeUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                     file.type === 'application/vnd.ms-excel'\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isExcel) {\n        this.$message.error('只能上传Excel文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n      return true\n    },\n\n    submitUpload() {\n      if (this.fileList.length === 0) {\n        this.$message.warning('请先选择要上传的文件')\n        return\n      }\n\n      this.uploading = true\n      this.showProgress = true\n      this.uploadProgress = 0\n      this.uploadStatus = ''\n      this.progressText = '开始上传文件...'\n\n      // 模拟上传过程\n      this.simulateUpload()\n    },\n\n    simulateUpload() {\n      const timer = setInterval(() => {\n        this.uploadProgress += Math.random() * 15\n\n        if (this.uploadProgress < 30) {\n          this.progressText = '正在上传文件...'\n        } else if (this.uploadProgress < 60) {\n          this.progressText = '正在解析文件内容...'\n        } else if (this.uploadProgress < 90) {\n          this.progressText = '正在导入题目数据...'\n        } else {\n          this.uploadProgress = 100\n          this.progressText = '导入完成！'\n          this.uploadStatus = 'success'\n          this.uploading = false\n\n          // 模拟导入结果\n          setTimeout(() => {\n            this.importResult = {\n              success_count: 245,\n              error_count: 5,\n              skip_count: 12,\n              total_count: 262,\n              errors: [\n                { row: 15, field: '题目内容', message: '题目内容不能为空' },\n                { row: 23, field: '正确答案', message: '正确答案格式错误' },\n                { row: 45, field: '分类ID', message: '分类ID不存在' },\n                { row: 67, field: '题目类型', message: '不支持的题目类型' },\n                { row: 89, field: '选项A', message: '单选题至少需要2个选项' }\n              ]\n            }\n          }, 500)\n\n          clearInterval(timer)\n        }\n      }, 200)\n    },\n\n    handleSuccess(response, file, fileList) {\n      this.$message.success('文件上传成功')\n    },\n\n    handleError(err, file, fileList) {\n      this.$message.error('文件上传失败')\n      this.uploading = false\n      this.showProgress = false\n    },\n\n    handleProgress(event, file, fileList) {\n      // 实际项目中可以在这里处理真实的上传进度\n    },\n\n    clearFiles() {\n      this.$refs.upload.clearFiles()\n      this.fileList = []\n      this.showProgress = false\n      this.importResult = null\n    },\n\n    resetImport() {\n      this.clearFiles()\n      this.uploadProgress = 0\n      this.uploadStatus = ''\n      this.progressText = ''\n    },\n\n    goToQuestionList() {\n      this.$router.push('/questions/list')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  .instruction-card,\n  .upload-card,\n  .progress-card,\n  .result-card {\n    margin-bottom: 20px;\n  }\n\n  .instruction-content {\n    .template-download {\n      margin-top: 20px;\n      text-align: center;\n    }\n  }\n\n  .upload-demo {\n    margin-bottom: 20px;\n  }\n\n  .upload-actions {\n    text-align: center;\n    margin-top: 20px;\n  }\n\n  .progress-info {\n    margin-top: 20px;\n    text-align: center;\n\n    p {\n      margin: 0;\n      color: #606266;\n    }\n  }\n\n  .result-summary {\n    margin-bottom: 30px;\n\n    .stat-item {\n      text-align: center;\n      padding: 20px;\n      border-radius: 8px;\n\n      .stat-number {\n        font-size: 32px;\n        font-weight: bold;\n        margin-bottom: 8px;\n      }\n\n      .stat-label {\n        font-size: 14px;\n        color: #666;\n      }\n\n      &.success {\n        background: #f0f9ff;\n        .stat-number { color: #67c23a; }\n      }\n\n      &.error {\n        background: #fef0f0;\n        .stat-number { color: #f56c6c; }\n      }\n\n      &.warning {\n        background: #fdf6ec;\n        .stat-number { color: #e6a23c; }\n      }\n\n      &.info {\n        background: #f4f4f5;\n        .stat-number { color: #909399; }\n      }\n    }\n  }\n\n  .error-details {\n    margin-bottom: 30px;\n\n    h4 {\n      margin-bottom: 15px;\n      color: #f56c6c;\n    }\n  }\n\n  .result-actions {\n    text-align: center;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,QAAA;MACAC,SAAA;MACAC,YAAA;MACAC,cAAA;MACAC,YAAA;MACAC,YAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA;MACA,IAAAC,YAAA,IACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,EACA;;MAEA;MACA,KAAAC,WAAA,CAAAD,YAAA;IACA;IAEAC,WAAA,WAAAA,YAAAZ,IAAA,EAAAa,QAAA;MACA,IAAAC,UAAA,QAAAC,YAAA,CAAAf,IAAA;MACA,IAAAgB,IAAA,OAAAC,IAAA,aAAAH,UAAA;QAAAI,IAAA;MAAA;MACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;MAEA,IAAAF,IAAA,CAAAG,QAAA,KAAAC,SAAA;QACA,IAAAC,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAV,IAAA;QACAG,IAAA,CAAAQ,YAAA,SAAAH,GAAA;QACAL,IAAA,CAAAQ,YAAA,aAAAd,QAAA;QACAM,IAAA,CAAAS,KAAA,CAAAC,UAAA;QACAT,QAAA,CAAAU,IAAA,CAAAC,WAAA,CAAAZ,IAAA;QACAA,IAAA,CAAAa,KAAA;QACAZ,QAAA,CAAAU,IAAA,CAAAG,WAAA,CAAAd,IAAA;MACA;IACA;IAEAJ,YAAA,WAAAA,aAAAf,IAAA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAAkC,MAAA;MAEA,IAAAC,OAAA,GAAAC,MAAA,CAAAC,IAAA,CAAArC,IAAA;MACA,IAAAsC,UAAA,GAAAH,OAAA,CAAAI,IAAA;MAEA,IAAAC,OAAA,GAAAxC,IAAA,CAAAyC,GAAA,WAAAC,GAAA;QACA,OAAAP,OAAA,CAAAM,GAAA,WAAAE,MAAA;UACA,IAAAC,KAAA,GAAAF,GAAA,CAAAC,MAAA;UACA,YAAAE,MAAA,CAAAD,KAAA,CAAAE,QAAA,GAAAC,OAAA;QACA,GAAAR,IAAA;MACA;MAEA,QAAAD,UAAA,EAAAO,MAAA,CAAAG,kBAAA,CAAAR,OAAA,GAAAD,IAAA;IACA;IAEAU,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAAhC,IAAA,4EACAgC,IAAA,CAAAhC,IAAA;MACA,IAAAkC,OAAA,GAAAF,IAAA,CAAAG,IAAA;MAEA,KAAAF,OAAA;QACA,KAAAG,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAH,OAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEAC,YAAA,WAAAA,aAAA;MACA,SAAAtD,QAAA,CAAAgC,MAAA;QACA,KAAAoB,QAAA,CAAAG,OAAA;QACA;MACA;MAEA,KAAAtD,SAAA;MACA,KAAAC,YAAA;MACA,KAAAC,cAAA;MACA,KAAAC,YAAA;MACA,KAAAC,YAAA;;MAEA;MACA,KAAAmD,cAAA;IACA;IAEAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,KAAA,GAAAC,WAAA;QACAF,KAAA,CAAAtD,cAAA,IAAAyD,IAAA,CAAAC,MAAA;QAEA,IAAAJ,KAAA,CAAAtD,cAAA;UACAsD,KAAA,CAAApD,YAAA;QACA,WAAAoD,KAAA,CAAAtD,cAAA;UACAsD,KAAA,CAAApD,YAAA;QACA,WAAAoD,KAAA,CAAAtD,cAAA;UACAsD,KAAA,CAAApD,YAAA;QACA;UACAoD,KAAA,CAAAtD,cAAA;UACAsD,KAAA,CAAApD,YAAA;UACAoD,KAAA,CAAArD,YAAA;UACAqD,KAAA,CAAAxD,SAAA;;UAEA;UACA6D,UAAA;YACAL,KAAA,CAAAnD,YAAA;cACAyD,aAAA;cACAC,WAAA;cACAC,UAAA;cACAC,WAAA;cACAC,MAAA,GACA;gBAAA3B,GAAA;gBAAA4B,KAAA;gBAAAC,OAAA;cAAA,GACA;gBAAA7B,GAAA;gBAAA4B,KAAA;gBAAAC,OAAA;cAAA,GACA;gBAAA7B,GAAA;gBAAA4B,KAAA;gBAAAC,OAAA;cAAA,GACA;gBAAA7B,GAAA;gBAAA4B,KAAA;gBAAAC,OAAA;cAAA,GACA;gBAAA7B,GAAA;gBAAA4B,KAAA;gBAAAC,OAAA;cAAA;YAEA;UACA;UAEAC,aAAA,CAAAZ,KAAA;QACA;MACA;IACA;IAEAa,aAAA,WAAAA,cAAAC,QAAA,EAAAxB,IAAA,EAAAhD,QAAA;MACA,KAAAoD,QAAA,CAAAqB,OAAA;IACA;IAEAC,WAAA,WAAAA,YAAAC,GAAA,EAAA3B,IAAA,EAAAhD,QAAA;MACA,KAAAoD,QAAA,CAAAC,KAAA;MACA,KAAApD,SAAA;MACA,KAAAC,YAAA;IACA;IAEA0E,cAAA,WAAAA,eAAAC,KAAA,EAAA7B,IAAA,EAAAhD,QAAA;MACA;IAAA,CACA;IAEA8E,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAF,UAAA;MACA,KAAA9E,QAAA;MACA,KAAAE,YAAA;MACA,KAAAI,YAAA;IACA;IAEA2E,WAAA,WAAAA,YAAA;MACA,KAAAH,UAAA;MACA,KAAA3E,cAAA;MACA,KAAAC,YAAA;MACA,KAAAC,YAAA;IACA;IAEA6E,gBAAA,WAAAA,iBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}