<!--result.wxml-->
<view class="container">
  <!-- 结果卡片 -->
  <view class="result-card card">
    <view class="result-icon">
      <text class="icon-emoji">{{resultIcon}}</text>
    </view>
    <view class="result-title">{{resultTitle}}</view>
    <view class="result-subtitle">{{resultSubtitle}}</view>
  </view>

  <!-- 成绩统计 -->
  <view class="stats-section card">
    <view class="section-title">答题统计</view>
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{result.totalQuestions}}</text>
        <text class="stats-label">总题数</text>
      </view>
      <view class="stats-item">
        <text class="stats-number correct">{{result.correctCount}}</text>
        <text class="stats-label">答对</text>
      </view>
      <view class="stats-item">
        <text class="stats-number wrong">{{result.wrongCount}}</text>
        <text class="stats-label">答错</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{result.accuracy}}%</text>
        <text class="stats-label">正确率</text>
      </view>
    </view>
  </view>

  <!-- 用时统计 -->
  <view class="time-section card" wx:if="{{result.totalTime}}">
    <view class="section-title">用时统计</view>
    <view class="time-grid">
      <view class="time-item">
        <text class="time-label">总用时</text>
        <text class="time-value">{{result.totalTimeText}}</text>
      </view>
      <view class="time-item">
        <text class="time-label">平均用时</text>
        <text class="time-value">{{result.averageTimeText}}</text>
      </view>
    </view>
  </view>

  <!-- 错题列表 -->
  <view class="wrong-section card" wx:if="{{result.wrongQuestions.length > 0}}">
    <view class="section-title">错题回顾</view>
    <view class="wrong-list">
      <view 
        wx:for="{{result.wrongQuestions}}" 
        wx:key="index"
        class="wrong-item"
        bindtap="reviewQuestion"
        data-item="{{item}}"
      >
        <view class="question-number">第{{item.questionIndex + 1}}题</view>
        <view class="question-content">{{item.question.question_content}}</view>
        <view class="answer-info">
          <text class="user-answer">你的答案: {{item.userAnswer || '未作答'}}</text>
          <text class="correct-answer">正确答案: {{item.correctAnswer}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 成就徽章 -->
  <view class="achievement-section card" wx:if="{{achievements.length > 0}}">
    <view class="section-title">获得成就</view>
    <view class="achievement-list">
      <view 
        wx:for="{{achievements}}" 
        wx:key="index"
        class="achievement-item"
      >
        <text class="achievement-icon">{{item.icon}}</text>
        <text class="achievement-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 学习建议 -->
  <view class="suggestion-section card" wx:if="{{suggestions.length > 0}}">
    <view class="section-title">学习建议</view>
    <view class="suggestion-list">
      <view wx:for="{{suggestions}}" wx:key="index" class="suggestion-item">
        <text class="suggestion-icon">💡</text>
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="action-btn btn-secondary" bindtap="goHome">返回首页</button>
    <button class="action-btn btn-primary" bindtap="continueStudy">继续学习</button>
  </view>

  <!-- 分享功能 -->
  <view class="share-section" wx:if="{{showShare}}">
    <button class="share-btn" open-type="share">
      <text class="share-icon">📤</text>
      <text class="share-text">分享成绩</text>
    </button>
  </view>
</view>
