# is-primitive [![NPM version](https://badge.fury.io/js/is-primitive.svg)](http://badge.fury.io/js/is-primitive)  [![Build Status](https://travis-ci.org/jonschlinkert/is-primitive.svg)](https://travis-ci.org/jonschlinkert/is-primitive) 

> Returns `true` if the value is a primitive. 

## Install with [npm](npmjs.org)

```bash
npm i is-primitive --save
```

## Running tests
Install dev dependencies.

```bash
npm i -d && npm test
```

## Usage

```js
var isPrimitive = require('is-primitive');
isPrimitive('abc');
//=> true

isPrimitive(42);
//=> true

isPrimitive(false);
//=> true

isPrimitive(true);
//=> true

isPrimitive({});
//=> false

isPrimitive([]);
//=> false

isPrimitive(function(){});
//=> false
```

## Author

**<PERSON>**
 
+ [github/jonschlink<PERSON>](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert) 

## License
Copyright (c) 2014-2015 Jon Schlinkert  
Released under the MIT license

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on March 16, 2015._