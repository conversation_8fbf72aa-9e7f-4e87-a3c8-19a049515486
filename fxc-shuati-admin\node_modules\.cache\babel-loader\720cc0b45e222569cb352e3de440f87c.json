{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\Pagination\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\Pagination\\index.vue", "mtime": 1752568300393}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["scrollTo", "name", "props", "total", "required", "type", "Number", "page", "default", "limit", "pageSizes", "Array", "layout", "String", "background", "Boolean", "autoScroll", "hidden", "computed", "currentPage", "get", "set", "val", "$emit", "pageSize", "methods", "handleSizeChange", "handleCurrentChange"], "sources": ["src/components/Pagination/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\n    <el-pagination\n      :background=\"background\"\n      :current-page.sync=\"currentPage\"\n      :page-size.sync=\"pageSize\"\n      :layout=\"layout\"\n      :page-sizes=\"pageSizes\"\n      :total=\"total\"\n      v-bind=\"$attrs\"\n      @size-change=\"handleSizeChange\"\n      @current-change=\"handleCurrentChange\"\n    />\n  </div>\n</template>\n\n<script>\nimport { scrollTo } from '@/utils/scroll-to'\n\nexport default {\n  name: 'Pagination',\n  props: {\n    total: {\n      required: true,\n      type: Number\n    },\n    page: {\n      type: Number,\n      default: 1\n    },\n    limit: {\n      type: Number,\n      default: 20\n    },\n    pageSizes: {\n      type: Array,\n      default() {\n        return [10, 20, 30, 50]\n      }\n    },\n    layout: {\n      type: String,\n      default: 'total, sizes, prev, pager, next, jumper'\n    },\n    background: {\n      type: Boolean,\n      default: true\n    },\n    autoScroll: {\n      type: Boolean,\n      default: true\n    },\n    hidden: {\n      type: <PERSON>olean,\n      default: false\n    }\n  },\n  computed: {\n    currentPage: {\n      get() {\n        return this.page\n      },\n      set(val) {\n        this.$emit('update:page', val)\n      }\n    },\n    pageSize: {\n      get() {\n        return this.limit\n      },\n      set(val) {\n        this.$emit('update:limit', val)\n      }\n    }\n  },\n  methods: {\n    handleSizeChange(val) {\n      this.$emit('pagination', { page: this.currentPage, limit: val })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    },\n    handleCurrentChange(val) {\n      this.$emit('pagination', { page: val, limit: this.pageSize })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  background: #fff;\n  padding: 32px 16px;\n}\n.pagination-container.hidden {\n  display: none;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAiBA,SAAAA,QAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,QAAA;MACAC,IAAA,EAAAC;IACA;IACAC,IAAA;MACAF,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAC,KAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAE,SAAA;MACAL,IAAA,EAAAM,KAAA;MACAH,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAI,MAAA;MACAP,IAAA,EAAAQ,MAAA;MACAL,OAAA;IACA;IACAM,UAAA;MACAT,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAQ,UAAA;MACAX,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAS,MAAA;MACAZ,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;EACA;EACAU,QAAA;IACAC,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAb,IAAA;MACA;MACAc,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,gBAAAD,GAAA;MACA;IACA;IACAE,QAAA;MACAJ,GAAA,WAAAA,IAAA;QACA,YAAAX,KAAA;MACA;MACAY,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,iBAAAD,GAAA;MACA;IACA;EACA;EACAG,OAAA;IACAC,gBAAA,WAAAA,iBAAAJ,GAAA;MACA,KAAAC,KAAA;QAAAhB,IAAA,OAAAY,WAAA;QAAAV,KAAA,EAAAa;MAAA;MACA,SAAAN,UAAA;QACAhB,QAAA;MACA;IACA;IACA2B,mBAAA,WAAAA,oBAAAL,GAAA;MACA,KAAAC,KAAA;QAAAhB,IAAA,EAAAe,GAAA;QAAAb,KAAA,OAAAe;MAAA;MACA,SAAAR,UAAA;QACAhB,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}