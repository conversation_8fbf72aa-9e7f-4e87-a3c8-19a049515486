# 刷题小程序 API 接口

基于 Express + MySQL 的刷题小程序后端API接口。

## 技术栈

- Node.js v14.12.0
- Express.js
- MySQL 2
- JWT 认证
- bcryptjs 密码加密

## 项目结构

```
fxc-shuati-api/
├── app.js                 # 主应用文件
├── package.json           # 项目配置
├── config/               # 配置文件
│   ├── database.js       # 数据库配置
│   └── init.sql          # 数据库初始化脚本
├── routes/               # 路由文件
│   ├── auth.js           # 认证相关
│   ├── users.js          # 用户管理
│   ├── questions.js      # 题目管理
│   ├── options.js        # 选项管理
│   ├── answers.js        # 答案管理
│   ├── explanations.js   # 解析管理
│   ├── composite.js      # 组合题管理
│   ├── categories.js     # 分类管理
│   ├── favorites.js      # 收藏功能
│   └── errors.js         # 错题本
└── uploads/              # 文件上传目录
```

## 安装和运行

1. 安装依赖
```bash
npm install
```

2. 配置数据库
- 创建 MySQL 数据库
- 执行 `config/init.sql` 初始化数据库表

3. 配置环境变量（可选）
```bash
# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=fxc_shuati
DB_PORT=3306

# JWT密钥
JWT_SECRET=your_jwt_secret_key

# 服务端口
PORT=3000
```

4. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## API 接口文档

### 认证相关

#### 管理员登录
- **POST** `/api/auth/admin/login`
- 请求体：
```json
{
  "username": "admin",
  "password": "admin123"
}
```

#### 微信小程序用户登录
- **POST** `/api/auth/wechat/login`
- 请求体：
```json
{
  "openid": "wx_openid_123",
  "nickname": "用户昵称",
  "avatar_url": "头像URL"
}
```

### 题目相关

#### 获取题目列表
- **GET** `/api/questions`
- 查询参数：
  - `question_type`: 题目类型（single/multiple/judge/fill/essay）
  - `question_subject`: 科目
  - `question_chapter`: 章节
  - `question_difficulty`: 难度（1-5）
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认20）
  - `random`: 是否随机排序（true/false）

#### 获取题目详情
- **GET** `/api/questions/:id`

#### 提交答案
- **POST** `/api/questions/:id/answer`
- 需要认证
- 请求体：
```json
{
  "user_answer": "用户答案",
  "answer_time": 30
}
```

#### 创建题目（管理员）
- **POST** `/api/questions`
- 需要管理员权限

### 用户相关

#### 获取用户列表（管理员）
- **GET** `/api/users`
- 需要管理员权限

#### 获取当前用户信息
- **GET** `/api/users/profile`
- 需要认证

#### 更新用户信息
- **PUT** `/api/users/profile`
- 需要认证

#### 获取用户统计
- **GET** `/api/users/stats`
- 需要认证

### 收藏相关

#### 获取收藏列表
- **GET** `/api/favorites`
- 需要认证

#### 添加收藏
- **POST** `/api/favorites`
- 需要认证
- 请求体：
```json
{
  "question_id": 1
}
```

#### 取消收藏
- **DELETE** `/api/favorites/:question_id`
- 需要认证

#### 检查收藏状态
- **GET** `/api/favorites/check/:question_id`
- 需要认证

### 错题本相关

#### 获取错题列表
- **GET** `/api/errors`
- 需要认证

#### 添加错题记录
- **POST** `/api/errors`
- 需要认证

#### 删除错题记录
- **DELETE** `/api/errors/:question_id`
- 需要认证

#### 清空错题本
- **DELETE** `/api/errors`
- 需要认证

#### 获取错题统计
- **GET** `/api/errors/stats`
- 需要认证

### 分类相关

#### 获取分类树
- **GET** `/api/categories/tree`

#### 获取分类列表
- **GET** `/api/categories`

#### 获取分类详情
- **GET** `/api/categories/:id`

#### 创建分类（管理员）
- **POST** `/api/categories`
- 需要管理员权限

### 组合题相关

#### 获取组合题列表
- **GET** `/api/composite`

#### 获取组合题详情
- **GET** `/api/composite/:id`

#### 创建组合题（管理员）
- **POST** `/api/composite`
- 需要管理员权限

## 响应格式

成功响应：
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

错误响应：
```json
{
  "success": false,
  "message": "错误信息"
}
```

## 认证方式

使用 JWT Token 进行认证，在请求头中添加：
```
Authorization: Bearer <token>
```

## 默认账号

管理员账号：
- 用户名：admin
- 密码：admin123

## 注意事项

1. 当前使用 Mock 数据，暂时不连接数据库
2. 所有接口都支持 CORS 跨域请求
3. 文件上传限制为 10MB
4. JWT Token 有效期：管理员24小时，用户30天
