/*!
  * vue-router v3.0.6
  * (c) 2019 <PERSON> You
  * @license MIT
  */
function t(t){return Object.prototype.toString.call(t).indexOf("Error")>-1}function e(t,e){for(const n in e)t[n]=e[n];return t}var n={name:"RouterView",functional:!0,props:{name:{type:String,default:"default"}},render(t,{props:n,children:r,parent:o,data:i}){i.routerView=!0;const s=o.$createElement,a=n.name,c=o.$route,u=o._routerViewCache||(o._routerViewCache={});let h=0,p=!1;for(;o&&o._routerRoot!==o;){const t=o.$vnode&&o.$vnode.data;t&&(t.routerView&&h++,t.keepAlive&&o._inactive&&(p=!0)),o=o.$parent}if(i.routerViewDepth=h,p)return s(u[a],i,r);const l=c.matched[h];if(!l)return u[a]=null,s();const f=u[a]=l.components[a];i.registerRouteInstance=((t,e)=>{const n=l.instances[a];(e&&n!==t||!e&&n===t)&&(l.instances[a]=e)}),(i.hook||(i.hook={})).prepatch=((t,e)=>{l.instances[a]=e.componentInstance}),i.hook.init=(t=>{t.data.keepAlive&&t.componentInstance&&t.componentInstance!==l.instances[a]&&(l.instances[a]=t.componentInstance)});let d=i.props=function(t,e){switch(typeof e){case"undefined":return;case"object":return e;case"function":return e(t);case"boolean":return e?t.params:void 0}}(c,l.props&&l.props[a]);if(d){d=i.props=e({},d);const t=i.attrs=i.attrs||{};for(const e in d)f.props&&e in f.props||(t[e]=d[e],delete d[e])}return s(f,i,r)}};const r=/[!'()*]/g,o=t=>"%"+t.charCodeAt(0).toString(16),i=/%2C/g,s=t=>encodeURIComponent(t).replace(r,o).replace(i,","),a=decodeURIComponent;function c(t){const e={};return(t=t.trim().replace(/^(\?|#|&)/,""))?(t.split("&").forEach(t=>{const n=t.replace(/\+/g," ").split("="),r=a(n.shift()),o=n.length>0?a(n.join("=")):null;void 0===e[r]?e[r]=o:Array.isArray(e[r])?e[r].push(o):e[r]=[e[r],o]}),e):e}function u(t){const e=t?Object.keys(t).map(e=>{const n=t[e];if(void 0===n)return"";if(null===n)return s(e);if(Array.isArray(n)){const t=[];return n.forEach(n=>{void 0!==n&&(null===n?t.push(s(e)):t.push(s(e)+"="+s(n)))}),t.join("&")}return s(e)+"="+s(n)}).filter(t=>t.length>0).join("&"):null;return e?`?${e}`:""}const h=/\/?$/;function p(t,e,n,r){const o=r&&r.options.stringifyQuery;let i=e.query||{};try{i=l(i)}catch(t){}const s={name:e.name||t&&t.name,meta:t&&t.meta||{},path:e.path||"/",hash:e.hash||"",query:i,params:e.params||{},fullPath:y(e,o),matched:t?d(t):[]};return n&&(s.redirectedFrom=y(n,o)),Object.freeze(s)}function l(t){if(Array.isArray(t))return t.map(l);if(t&&"object"==typeof t){const e={};for(const n in t)e[n]=l(t[n]);return e}return t}const f=p(null,{path:"/"});function d(t){const e=[];for(;t;)e.unshift(t),t=t.parent;return e}function y({path:t,query:e={},hash:n=""},r){return(t||"/")+(r||u)(e)+n}function m(t,e){return e===f?t===e:!!e&&(t.path&&e.path?t.path.replace(h,"")===e.path.replace(h,"")&&t.hash===e.hash&&g(t.query,e.query):!(!t.name||!e.name)&&(t.name===e.name&&t.hash===e.hash&&g(t.query,e.query)&&g(t.params,e.params)))}function g(t={},e={}){if(!t||!e)return t===e;const n=Object.keys(t),r=Object.keys(e);return n.length===r.length&&n.every(n=>{const r=t[n],o=e[n];return"object"==typeof r&&"object"==typeof o?g(r,o):String(r)===String(o)})}const b=[String,Object],w=[String,Array];var v={name:"RouterLink",props:{to:{type:b,required:!0},tag:{type:String,default:"a"},exact:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,event:{type:w,default:"click"}},render(t){const n=this.$router,r=this.$route,{location:o,route:i,href:s}=n.resolve(this.to,r,this.append),a={},c=n.options.linkActiveClass,u=n.options.linkExactActiveClass,l=null==c?"router-link-active":c,f=null==u?"router-link-exact-active":u,d=null==this.activeClass?l:this.activeClass,y=null==this.exactActiveClass?f:this.exactActiveClass,g=o.path?p(null,o,null,n):i;a[y]=m(r,g),a[d]=this.exact?a[y]:function(t,e){return 0===t.path.replace(h,"/").indexOf(e.path.replace(h,"/"))&&(!e.hash||t.hash===e.hash)&&function(t,e){for(const n in e)if(!(n in t))return!1;return!0}(t.query,e.query)}(r,g);const b=t=>{x(t)&&(this.replace?n.replace(o):n.push(o))},w={click:x};Array.isArray(this.event)?this.event.forEach(t=>{w[t]=b}):w[this.event]=b;const v={class:a};if("a"===this.tag)v.on=w,v.attrs={href:s};else{const t=function t(e){if(e){let n;for(let r=0;r<e.length;r++){if("a"===(n=e[r]).tag)return n;if(n.children&&(n=t(n.children)))return n}}}(this.$slots.default);if(t){t.isStatic=!1,(t.data=e({},t.data)).on=w,(t.data.attrs=e({},t.data.attrs)).href=s}else v.on=w}return t(this.tag,v,this.$slots.default)}};function x(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey||t.defaultPrevented||void 0!==t.button&&0!==t.button)){if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}let k;const R="undefined"!=typeof window;function E(t,e,n){const r=t.charAt(0);if("/"===r)return t;if("?"===r||"#"===r)return e+t;const o=e.split("/");n&&o[o.length-1]||o.pop();const i=t.replace(/^\//,"").split("/");for(let t=0;t<i.length;t++){const e=i[t];".."===e?o.pop():"."!==e&&o.push(e)}return""!==o[0]&&o.unshift(""),o.join("/")}function O(t){return t.replace(/\/\//g,"/")}var A=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},C=H,$=L,j=function(t,e){return U(L(t,e))},T=U,S=B,_=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function L(t,e){for(var n,r=[],o=0,i=0,s="",a=e&&e.delimiter||"/";null!=(n=_.exec(t));){var c=n[0],u=n[1],h=n.index;if(s+=t.slice(i,h),i=h+c.length,u)s+=u[1];else{var p=t[i],l=n[2],f=n[3],d=n[4],y=n[5],m=n[6],g=n[7];s&&(r.push(s),s="");var b=null!=l&&null!=p&&p!==l,w="+"===m||"*"===m,v="?"===m||"*"===m,x=n[2]||a,k=d||y;r.push({name:f||o++,prefix:l||"",delimiter:x,optional:v,repeat:w,partial:b,asterisk:!!g,pattern:k?I(k):g?".*":"[^"+P(x)+"]+?"})}}return i<t.length&&(s+=t.substr(i)),s&&r.push(s),r}function q(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function U(t){for(var e=new Array(t.length),n=0;n<t.length;n++)"object"==typeof t[n]&&(e[n]=new RegExp("^(?:"+t[n].pattern+")$"));return function(n,r){for(var o="",i=n||{},s=(r||{}).pretty?q:encodeURIComponent,a=0;a<t.length;a++){var c=t[a];if("string"!=typeof c){var u,h=i[c.name];if(null==h){if(c.optional){c.partial&&(o+=c.prefix);continue}throw new TypeError('Expected "'+c.name+'" to be defined')}if(A(h)){if(!c.repeat)throw new TypeError('Expected "'+c.name+'" to not repeat, but received `'+JSON.stringify(h)+"`");if(0===h.length){if(c.optional)continue;throw new TypeError('Expected "'+c.name+'" to not be empty')}for(var p=0;p<h.length;p++){if(u=s(h[p]),!e[a].test(u))throw new TypeError('Expected all "'+c.name+'" to match "'+c.pattern+'", but received `'+JSON.stringify(u)+"`");o+=(0===p?c.prefix:c.delimiter)+u}}else{if(u=c.asterisk?encodeURI(h).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}):s(h),!e[a].test(u))throw new TypeError('Expected "'+c.name+'" to match "'+c.pattern+'", but received "'+u+'"');o+=c.prefix+u}}else o+=c}return o}}function P(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function I(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function M(t,e){return t.keys=e,t}function V(t){return t.sensitive?"":"i"}function B(t,e,n){A(e)||(n=e||n,e=[]);for(var r=(n=n||{}).strict,o=!1!==n.end,i="",s=0;s<t.length;s++){var a=t[s];if("string"==typeof a)i+=P(a);else{var c=P(a.prefix),u="(?:"+a.pattern+")";e.push(a),a.repeat&&(u+="(?:"+c+u+")*"),i+=u=a.optional?a.partial?c+"("+u+")?":"(?:"+c+"("+u+"))?":c+"("+u+")"}}var h=P(n.delimiter||"/"),p=i.slice(-h.length)===h;return r||(i=(p?i.slice(0,-h.length):i)+"(?:"+h+"(?=$))?"),i+=o?"$":r&&p?"":"(?="+h+"|$)",M(new RegExp("^"+i,V(n)),e)}function H(t,e,n){return A(e)||(n=e||n,e=[]),n=n||{},t instanceof RegExp?function(t,e){var n=t.source.match(/\((?!\?)/g);if(n)for(var r=0;r<n.length;r++)e.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return M(t,e)}(t,e):A(t)?function(t,e,n){for(var r=[],o=0;o<t.length;o++)r.push(H(t[o],e,n).source);return M(new RegExp("(?:"+r.join("|")+")",V(n)),e)}(t,e,n):function(t,e,n){return B(L(t,n),e,n)}(t,e,n)}C.parse=$,C.compile=j,C.tokensToFunction=T,C.tokensToRegExp=S;const z=Object.create(null);function D(t,e,n){e=e||{};try{const n=z[t]||(z[t]=C.compile(t));return e.pathMatch&&(e[0]=e.pathMatch),n(e,{pretty:!0})}catch(t){return""}finally{delete e[0]}}function F(t,e,n,r){const o=e||[],i=n||Object.create(null),s=r||Object.create(null);t.forEach(t=>{!function t(e,n,r,o,i,s){const{path:a,name:c}=o;const u=o.pathToRegexpOptions||{};const h=function(t,e,n){n||(t=t.replace(/\/$/,""));return"/"===t[0]?t:null==e?t:O(`${e.path}/${t}`)}(a,i,u.strict);"boolean"==typeof o.caseSensitive&&(u.sensitive=o.caseSensitive);const p={path:h,regex:K(h,u),components:o.components||{default:o.component},instances:{},name:c,parent:i,matchAs:s,redirect:o.redirect,beforeEnter:o.beforeEnter,meta:o.meta||{},props:null==o.props?{}:o.components?o.props:{default:o.props}};o.children&&o.children.forEach(o=>{const i=s?O(`${s}/${o.path}`):void 0;t(e,n,r,o,p,i)});if(void 0!==o.alias){const s=Array.isArray(o.alias)?o.alias:[o.alias];s.forEach(s=>{const a={path:s,children:o.children};t(e,n,r,a,i,p.path||"/")})}n[p.path]||(e.push(p.path),n[p.path]=p);c&&(r[c]||(r[c]=p))}(o,i,s,t)});for(let t=0,e=o.length;t<e;t++)"*"===o[t]&&(o.push(o.splice(t,1)[0]),e--,t--);return{pathList:o,pathMap:i,nameMap:s}}function K(t,e){return C(t,[],e)}function J(t,n,r,o){let i="string"==typeof t?{path:t}:t;if(i._normalized)return i;if(i.name)return e({},t);if(!i.path&&i.params&&n){(i=e({},i))._normalized=!0;const t=e(e({},n.params),i.params);if(n.name)i.name=n.name,i.params=t;else if(n.matched.length){const e=n.matched[n.matched.length-1].path;i.path=D(e,t,n.path)}return i}const s=function(t){let e="",n="";const r=t.indexOf("#");r>=0&&(e=t.slice(r),t=t.slice(0,r));const o=t.indexOf("?");return o>=0&&(n=t.slice(o+1),t=t.slice(0,o)),{path:t,query:n,hash:e}}(i.path||""),a=n&&n.path||"/",u=s.path?E(s.path,a,r||i.append):a,h=function(t,e={},n){const r=n||c;let o;try{o=r(t||"")}catch(t){o={}}for(const t in e)o[t]=e[t];return o}(s.query,i.query,o&&o.options.parseQuery);let p=i.hash||s.hash;return p&&"#"!==p.charAt(0)&&(p=`#${p}`),{_normalized:!0,path:u,query:h,hash:p}}function N(t,e){const{pathList:n,pathMap:r,nameMap:o}=F(t);function i(t,i,s){const c=J(t,i,!1,e),{name:u}=c;if(u){const t=o[u];if(!t)return a(null,c);const e=t.regex.keys.filter(t=>!t.optional).map(t=>t.name);if("object"!=typeof c.params&&(c.params={}),i&&"object"==typeof i.params)for(const t in i.params)!(t in c.params)&&e.indexOf(t)>-1&&(c.params[t]=i.params[t]);if(t)return c.path=D(t.path,c.params),a(t,c,s)}else if(c.path){c.params={};for(let t=0;t<n.length;t++){const e=n[t],o=r[e];if(Q(o.regex,c.path,c.params))return a(o,c,s)}}return a(null,c)}function s(t,n){const r=t.redirect;let s="function"==typeof r?r(p(t,n,null,e)):r;if("string"==typeof s&&(s={path:s}),!s||"object"!=typeof s)return a(null,n);const c=s,{name:u,path:h}=c;let{query:l,hash:f,params:d}=n;if(l=c.hasOwnProperty("query")?c.query:l,f=c.hasOwnProperty("hash")?c.hash:f,d=c.hasOwnProperty("params")?c.params:d,u){o[u];return i({_normalized:!0,name:u,query:l,hash:f,params:d},void 0,n)}if(h){const e=function(t,e){return E(t,e.parent?e.parent.path:"/",!0)}(h,t);return i({_normalized:!0,path:D(e,d),query:l,hash:f},void 0,n)}return a(null,n)}function a(t,n,r){return t&&t.redirect?s(t,r||n):t&&t.matchAs?function(t,e,n){const r=i({_normalized:!0,path:D(n,e.params)});if(r){const t=r.matched,n=t[t.length-1];return e.params=r.params,a(n,e)}return a(null,e)}(0,n,t.matchAs):p(t,n,r,e)}return{match:i,addRoutes:function(t){F(t,n,r,o)}}}function Q(t,e,n){const r=e.match(t);if(!r)return!1;if(!n)return!0;for(let e=1,o=r.length;e<o;++e){const o=t.keys[e-1],i="string"==typeof r[e]?decodeURIComponent(r[e]):r[e];o&&(n[o.name||"pathMatch"]=i)}return!0}const X=Object.create(null);function Y(){window.history.replaceState({key:at()},"",window.location.href.replace(window.location.origin,"")),window.addEventListener("popstate",t=>{G(),t.state&&t.state.key&&function(t){it=t}(t.state.key)})}function W(t,e,n,r){if(!t.app)return;const o=t.options.scrollBehavior;o&&t.app.$nextTick(()=>{const i=function(){const t=at();if(t)return X[t]}(),s=o.call(t,e,n,r?i:null);s&&("function"==typeof s.then?s.then(t=>{nt(t,i)}).catch(t=>{}):nt(s,i))})}function G(){const t=at();t&&(X[t]={x:window.pageXOffset,y:window.pageYOffset})}function Z(t){return et(t.x)||et(t.y)}function tt(t){return{x:et(t.x)?t.x:window.pageXOffset,y:et(t.y)?t.y:window.pageYOffset}}function et(t){return"number"==typeof t}function nt(t,e){const n="object"==typeof t;if(n&&"string"==typeof t.selector){const n=document.querySelector(t.selector);if(n){let o=t.offset&&"object"==typeof t.offset?t.offset:{};e=function(t,e){const n=document.documentElement.getBoundingClientRect(),r=t.getBoundingClientRect();return{x:r.left-n.left-e.x,y:r.top-n.top-e.y}}(n,o={x:et((r=o).x)?r.x:0,y:et(r.y)?r.y:0})}else Z(t)&&(e=tt(t))}else n&&Z(t)&&(e=tt(t));var r;e&&window.scrollTo(e.x,e.y)}const rt=R&&function(){const t=window.navigator.userAgent;return(-1===t.indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone"))&&(window.history&&"pushState"in window.history)}(),ot=R&&window.performance&&window.performance.now?window.performance:Date;let it=st();function st(){return ot.now().toFixed(3)}function at(){return it}function ct(t,e){G();const n=window.history;try{e?n.replaceState({key:it},"",t):(it=st(),n.pushState({key:it},"",t))}catch(n){window.location[e?"replace":"assign"](t)}}function ut(t){ct(t,!0)}function ht(t,e,n){const r=o=>{o>=t.length?n():t[o]?e(t[o],()=>{r(o+1)}):r(o+1)};r(0)}function pt(e){return(n,r,o)=>{let i=!1,s=0,a=null;lt(e,(e,n,r,c)=>{if("function"==typeof e&&void 0===e.cid){i=!0,s++;const n=yt(t=>{(function(t){return t.__esModule||dt&&"Module"===t[Symbol.toStringTag]})(t)&&(t=t.default),e.resolved="function"==typeof t?t:k.extend(t),r.components[c]=t,--s<=0&&o()}),u=yt(e=>{const n=`Failed to resolve async component ${c}: ${e}`;a||(a=t(e)?e:new Error(n),o(a))});let h;try{h=e(n,u)}catch(t){u(t)}if(h)if("function"==typeof h.then)h.then(n,u);else{const t=h.component;t&&"function"==typeof t.then&&t.then(n,u)}}}),i||o()}}function lt(t,e){return ft(t.map(t=>Object.keys(t.components).map(n=>e(t.components[n],t.instances[n],t,n))))}function ft(t){return Array.prototype.concat.apply([],t)}const dt="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;function yt(t){let e=!1;return function(...n){if(!e)return e=!0,t.apply(this,n)}}class mt{constructor(t,e){this.router=t,this.base=function(t){if(!t)if(R){const e=document.querySelector("base");t=(t=e&&e.getAttribute("href")||"/").replace(/^https?:\/\/[^\/]+/,"")}else t="/";"/"!==t.charAt(0)&&(t="/"+t);return t.replace(/\/$/,"")}(e),this.current=f,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[]}listen(t){this.cb=t}onReady(t,e){this.ready?t():(this.readyCbs.push(t),e&&this.readyErrorCbs.push(e))}onError(t){this.errorCbs.push(t)}transitionTo(t,e,n){const r=this.router.match(t,this.current);this.confirmTransition(r,()=>{this.updateRoute(r),e&&e(r),this.ensureURL(),this.ready||(this.ready=!0,this.readyCbs.forEach(t=>{t(r)}))},t=>{n&&n(t),t&&!this.ready&&(this.ready=!0,this.readyErrorCbs.forEach(e=>{e(t)}))})}confirmTransition(e,n,r){const o=this.current,i=e=>{t(e)&&(this.errorCbs.length?this.errorCbs.forEach(t=>{t(e)}):console.error(e)),r&&r(e)};if(m(e,o)&&e.matched.length===o.matched.length)return this.ensureURL(),i();const{updated:s,deactivated:a,activated:c}=function(t,e){let n;const r=Math.max(t.length,e.length);for(n=0;n<r&&t[n]===e[n];n++);return{updated:e.slice(0,n),activated:e.slice(n),deactivated:t.slice(n)}}(this.current.matched,e.matched),u=[].concat(function(t){return gt(t,"beforeRouteLeave",bt,!0)}(a),this.router.beforeHooks,function(t){return gt(t,"beforeRouteUpdate",bt)}(s),c.map(t=>t.beforeEnter),pt(c));this.pending=e;const h=(n,r)=>{if(this.pending!==e)return i();try{n(e,o,e=>{!1===e||t(e)?(this.ensureURL(!0),i(e)):"string"==typeof e||"object"==typeof e&&("string"==typeof e.path||"string"==typeof e.name)?(i(),"object"==typeof e&&e.replace?this.replace(e):this.push(e)):r(e)})}catch(t){i(t)}};ht(u,h,()=>{const t=[];ht(function(t,e,n){return gt(t,"beforeRouteEnter",(t,r,o,i)=>(function(t,e,n,r,o){return function(i,s,a){return t(i,s,t=>{a(t),"function"==typeof t&&r.push(()=>{!function t(e,n,r,o){n[r]&&!n[r]._isBeingDestroyed?e(n[r]):o()&&setTimeout(()=>{t(e,n,r,o)},16)}(t,e.instances,n,o)})})}})(t,o,i,e,n))}(c,t,()=>this.current===e).concat(this.router.resolveHooks),h,()=>{if(this.pending!==e)return i();this.pending=null,n(e),this.router.app&&this.router.app.$nextTick(()=>{t.forEach(t=>{t()})})})})}updateRoute(t){const e=this.current;this.current=t,this.cb&&this.cb(t),this.router.afterHooks.forEach(n=>{n&&n(t,e)})}}function gt(t,e,n,r){const o=lt(t,(t,r,o,i)=>{const s=function(t,e){"function"!=typeof t&&(t=k.extend(t));return t.options[e]}(t,e);if(s)return Array.isArray(s)?s.map(t=>n(t,r,o,i)):n(s,r,o,i)});return ft(r?o.reverse():o)}function bt(t,e){if(e)return function(){return t.apply(e,arguments)}}class wt extends mt{constructor(t,e){super(t,e);const n=t.options.scrollBehavior,r=rt&&n;r&&Y();const o=vt(this.base);window.addEventListener("popstate",e=>{const n=this.current,i=vt(this.base);this.current===f&&i===o||this.transitionTo(i,e=>{r&&W(t,e,n,!0)})})}go(t){window.history.go(t)}push(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{ct(O(this.base+t.fullPath)),W(this.router,t,r,!1),e&&e(t)},n)}replace(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{ut(O(this.base+t.fullPath)),W(this.router,t,r,!1),e&&e(t)},n)}ensureURL(t){if(vt(this.base)!==this.current.fullPath){const e=O(this.base+this.current.fullPath);t?ct(e):ut(e)}}getCurrentLocation(){return vt(this.base)}}function vt(t){let e=decodeURI(window.location.pathname);return t&&0===e.indexOf(t)&&(e=e.slice(t.length)),(e||"/")+window.location.search+window.location.hash}class xt extends mt{constructor(t,e,n){super(t,e),n&&function(t){const e=vt(t);if(!/^\/#/.test(e))return window.location.replace(O(t+"/#"+e)),!0}(this.base)||kt()}setupListeners(){const t=this.router.options.scrollBehavior,e=rt&&t;e&&Y(),window.addEventListener(rt?"popstate":"hashchange",()=>{const t=this.current;kt()&&this.transitionTo(Rt(),n=>{e&&W(this.router,n,t,!0),rt||At(n.fullPath)})})}push(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{Ot(t.fullPath),W(this.router,t,r,!1),e&&e(t)},n)}replace(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{At(t.fullPath),W(this.router,t,r,!1),e&&e(t)},n)}go(t){window.history.go(t)}ensureURL(t){const e=this.current.fullPath;Rt()!==e&&(t?Ot(e):At(e))}getCurrentLocation(){return Rt()}}function kt(){const t=Rt();return"/"===t.charAt(0)||(At("/"+t),!1)}function Rt(){let t=window.location.href;const e=t.indexOf("#");if(e<0)return"";const n=(t=t.slice(e+1)).indexOf("?");if(n<0){const e=t.indexOf("#");t=e>-1?decodeURI(t.slice(0,e))+t.slice(e):decodeURI(t)}else n>-1&&(t=decodeURI(t.slice(0,n))+t.slice(n));return t}function Et(t){const e=window.location.href,n=e.indexOf("#");return`${n>=0?e.slice(0,n):e}#${t}`}function Ot(t){rt?ct(Et(t)):window.location.hash=t}function At(t){rt?ut(Et(t)):window.location.replace(Et(t))}class Ct extends mt{constructor(t,e){super(t,e),this.stack=[],this.index=-1}push(t,e,n){this.transitionTo(t,t=>{this.stack=this.stack.slice(0,this.index+1).concat(t),this.index++,e&&e(t)},n)}replace(t,e,n){this.transitionTo(t,t=>{this.stack=this.stack.slice(0,this.index).concat(t),e&&e(t)},n)}go(t){const e=this.index+t;if(e<0||e>=this.stack.length)return;const n=this.stack[e];this.confirmTransition(n,()=>{this.index=e,this.updateRoute(n)})}getCurrentLocation(){const t=this.stack[this.stack.length-1];return t?t.fullPath:"/"}ensureURL(){}}class $t{constructor(t={}){this.app=null,this.apps=[],this.options=t,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=N(t.routes||[],this);let e=t.mode||"hash";switch(this.fallback="history"===e&&!rt&&!1!==t.fallback,this.fallback&&(e="hash"),R||(e="abstract"),this.mode=e,e){case"history":this.history=new wt(this,t.base);break;case"hash":this.history=new xt(this,t.base,this.fallback);break;case"abstract":this.history=new Ct(this,t.base)}}match(t,e,n){return this.matcher.match(t,e,n)}get currentRoute(){return this.history&&this.history.current}init(t){if(this.apps.push(t),t.$once("hook:destroyed",()=>{const e=this.apps.indexOf(t);e>-1&&this.apps.splice(e,1),this.app===t&&(this.app=this.apps[0]||null)}),this.app)return;this.app=t;const e=this.history;if(e instanceof wt)e.transitionTo(e.getCurrentLocation());else if(e instanceof xt){const t=()=>{e.setupListeners()};e.transitionTo(e.getCurrentLocation(),t,t)}e.listen(t=>{this.apps.forEach(e=>{e._route=t})})}beforeEach(t){return jt(this.beforeHooks,t)}beforeResolve(t){return jt(this.resolveHooks,t)}afterEach(t){return jt(this.afterHooks,t)}onReady(t,e){this.history.onReady(t,e)}onError(t){this.history.onError(t)}push(t,e,n){this.history.push(t,e,n)}replace(t,e,n){this.history.replace(t,e,n)}go(t){this.history.go(t)}back(){this.go(-1)}forward(){this.go(1)}getMatchedComponents(t){const e=t?t.matched?t:this.resolve(t).route:this.currentRoute;return e?[].concat.apply([],e.matched.map(t=>Object.keys(t.components).map(e=>t.components[e]))):[]}resolve(t,e,n){const r=J(t,e=e||this.history.current,n,this),o=this.match(r,e),i=o.redirectedFrom||o.fullPath;return{location:r,route:o,href:function(t,e,n){var r="hash"===n?"#"+e:e;return t?O(t+"/"+r):r}(this.history.base,i,this.mode),normalizedTo:r,resolved:o}}addRoutes(t){this.matcher.addRoutes(t),this.history.current!==f&&this.history.transitionTo(this.history.getCurrentLocation())}}function jt(t,e){return t.push(e),()=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)}}$t.install=function t(e){if(t.installed&&k===e)return;t.installed=!0,k=e;const r=t=>void 0!==t,o=(t,e)=>{let n=t.$options._parentVnode;r(n)&&r(n=n.data)&&r(n=n.registerRouteInstance)&&n(t,e)};e.mixin({beforeCreate(){r(this.$options.router)?(this._routerRoot=this,this._router=this.$options.router,this._router.init(this),e.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,o(this,this)},destroyed(){o(this)}}),Object.defineProperty(e.prototype,"$router",{get(){return this._routerRoot._router}}),Object.defineProperty(e.prototype,"$route",{get(){return this._routerRoot._route}}),e.component("RouterView",n),e.component("RouterLink",v);const i=e.config.optionMergeStrategies;i.beforeRouteEnter=i.beforeRouteLeave=i.beforeRouteUpdate=i.created},$t.version="3.0.6",R&&window.Vue&&window.Vue.use($t);export default $t;