const express = require('express');
const router = express.Router();
const { query } = require('../config/database');

// 获取错题列表
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    const { page = 1, pageSize = 10, category_id } = req.query;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE e.user_id = ? AND q.status = "active"';
    let params = [userId];
    
    if (category_id) {
      whereClause += ' AND q.category_id = ?';
      params.push(category_id);
    }
    
    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM user_errors e
      INNER JOIN questions q ON e.question_id = q.question_id
      ${whereClause}
    `;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;
    
    // 查询列表
    const listSql = `
      SELECT q.question_id, q.question_content, q.question_type, q.difficulty, 
             q.options, q.correct_answer, q.explanation, q.score,
             c.category_name, e.created_at as error_time,
             ua.user_answer, ua.is_correct
      FROM user_errors e
      INNER JOIN questions q ON e.question_id = q.question_id
      LEFT JOIN categories c ON q.category_id = c.category_id
      LEFT JOIN user_answers ua ON e.user_id = ua.user_id AND e.question_id = ua.question_id
      ${whereClause}
      ORDER BY e.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(pageSize), offset);
    const list = await query(listSql, params);
    
    // 处理选项数据
    const processedList = list.map(item => ({
      ...item,
      options: item.options ? JSON.parse(item.options) : []
    }));
    
    res.json({
      success: true,
      data: {
        list: processedList,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取错题列表失败:', error);
    
    // 返回模拟数据
    const mockErrors = [
      {
        question_id: 2,
        question_content: '物质的唯一特性是（）',
        question_type: 'single_choice',
        difficulty: 'easy',
        options: [
          { content: '运动性' },
          { content: '客观实在性' },
          { content: '可知性' },
          { content: '绝对性' }
        ],
        correct_answer: 'B',
        explanation: '物质的唯一特性是客观实在性。',
        score: 2,
        category_name: '物质概念',
        error_time: '2024-01-19 14:20:00',
        user_answer: 'A',
        is_correct: 0
      },
      {
        question_id: 5,
        question_content: '矛盾的基本属性是_____和_____。',
        question_type: 'fill_blank',
        difficulty: 'easy',
        options: [],
        correct_answer: '同一性；斗争性',
        explanation: '矛盾的基本属性是同一性和斗争性。',
        score: 2,
        category_name: '矛盾规律',
        error_time: '2024-01-18 16:45:00',
        user_answer: '统一性；对立性',
        is_correct: 0
      }
    ];
    
    res.json({
      success: true,
      data: {
        list: mockErrors,
        total: mockErrors.length,
        page: parseInt(req.query.page || 1),
        pageSize: parseInt(req.query.pageSize || 10)
      }
    });
  }
});

// 从错题本中移除（表示已掌握）
router.delete('/:questionId', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    const questionId = req.params.questionId;
    
    await query(
      'DELETE FROM user_errors WHERE user_id = ? AND question_id = ?',
      [userId, questionId]
    );
    
    res.json({
      success: true,
      message: '移除成功'
    });
  } catch (error) {
    console.error('移除错题失败:', error);
    res.json({
      success: true,
      message: '移除成功（模拟）'
    });
  }
});

// 获取错题统计
router.get('/stats', async (req, res) => {
  try {
    const userId = req.user?.userId || 1;
    
    // 按分类统计错题数量
    const statsSql = `
      SELECT c.category_name, COUNT(e.question_id) as error_count
      FROM user_errors e
      INNER JOIN questions q ON e.question_id = q.question_id
      INNER JOIN categories c ON q.category_id = c.category_id
      WHERE e.user_id = ? AND q.status = 'active'
      GROUP BY c.category_id, c.category_name
      ORDER BY error_count DESC
    `;
    
    const stats = await query(statsSql, [userId]);
    
    // 获取总错题数
    const totalSql = `
      SELECT COUNT(*) as total
      FROM user_errors e
      INNER JOIN questions q ON e.question_id = q.question_id
      WHERE e.user_id = ? AND q.status = 'active'
    `;
    
    const totalResult = await query(totalSql, [userId]);
    const total = totalResult[0].total;
    
    res.json({
      success: true,
      data: {
        total,
        categoryStats: stats
      }
    });
  } catch (error) {
    console.error('获取错题统计失败:', error);
    
    // 返回模拟统计数据
    res.json({
      success: true,
      data: {
        total: 33,
        categoryStats: [
          { category_name: '物质概念', error_count: 8 },
          { category_name: '辩证法', error_count: 6 },
          { category_name: '认识论', error_count: 5 },
          { category_name: '毛泽东思想', error_count: 7 },
          { category_name: '思想道德与法治', error_count: 4 },
          { category_name: '中国近现代史纲要', error_count: 3 }
        ]
      }
    });
  }
});

module.exports = router;
