<!--practice.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section card">
    <view class="search-input-wrapper">
      <input 
        value="{{searchKeyword}}" 
        placeholder="搜索题目分类" 
        class="search-input"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <button class="search-btn" bindtap="onSearch">搜索</button>
    </view>
  </view>

  <!-- 分类列表 -->
  <view class="category-section card">
    <view class="section-title">题目分类</view>
    <view class="category-list">
      <view 
        wx:for="{{categories}}" 
        wx:key="id"
        class="category-item"
        bindtap="selectCategory"
        data-category="{{item}}"
      >
        <view class="category-icon">{{item.icon}}</view>
        <view class="category-info">
          <text class="category-name">{{item.name}}</text>
          <text class="category-count">{{item.questionCount}}道题</text>
        </view>
        <view class="category-arrow">
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 推荐练习 -->
  <view class="recommend-section card">
    <view class="section-title">推荐练习</view>
    <view class="recommend-list">
      <view 
        wx:for="{{recommendList}}" 
        wx:key="id"
        class="recommend-item"
        bindtap="startPractice"
        data-item="{{item}}"
      >
        <view class="recommend-cover">{{item.cover}}</view>
        <view class="recommend-info">
          <text class="recommend-title">{{item.title}}</text>
          <text class="recommend-desc">{{item.description}}</text>
          <view class="recommend-meta">
            <text class="meta-item">{{item.questionCount}}题</text>
            <text class="meta-item">难度: {{item.difficultyText}}</text>
          </view>
        </view>
        <view class="recommend-action">
          <button class="start-btn btn btn-primary" size="mini">开始</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速练习 -->
  <view class="quick-section card">
    <view class="section-title">快速练习</view>
    <view class="quick-options">
      <view class="quick-item" bindtap="quickPractice" data-type="random">
        <view class="quick-icon">🎲</view>
        <text class="quick-text">随机练习</text>
      </view>
      <view class="quick-item" bindtap="quickPractice" data-type="wrong">
        <view class="quick-icon">❌</view>
        <text class="quick-text">错题练习</text>
      </view>
      <view class="quick-item" bindtap="quickPractice" data-type="favorite">
        <view class="quick-icon">❤️</view>
        <text class="quick-text">收藏练习</text>
      </view>
      <view class="quick-item" bindtap="quickPractice" data-type="mock">
        <view class="quick-icon">📝</view>
        <text class="quick-text">模拟考试</text>
      </view>
    </view>
  </view>
</view>
