{"_from": "is-core-module@^2.16.0", "_id": "is-core-module@2.16.1", "_inBundle": false, "_integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "_location": "/is-core-module", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-core-module@^2.16.0", "name": "is-core-module", "escapedName": "is-core-module", "rawSpec": "^2.16.0", "saveSpec": null, "fetchSpec": "^2.16.0"}, "_requiredBy": ["/resolve"], "_resolved": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.16.1.tgz", "_shasum": "2a98801a849f43e2add644fbb6bc6229b19a4ef4", "_spec": "is-core-module@^2.16.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\resolve", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-core-module/issues"}, "bundleDependencies": false, "dependencies": {"hasown": "^2.0.2"}, "deprecated": false, "description": "Is this specifier a node.js core module?", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-core-module", "keywords": ["core", "modules", "module", "npm", "node", "dependencies"], "license": "MIT", "main": "index.js", "name": "is-core-module", "publishConfig": {"ignore": [".github"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-core-module.git"}, "scripts": {"lint": "eslint .", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "2.16.1"}