{"_from": "is-dotfile@^1.0.0", "_id": "is-dotfile@1.0.3", "_inBundle": false, "_integrity": "sha512-9YclgOGtN/f8zx0Pr4FQYMdibBiTaH3sn52vjYip4ZSf6C4/6RfTEZ+MR4GvKhCxdPh21Bg42/WL55f6KSnKpg==", "_location": "/is-dotfile", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-dotfile@^1.0.0", "name": "is-dotfile", "escapedName": "is-dotfile", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/parse-glob"], "_resolved": "https://registry.npmmirror.com/is-dotfile/-/is-dotfile-1.0.3.tgz", "_shasum": "a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1", "_spec": "is-dotfile@^1.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\parse-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-dotfile/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://exitiumonline.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "deprecated": false, "description": "Return true if a file path is (or has) a dotfile. Returns false if the path is a dot directory.", "devDependencies": {"benchmarked": "^0.1.3", "dotfile-regex": "^0.1.2", "gulp-format-md": "^0.1.12", "mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-dotfile", "keywords": ["detect", "dot", "dotfile", "expression", "file", "filepath", "find", "fs", "is", "match", "path", "regex", "regexp", "regular"], "license": "MIT", "main": "index.js", "name": "is-dotfile", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-dotfile.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["dotdir-regex", "dotfile-regex", "is-dotdir", "is-glob"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "1.0.3"}