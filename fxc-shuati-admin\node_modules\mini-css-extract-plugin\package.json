{"_from": "mini-css-extract-plugin@^0.9.0", "_id": "mini-css-extract-plugin@0.9.0", "_inBundle": false, "_integrity": "sha512-lp3GeY7ygcgAmVIcRPBVhIkf8Us7FZjA+ILpal44qLdSu11wmjKQ3d9k15lfD7pO4esu9eUIAW7qiYIBppv40A==", "_location": "/mini-css-extract-plugin", "_phantomChildren": {"ajv": "6.12.6", "ajv-errors": "1.0.1", "ajv-keywords": "3.5.2", "big.js": "5.2.2", "emojis-list": "3.0.0", "minimist": "1.2.8", "object-assign": "4.1.1", "prepend-http": "1.0.4", "query-string": "4.3.4", "sort-keys": "1.1.2"}, "_requested": {"type": "range", "registry": true, "raw": "mini-css-extract-plugin@^0.9.0", "name": "mini-css-extract-plugin", "escapedName": "mini-css-extract-plugin", "rawSpec": "^0.9.0", "saveSpec": null, "fetchSpec": "^0.9.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "https://registry.npmmirror.com/mini-css-extract-plugin/-/mini-css-extract-plugin-0.9.0.tgz", "_shasum": "47f2cf07aa165ab35733b1fc97d4c46c0564339e", "_spec": "mini-css-extract-plugin@^0.9.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack-contrib/mini-css-extract-plugin/issues"}, "bundleDependencies": false, "dependencies": {"loader-utils": "^1.1.0", "normalize-url": "1.9.1", "schema-utils": "^1.0.0", "webpack-sources": "^1.1.0"}, "deprecated": false, "description": "extracts CSS into separate files", "devDependencies": {"@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.6", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-eslint": "^10.0.2", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "css-loader": "^3.3.2", "del": "^4.1.1", "del-cli": "^1.1.0", "es-check": "^5.0.0", "eslint": "^6.7.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.19.1", "file-loader": "^4.0.0", "husky": "^3.0.0", "jest": "^24.8.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "memory-fs": "^0.4.1", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "webpack": "^4.41.3", "webpack-cli": "^3.3.6", "webpack-dev-server": "^3.7.2"}, "engines": {"node": ">= 6.9.0"}, "files": ["dist"], "homepage": "https://github.com/webpack-contrib/mini-css-extract-plugin", "keywords": ["webpack", "css", "extract", "hmr"], "license": "MIT", "main": "dist/cjs.js", "name": "mini-css-extract-plugin", "peerDependencies": {"webpack": "^4.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/mini-css-extract-plugin.git"}, "scripts": {"build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint --cache src test", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "postbuild": "es-check es5 dist/hmr/hotModuleReplacement.js", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "cross-env NODE_ENV=test npm run test:coverage", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:manual": "npm run build && webpack-dev-server test/manual/src/index.js --open --config test/manual/webpack.config.js", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch"}, "version": "0.9.0"}