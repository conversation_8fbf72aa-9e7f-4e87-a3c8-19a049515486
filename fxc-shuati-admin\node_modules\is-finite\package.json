{"_from": "is-finite@^1.0.0", "_id": "is-finite@1.1.0", "_inBundle": false, "_integrity": "sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==", "_location": "/is-finite", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-finite@^1.0.0", "name": "is-finite", "escapedName": "is-finite", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/repeating"], "_resolved": "https://registry.npmmirror.com/is-finite/-/is-finite-1.1.0.tgz", "_shasum": "904135c77fb42c0641d6aa1bcdbc4daa8da082f3", "_spec": "is-finite@^1.0.0", "_where": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\repeating", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-finite/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ES2015 Number.isFinite() ponyfill", "devDependencies": {"ava": "^3.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/is-finite#readme", "keywords": ["es2015", "ponyfill", "polyfill", "shim", "number", "finite", "is"], "license": "MIT", "name": "is-finite", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-finite.git"}, "scripts": {"test": "ava"}, "version": "1.1.0"}