{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\components\\SvgIcon\\index.vue", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\babel.config.js", "mtime": 1637306514000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KCi8vIGRvYzogaHR0cHM6Ly9wYW5qaWFjaGVuLmdpdGh1Yi5pby92dWUtZWxlbWVudC1hZG1pbi1zaXRlL2ZlYXR1cmUvY29tcG9uZW50L3N2Zy1pY29uLmh0bWwjdXNhZ2UKaW1wb3J0IHsgaXNFeHRlcm5hbCBhcyBfaXNFeHRlcm5hbCB9IGZyb20gJ0AvdXRpbHMvdmFsaWRhdGUnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1N2Z0ljb24nLAogIHByb3BzOiB7CiAgICBpY29uQ2xhc3M6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIGNsYXNzTmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgaXNFeHRlcm5hbDogZnVuY3Rpb24gaXNFeHRlcm5hbCgpIHsKICAgICAgcmV0dXJuIF9pc0V4dGVybmFsKHRoaXMuaWNvbkNsYXNzKTsKICAgIH0sCiAgICBpY29uTmFtZTogZnVuY3Rpb24gaWNvbk5hbWUoKSB7CiAgICAgIHJldHVybiAiI2ljb24tIi5jb25jYXQodGhpcy5pY29uQ2xhc3MpOwogICAgfSwKICAgIHN2Z0NsYXNzOiBmdW5jdGlvbiBzdmdDbGFzcygpIHsKICAgICAgaWYgKHRoaXMuY2xhc3NOYW1lKSB7CiAgICAgICAgcmV0dXJuICdzdmctaWNvbiAnICsgdGhpcy5jbGFzc05hbWU7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICdzdmctaWNvbic7CiAgICAgIH0KICAgIH0sCiAgICBzdHlsZUV4dGVybmFsSWNvbjogZnVuY3Rpb24gc3R5bGVFeHRlcm5hbEljb24oKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgbWFzazogInVybCgiLmNvbmNhdCh0aGlzLmljb25DbGFzcywgIikgbm8tcmVwZWF0IDUwJSA1MCUiKSwKICAgICAgICAnLXdlYmtpdC1tYXNrJzogInVybCgiLmNvbmNhdCh0aGlzLmljb25DbGFzcywgIikgbm8tcmVwZWF0IDUwJSA1MCUiKQogICAgICB9OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["isExternal", "name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "iconName", "concat", "svgClass", "styleExternalIcon", "mask"], "sources": ["src/components/SvgIcon/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\n// doc: https://panjiachen.github.io/vue-element-admin-site/feature/component/svg-icon.html#usage\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.iconClass)\n    },\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    },\n    styleExternalIcon() {\n      return {\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n.svg-external-icon {\n  background-color: currentColor;\n  mask-size: cover!important;\n  display: inline-block;\n}\n</style>\n"], "mappings": ";;;;;;;;AAQA;AACA,SAAAA,UAAA,IAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAC,QAAA;IACAT,UAAA,WAAAA,WAAA;MACA,OAAAA,WAAA,MAAAG,SAAA;IACA;IACAO,QAAA,WAAAA,SAAA;MACA,gBAAAC,MAAA,MAAAR,SAAA;IACA;IACAS,QAAA,WAAAA,SAAA;MACA,SAAAL,SAAA;QACA,0BAAAA,SAAA;MACA;QACA;MACA;IACA;IACAM,iBAAA,WAAAA,kBAAA;MACA;QACAC,IAAA,SAAAH,MAAA,MAAAR,SAAA;QACA,uBAAAQ,MAAA,MAAAR,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}