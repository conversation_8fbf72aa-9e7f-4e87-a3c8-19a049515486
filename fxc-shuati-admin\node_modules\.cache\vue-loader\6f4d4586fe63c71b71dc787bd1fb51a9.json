{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\roles.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\roles.vue", "mtime": 1752570845684}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFJvbGVMaXN0LCBjcmVhdGVSb2xlLCB1cGRhdGVSb2xlLCBkZWxldGVSb2xlLCBnZXRSb2xlUGVybWlzc2lvbnMsIHVwZGF0ZVJvbGVQZXJtaXNzaW9ucyB9IGZyb20gJ0AvYXBpL3VzZXJzJwppbXBvcnQgeyBnZXRQZXJtaXNzaW9uVHJlZSB9IGZyb20gJ0AvYXBpL3VzZXJzJwppbXBvcnQgd2F2ZXMgZnJvbSAnQC9kaXJlY3RpdmUvd2F2ZXMnCmltcG9ydCB7IHBhcnNlVGltZSB9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdSb2xlTGlzdCcsCiAgY29tcG9uZW50czogeyBQYWdpbmF0aW9uIH0sCiAgZGlyZWN0aXZlczogeyB3YXZlcyB9LAogIGZpbHRlcnM6IHsKICAgIHBhcnNlVGltZQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRhYmxlS2V5OiAwLAogICAgICBsaXN0OiBbXSwKICAgICAgdG90YWw6IDAsCiAgICAgIGxpc3RMb2FkaW5nOiB0cnVlLAogICAgICBsaXN0UXVlcnk6IHsKICAgICAgICBwYWdlOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBrZXl3b3JkOiAnJywKICAgICAgICBzdGF0dXM6ICcnCiAgICAgIH0sCiAgICAgIHRlbXA6IHsKICAgICAgICByb2xlX2lkOiB1bmRlZmluZWQsCiAgICAgICAgcm9sZV9uYW1lOiAnJywKICAgICAgICByb2xlX2NvZGU6ICcnLAogICAgICAgIHJvbGVfZGVzYzogJycsCiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJywKICAgICAgICBzb3J0X29yZGVyOiAwCiAgICAgIH0sCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nU3RhdHVzOiAnJywKICAgICAgdGV4dE1hcDogewogICAgICAgIHVwZGF0ZTogJ+e8lui+keinkuiJsicsCiAgICAgICAgY3JlYXRlOiAn5re75Yqg6KeS6ImyJwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIHJvbGVfbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfop5LoibLlkI3np7DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgcm9sZV9jb2RlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+inkuiJsue8lueggeS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9XQogICAgICB9LAogICAgICBwZXJtaXNzaW9uRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHBlcm1pc3Npb25Mb2FkaW5nOiBmYWxzZSwKICAgICAgcGVybWlzc2lvblRyZWU6IFtdLAogICAgICBwZXJtaXNzaW9uUHJvcHM6IHsKICAgICAgICBsYWJlbDogJ3Blcm1pc3Npb25fbmFtZScsCiAgICAgICAgY2hpbGRyZW46ICdjaGlsZHJlbicKICAgICAgfSwKICAgICAgY3VycmVudFJvbGU6IG51bGwKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWUKICAgICAgZ2V0Um9sZUxpc3QodGhpcy5saXN0UXVlcnkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdAogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRmlsdGVyKCkgewogICAgICB0aGlzLmxpc3RRdWVyeS5wYWdlID0gMQogICAgICB0aGlzLmdldExpc3QoKQogICAgfSwKICAgIHJlc2V0VGVtcCgpIHsKICAgICAgdGhpcy50ZW1wID0gewogICAgICAgIHJvbGVfaWQ6IHVuZGVmaW5lZCwKICAgICAgICByb2xlX25hbWU6ICcnLAogICAgICAgIHJvbGVfY29kZTogJycsCiAgICAgICAgcm9sZV9kZXNjOiAnJywKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgIHNvcnRfb3JkZXI6IDAKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUNyZWF0ZSgpIHsKICAgICAgdGhpcy5yZXNldFRlbXAoKQogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICdjcmVhdGUnCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICB9KQogICAgfSwKICAgIGNyZWF0ZURhdGEoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBjcmVhdGVSb2xlKHRoaXMudGVtcCkudGhlbigoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZQogICAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yib5bu65oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy50ZW1wID0gT2JqZWN0LmFzc2lnbih7fSwgcm93KQogICAgICB0aGlzLmRpYWxvZ1N0YXR1cyA9ICd1cGRhdGUnCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkYXRhRm9ybSddLmNsZWFyVmFsaWRhdGUoKQogICAgICB9KQogICAgfSwKICAgIHVwZGF0ZURhdGEoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBjb25zdCB0ZW1wRGF0YSA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMudGVtcCkKICAgICAgICAgIHVwZGF0ZVJvbGUodGVtcERhdGEucm9sZV9pZCwgdGVtcERhdGEpLnRoZW4oKCkgPT4gewogICAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+abtOaWsOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOivpeinkuiJsuWQl++8nycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBkZWxldGVSb2xlKHJvdy5yb2xlX2lkKS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlUGVybWlzc2lvbihyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Um9sZSA9IHJvdwogICAgICB0aGlzLnBlcm1pc3Npb25EaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLnBlcm1pc3Npb25Mb2FkaW5nID0gdHJ1ZQogICAgICAKICAgICAgLy8g6I635Y+W5p2D6ZmQ5qCRCiAgICAgIGdldFBlcm1pc3Npb25UcmVlKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wZXJtaXNzaW9uVHJlZSA9IHJlc3BvbnNlLmRhdGEKICAgICAgICAKICAgICAgICAvLyDojrflj5bop5LoibLlt7LmnInmnYPpmZAKICAgICAgICBnZXRSb2xlUGVybWlzc2lvbnMocm93LnJvbGVfaWQpLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgdGhpcy4kcmVmcy5wZXJtaXNzaW9uVHJlZS5zZXRDaGVja2VkS2V5cyhyZXMuZGF0YS5wZXJtaXNzaW9ucykKICAgICAgICAgICAgdGhpcy5wZXJtaXNzaW9uTG9hZGluZyA9IGZhbHNlCiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgdXBkYXRlUGVybWlzc2lvbnMoKSB7CiAgICAgIGNvbnN0IGNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5wZXJtaXNzaW9uVHJlZS5nZXRDaGVja2VkS2V5cygpCiAgICAgIGNvbnN0IGhhbGZDaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMucGVybWlzc2lvblRyZWUuZ2V0SGFsZkNoZWNrZWRLZXlzKCkKICAgICAgY29uc3QgcGVybWlzc2lvbnMgPSBbLi4uY2hlY2tlZEtleXMsIC4uLmhhbGZDaGVja2VkS2V5c10KICAgICAgCiAgICAgIHVwZGF0ZVJvbGVQZXJtaXNzaW9ucyh0aGlzLmN1cnJlbnRSb2xlLnJvbGVfaWQsIHsgcGVybWlzc2lvbnMgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5wZXJtaXNzaW9uRGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgIG1lc3NhZ2U6ICfmnYPpmZDorr7nva7miJDlip8nLAogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICB9KQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["roles.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "roles.vue", "sourceRoot": "src/views/users", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <div class=\"filter-container\">\n      <el-input\n        v-model=\"listQuery.keyword\"\n        placeholder=\"搜索角色名称、编码\"\n        style=\"width: 200px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-select\n        v-model=\"listQuery.status\"\n        placeholder=\"状态\"\n        clearable\n        style=\"width: 120px\"\n        class=\"filter-item\"\n      >\n        <el-option label=\"启用\" value=\"active\" />\n        <el-option label=\"禁用\" value=\"disabled\" />\n      </el-select>\n      <el-button\n        v-waves\n        class=\"filter-item\"\n        type=\"primary\"\n        icon=\"el-icon-search\"\n        @click=\"handleFilter\"\n      >\n        搜索\n      </el-button>\n      <el-button\n        class=\"filter-item\"\n        style=\"margin-left: 10px;\"\n        type=\"primary\"\n        icon=\"el-icon-plus\"\n        @click=\"handleCreate\"\n      >\n        添加角色\n      </el-button>\n    </div>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"role_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"角色名称\" prop=\"role_name\" width=\"150\" />\n      <el-table-column label=\"角色编码\" prop=\"role_code\" width=\"150\" />\n      <el-table-column label=\"角色描述\" prop=\"role_desc\" />\n      <el-table-column label=\"管理员数\" prop=\"admin_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"权限数\" prop=\"permission_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"250\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handlePermission(row)\">\n            权限\n          </el-button>\n          <el-button\n            v-if=\"row.role_id !== 1 && row.role_id !== 2\"\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"角色名称\" prop=\"role_name\">\n          <el-input v-model=\"temp.role_name\" />\n        </el-form-item>\n        <el-form-item label=\"角色编码\" prop=\"role_code\">\n          <el-input v-model=\"temp.role_code\" />\n        </el-form-item>\n        <el-form-item label=\"角色描述\" prop=\"role_desc\">\n          <el-input v-model=\"temp.role_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 权限设置对话框 -->\n    <el-dialog title=\"权限设置\" :visible.sync=\"permissionDialogVisible\" width=\"600px\">\n      <div v-loading=\"permissionLoading\">\n        <el-tree\n          ref=\"permissionTree\"\n          :data=\"permissionTree\"\n          :props=\"permissionProps\"\n          show-checkbox\n          node-key=\"permission_id\"\n          default-expand-all\n        />\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"permissionDialogVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"updatePermissions\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRoleList, createRole, updateRole, deleteRole, getRolePermissions, updateRolePermissions } from '@/api/users'\nimport { getPermissionTree } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'RoleList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: [],\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      temp: {\n        role_id: undefined,\n        role_name: '',\n        role_code: '',\n        role_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑角色',\n        create: '添加角色'\n      },\n      rules: {\n        role_name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],\n        role_code: [{ required: true, message: '角色编码不能为空', trigger: 'blur' }]\n      },\n      permissionDialogVisible: false,\n      permissionLoading: false,\n      permissionTree: [],\n      permissionProps: {\n        label: 'permission_name',\n        children: 'children'\n      },\n      currentRole: null\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getRoleList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        role_id: undefined,\n        role_name: '',\n        role_code: '',\n        role_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createRole(this.temp).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          updateRole(tempData.role_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该角色吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteRole(row.role_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    },\n    handlePermission(row) {\n      this.currentRole = row\n      this.permissionDialogVisible = true\n      this.permissionLoading = true\n      \n      // 获取权限树\n      getPermissionTree().then(response => {\n        this.permissionTree = response.data\n        \n        // 获取角色已有权限\n        getRolePermissions(row.role_id).then(res => {\n          this.$nextTick(() => {\n            this.$refs.permissionTree.setCheckedKeys(res.data.permissions)\n            this.permissionLoading = false\n          })\n        })\n      })\n    },\n    updatePermissions() {\n      const checkedKeys = this.$refs.permissionTree.getCheckedKeys()\n      const halfCheckedKeys = this.$refs.permissionTree.getHalfCheckedKeys()\n      const permissions = [...checkedKeys, ...halfCheckedKeys]\n      \n      updateRolePermissions(this.currentRole.role_id, { permissions }).then(() => {\n        this.permissionDialogVisible = false\n        this.$notify({\n          title: '成功',\n          message: '权限设置成功',\n          type: 'success',\n          duration: 2000\n        })\n        this.getList()\n      })\n    }\n  }\n}\n</script>\n"]}]}