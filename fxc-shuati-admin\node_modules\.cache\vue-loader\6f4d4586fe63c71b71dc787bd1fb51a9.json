{"remainingRequest": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\roles.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\src\\views\\users\\roles.vue", "mtime": 1752566462265}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\code\\fanxiaochang\\fxc-shuati-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFJvbGVMaXN0LCBjcmVhdGVSb2xlLCB1cGRhdGVSb2xlLCBkZWxldGVSb2xlLCBnZXRSb2xlUGVybWlzc2lvbnMsIHVwZGF0ZVJvbGVQZXJtaXNzaW9ucyB9IGZyb20gJ0AvYXBpL3VzZXJzJwppbXBvcnQgeyBnZXRQZXJtaXNzaW9uVHJlZSB9IGZyb20gJ0AvYXBpL3VzZXJzJwppbXBvcnQgd2F2ZXMgZnJvbSAnQC9kaXJlY3RpdmUvd2F2ZXMnCmltcG9ydCB7IHBhcnNlVGltZSB9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdSb2xlTGlzdCcsCiAgY29tcG9uZW50czogeyBQYWdpbmF0aW9uIH0sCiAgZGlyZWN0aXZlczogeyB3YXZlcyB9LAogIGZpbHRlcnM6IHsKICAgIHBhcnNlVGltZQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRhYmxlS2V5OiAwLAogICAgICBsaXN0OiBudWxsLAogICAgICB0b3RhbDogMCwKICAgICAgbGlzdExvYWRpbmc6IHRydWUsCiAgICAgIGxpc3RRdWVyeTogewogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHN0YXR1czogJycKICAgICAgfSwKICAgICAgdGVtcDogewogICAgICAgIHJvbGVfaWQ6IHVuZGVmaW5lZCwKICAgICAgICByb2xlX25hbWU6ICcnLAogICAgICAgIHJvbGVfY29kZTogJycsCiAgICAgICAgcm9sZV9kZXNjOiAnJywKICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLAogICAgICAgIHNvcnRfb3JkZXI6IDAKICAgICAgfSwKICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dTdGF0dXM6ICcnLAogICAgICB0ZXh0TWFwOiB7CiAgICAgICAgdXBkYXRlOiAn57yW6L6R6KeS6ImyJywKICAgICAgICBjcmVhdGU6ICfmt7vliqDop5LoibInCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgcm9sZV9uYW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+inkuiJsuWQjeensOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9XSwKICAgICAgICByb2xlX2NvZGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6KeS6Imy57yW56CB5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH1dCiAgICAgIH0sCiAgICAgIHBlcm1pc3Npb25EaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcGVybWlzc2lvbkxvYWRpbmc6IGZhbHNlLAogICAgICBwZXJtaXNzaW9uVHJlZTogW10sCiAgICAgIHBlcm1pc3Npb25Qcm9wczogewogICAgICAgIGxhYmVsOiAncGVybWlzc2lvbl9uYW1lJywKICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJwogICAgICB9LAogICAgICBjdXJyZW50Um9sZTogbnVsbAogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZQogICAgICBnZXRSb2xlTGlzdCh0aGlzLmxpc3RRdWVyeSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWwKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVGaWx0ZXIoKSB7CiAgICAgIHRoaXMubGlzdFF1ZXJ5LnBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgcmVzZXRUZW1wKCkgewogICAgICB0aGlzLnRlbXAgPSB7CiAgICAgICAgcm9sZV9pZDogdW5kZWZpbmVkLAogICAgICAgIHJvbGVfbmFtZTogJycsCiAgICAgICAgcm9sZV9jb2RlOiAnJywKICAgICAgICByb2xlX2Rlc2M6ICcnLAogICAgICAgIHN0YXR1czogJ2FjdGl2ZScsCiAgICAgICAgc29ydF9vcmRlcjogMAogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ3JlYXRlKCkgewogICAgICB0aGlzLnJlc2V0VGVtcCgpCiAgICAgIHRoaXMuZGlhbG9nU3RhdHVzID0gJ2NyZWF0ZScKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgY3JlYXRlRGF0YSgpIHsKICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGNyZWF0ZVJvbGUodGhpcy50ZW1wKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliJvlu7rmiJDlip8nLAogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnRlbXAgPSBPYmplY3QuYXNzaWduKHt9LCByb3cpCiAgICAgIHRoaXMuZGlhbG9nU3RhdHVzID0gJ3VwZGF0ZScKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnNbJ2RhdGFGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpCiAgICAgIH0pCiAgICB9LAogICAgdXBkYXRlRGF0YSgpIHsKICAgICAgdGhpcy4kcmVmc1snZGF0YUZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGNvbnN0IHRlbXBEYXRhID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50ZW1wKQogICAgICAgICAgdXBkYXRlUm9sZSh0ZW1wRGF0YS5yb2xlX2lkLCB0ZW1wRGF0YSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZQogICAgICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgICAgIHRpdGxlOiAn5oiQ5YqfJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5pu05paw5oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6K+l6KeS6Imy5ZCX77yfJywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGRlbGV0ZVJvbGUocm93LnJvbGVfaWQpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfJywKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVQZXJtaXNzaW9uKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRSb2xlID0gcm93CiAgICAgIHRoaXMucGVybWlzc2lvbkRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgIHRoaXMucGVybWlzc2lvbkxvYWRpbmcgPSB0cnVlCiAgICAgIAogICAgICAvLyDojrflj5bmnYPpmZDmoJEKICAgICAgZ2V0UGVybWlzc2lvblRyZWUoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnBlcm1pc3Npb25UcmVlID0gcmVzcG9uc2UuZGF0YQogICAgICAgIAogICAgICAgIC8vIOiOt+WPluinkuiJsuW3suacieadg+mZkAogICAgICAgIGdldFJvbGVQZXJtaXNzaW9ucyhyb3cucm9sZV9pZCkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICB0aGlzLiRyZWZzLnBlcm1pc3Npb25UcmVlLnNldENoZWNrZWRLZXlzKHJlcy5kYXRhLnBlcm1pc3Npb25zKQogICAgICAgICAgICB0aGlzLnBlcm1pc3Npb25Mb2FkaW5nID0gZmFsc2UKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICB1cGRhdGVQZXJtaXNzaW9ucygpIHsKICAgICAgY29uc3QgY2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLnBlcm1pc3Npb25UcmVlLmdldENoZWNrZWRLZXlzKCkKICAgICAgY29uc3QgaGFsZkNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5wZXJtaXNzaW9uVHJlZS5nZXRIYWxmQ2hlY2tlZEtleXMoKQogICAgICBjb25zdCBwZXJtaXNzaW9ucyA9IFsuLi5jaGVja2VkS2V5cywgLi4uaGFsZkNoZWNrZWRLZXlzXQogICAgICAKICAgICAgdXBkYXRlUm9sZVBlcm1pc3Npb25zKHRoaXMuY3VycmVudFJvbGUucm9sZV9pZCwgeyBwZXJtaXNzaW9ucyB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLnBlcm1pc3Npb25EaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgbWVzc2FnZTogJ+adg+mZkOiuvue9ruaIkOWKnycsCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICAgIH0pCiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["roles.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "roles.vue", "sourceRoot": "src/views/users", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索栏 -->\n    <div class=\"filter-container\">\n      <el-input\n        v-model=\"listQuery.keyword\"\n        placeholder=\"搜索角色名称、编码\"\n        style=\"width: 200px;\"\n        class=\"filter-item\"\n        @keyup.enter.native=\"handleFilter\"\n      />\n      <el-select\n        v-model=\"listQuery.status\"\n        placeholder=\"状态\"\n        clearable\n        style=\"width: 120px\"\n        class=\"filter-item\"\n      >\n        <el-option label=\"启用\" value=\"active\" />\n        <el-option label=\"禁用\" value=\"disabled\" />\n      </el-select>\n      <el-button\n        v-waves\n        class=\"filter-item\"\n        type=\"primary\"\n        icon=\"el-icon-search\"\n        @click=\"handleFilter\"\n      >\n        搜索\n      </el-button>\n      <el-button\n        class=\"filter-item\"\n        style=\"margin-left: 10px;\"\n        type=\"primary\"\n        icon=\"el-icon-plus\"\n        @click=\"handleCreate\"\n      >\n        添加角色\n      </el-button>\n    </div>\n\n    <!-- 表格 -->\n    <el-table\n      :key=\"tableKey\"\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      border\n      fit\n      highlight-current-row\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"ID\" prop=\"role_id\" align=\"center\" width=\"80\" />\n      <el-table-column label=\"角色名称\" prop=\"role_name\" width=\"150\" />\n      <el-table-column label=\"角色编码\" prop=\"role_code\" width=\"150\" />\n      <el-table-column label=\"角色描述\" prop=\"role_desc\" />\n      <el-table-column label=\"管理员数\" prop=\"admin_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"权限数\" prop=\"permission_count\" width=\"100\" align=\"center\" />\n      <el-table-column label=\"状态\" width=\"100\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          <el-tag :type=\"row.status === 'active' ? 'success' : 'info'\">\n            {{ row.status === 'active' ? '启用' : '禁用' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" prop=\"sort_order\" width=\"80\" align=\"center\" />\n      <el-table-column label=\"创建时间\" width=\"160\" align=\"center\">\n        <template slot-scope=\"{row}\">\n          {{ row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"250\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{row}\">\n          <el-button type=\"primary\" size=\"mini\" @click=\"handleUpdate(row)\">\n            编辑\n          </el-button>\n          <el-button type=\"success\" size=\"mini\" @click=\"handlePermission(row)\">\n            权限\n          </el-button>\n          <el-button\n            v-if=\"row.role_id !== 1 && row.role_id !== 2\"\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"listQuery.page\"\n      :limit.sync=\"listQuery.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加/编辑对话框 -->\n    <el-dialog :title=\"textMap[dialogStatus]\" :visible.sync=\"dialogFormVisible\">\n      <el-form\n        ref=\"dataForm\"\n        :rules=\"rules\"\n        :model=\"temp\"\n        label-position=\"left\"\n        label-width=\"100px\"\n        style=\"width: 400px; margin-left:50px;\"\n      >\n        <el-form-item label=\"角色名称\" prop=\"role_name\">\n          <el-input v-model=\"temp.role_name\" />\n        </el-form-item>\n        <el-form-item label=\"角色编码\" prop=\"role_code\">\n          <el-input v-model=\"temp.role_code\" />\n        </el-form-item>\n        <el-form-item label=\"角色描述\" prop=\"role_desc\">\n          <el-input v-model=\"temp.role_desc\" type=\"textarea\" :rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-select v-model=\"temp.status\" placeholder=\"请选择状态\">\n            <el-option label=\"启用\" value=\"active\" />\n            <el-option label=\"禁用\" value=\"disabled\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sort_order\">\n          <el-input-number v-model=\"temp.sort_order\" :min=\"0\" :max=\"999\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"dialogStatus==='create'?createData():updateData()\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 权限设置对话框 -->\n    <el-dialog title=\"权限设置\" :visible.sync=\"permissionDialogVisible\" width=\"600px\">\n      <div v-loading=\"permissionLoading\">\n        <el-tree\n          ref=\"permissionTree\"\n          :data=\"permissionTree\"\n          :props=\"permissionProps\"\n          show-checkbox\n          node-key=\"permission_id\"\n          default-expand-all\n        />\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"permissionDialogVisible = false\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"updatePermissions\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRoleList, createRole, updateRole, deleteRole, getRolePermissions, updateRolePermissions } from '@/api/users'\nimport { getPermissionTree } from '@/api/users'\nimport waves from '@/directive/waves'\nimport { parseTime } from '@/utils'\nimport Pagination from '@/components/Pagination'\n\nexport default {\n  name: 'RoleList',\n  components: { Pagination },\n  directives: { waves },\n  filters: {\n    parseTime\n  },\n  data() {\n    return {\n      tableKey: 0,\n      list: null,\n      total: 0,\n      listLoading: true,\n      listQuery: {\n        page: 1,\n        pageSize: 20,\n        keyword: '',\n        status: ''\n      },\n      temp: {\n        role_id: undefined,\n        role_name: '',\n        role_code: '',\n        role_desc: '',\n        status: 'active',\n        sort_order: 0\n      },\n      dialogFormVisible: false,\n      dialogStatus: '',\n      textMap: {\n        update: '编辑角色',\n        create: '添加角色'\n      },\n      rules: {\n        role_name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],\n        role_code: [{ required: true, message: '角色编码不能为空', trigger: 'blur' }]\n      },\n      permissionDialogVisible: false,\n      permissionLoading: false,\n      permissionTree: [],\n      permissionProps: {\n        label: 'permission_name',\n        children: 'children'\n      },\n      currentRole: null\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.listLoading = true\n      getRoleList(this.listQuery).then(response => {\n        this.list = response.data.list\n        this.total = response.data.total\n        this.listLoading = false\n      })\n    },\n    handleFilter() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    resetTemp() {\n      this.temp = {\n        role_id: undefined,\n        role_name: '',\n        role_code: '',\n        role_desc: '',\n        status: 'active',\n        sort_order: 0\n      }\n    },\n    handleCreate() {\n      this.resetTemp()\n      this.dialogStatus = 'create'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    createData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          createRole(this.temp).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '创建成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleUpdate(row) {\n      this.temp = Object.assign({}, row)\n      this.dialogStatus = 'update'\n      this.dialogFormVisible = true\n      this.$nextTick(() => {\n        this.$refs['dataForm'].clearValidate()\n      })\n    },\n    updateData() {\n      this.$refs['dataForm'].validate((valid) => {\n        if (valid) {\n          const tempData = Object.assign({}, this.temp)\n          updateRole(tempData.role_id, tempData).then(() => {\n            this.dialogFormVisible = false\n            this.$notify({\n              title: '成功',\n              message: '更新成功',\n              type: 'success',\n              duration: 2000\n            })\n            this.getList()\n          })\n        }\n      })\n    },\n    handleDelete(row) {\n      this.$confirm('确定要删除该角色吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteRole(row.role_id).then(() => {\n          this.$notify({\n            title: '成功',\n            message: '删除成功',\n            type: 'success',\n            duration: 2000\n          })\n          this.getList()\n        })\n      })\n    },\n    handlePermission(row) {\n      this.currentRole = row\n      this.permissionDialogVisible = true\n      this.permissionLoading = true\n      \n      // 获取权限树\n      getPermissionTree().then(response => {\n        this.permissionTree = response.data\n        \n        // 获取角色已有权限\n        getRolePermissions(row.role_id).then(res => {\n          this.$nextTick(() => {\n            this.$refs.permissionTree.setCheckedKeys(res.data.permissions)\n            this.permissionLoading = false\n          })\n        })\n      })\n    },\n    updatePermissions() {\n      const checkedKeys = this.$refs.permissionTree.getCheckedKeys()\n      const halfCheckedKeys = this.$refs.permissionTree.getHalfCheckedKeys()\n      const permissions = [...checkedKeys, ...halfCheckedKeys]\n      \n      updateRolePermissions(this.currentRole.role_id, { permissions }).then(() => {\n        this.permissionDialogVisible = false\n        this.$notify({\n          title: '成功',\n          message: '权限设置成功',\n          type: 'success',\n          duration: 2000\n        })\n        this.getList()\n      })\n    }\n  }\n}\n</script>\n"]}]}